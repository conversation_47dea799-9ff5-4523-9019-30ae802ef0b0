using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage.Dto
{
    /// <summary>
    /// 文件复制请求DTO
    /// </summary>
    public class FileCopyRequestDto
    {
        /// <summary>
        /// 源文件ID
        /// </summary>
        [Required]
        [StringLength(32)]
        public string SourceFileId { get; set; }

        /// <summary>
        /// 目标目录
        /// </summary>
        [StringLength(1000)]
        public string TargetDirectory { get; set; }

        /// <summary>
        /// 新文件名（可选，不指定则使用原文件名）
        /// </summary>
        [StringLength(255)]
        public string NewFileName { get; set; }

        /// <summary>
        /// 是否覆盖已存在的文件
        /// </summary>
        public bool Overwrite { get; set; } = false;

        /// <summary>
        /// 是否保持原文件的元数据
        /// </summary>
        public bool PreserveMetadata { get; set; } = true;
    }

    /// <summary>
    /// 文件复制响应DTO
    /// </summary>
    public class FileCopyResponseDto
    {
        /// <summary>
        /// 新文件ID
        /// </summary>
        public string NewFileId { get; set; }

        /// <summary>
        /// 新文件名
        /// </summary>
        public string NewFileName { get; set; }

        /// <summary>
        /// 新文件路径
        /// </summary>
        public string NewFilePath { get; set; }

        /// <summary>
        /// 复制是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 文件移动请求DTO
    /// </summary>
    public class FileMoveRequestDto
    {
        /// <summary>
        /// 源文件ID
        /// </summary>
        [Required]
        [StringLength(32)]
        public string SourceFileId { get; set; }

        /// <summary>
        /// 目标目录
        /// </summary>
        [StringLength(1000)]
        public string TargetDirectory { get; set; }

        /// <summary>
        /// 新文件名（可选，不指定则使用原文件名）
        /// </summary>
        [StringLength(255)]
        public string NewFileName { get; set; }

        /// <summary>
        /// 是否覆盖已存在的文件
        /// </summary>
        public bool Overwrite { get; set; } = false;
    }

    /// <summary>
    /// 文件移动响应DTO
    /// </summary>
    public class FileMoveResponseDto
    {
        /// <summary>
        /// 文件ID（保持不变）
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        /// 新文件名
        /// </summary>
        public string NewFileName { get; set; }

        /// <summary>
        /// 新文件路径
        /// </summary>
        public string NewFilePath { get; set; }

        /// <summary>
        /// 移动是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 文件删除请求DTO
    /// </summary>
    public class FileDeleteRequestDto
    {
        /// <summary>
        /// 是否物理删除（true=物理删除，false=逻辑删除）
        /// </summary>
        public bool PhysicalDelete { get; set; } = false;

        /// <summary>
        /// 删除原因
        /// </summary>
        [StringLength(500)]
        public string Reason { get; set; }

        /// <summary>
        /// 是否强制删除（忽略引用计数）
        /// </summary>
        public bool ForceDelete { get; set; } = false;
    }

    /// <summary>
    /// 文件删除响应DTO
    /// </summary>
    public class FileDeleteResponseDto
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        /// 删除是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 是否为物理删除
        /// </summary>
        public bool PhysicalDeleted { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime DeletedAt { get; set; }
    }

    /// <summary>
    /// 批量删除请求DTO
    /// </summary>
    public class FileBatchDeleteRequestDto
    {
        /// <summary>
        /// 文件ID列表
        /// </summary>
        [Required]
        public List<string> FileIds { get; set; }

        /// <summary>
        /// 是否物理删除
        /// </summary>
        public bool PhysicalDelete { get; set; } = false;

        /// <summary>
        /// 删除原因
        /// </summary>
        [StringLength(500)]
        public string Reason { get; set; }

        /// <summary>
        /// 是否强制删除
        /// </summary>
        public bool ForceDelete { get; set; } = false;
    }

    /// <summary>
    /// 批量删除响应DTO
    /// </summary>
    public class FileBatchDeleteResponseDto
    {
        /// <summary>
        /// 成功删除的文件ID列表
        /// </summary>
        public List<string> SuccessFileIds { get; set; }

        /// <summary>
        /// 删除失败的文件ID列表
        /// </summary>
        public List<string> FailedFileIds { get; set; }

        /// <summary>
        /// 失败原因映射
        /// </summary>
        public Dictionary<string, string> FailureReasons { get; set; }

        /// <summary>
        /// 总数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailedCount { get; set; }
    }

    /// <summary>
    /// 文件重命名请求DTO
    /// </summary>
    public class FileRenameRequestDto
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        [Required]
        [StringLength(32)]
        public string FileId { get; set; }

        /// <summary>
        /// 新文件名
        /// </summary>
        [Required]
        [StringLength(255)]
        public string NewFileName { get; set; }

        /// <summary>
        /// 是否覆盖已存在的文件
        /// </summary>
        public bool Overwrite { get; set; } = false;
    }

    /// <summary>
    /// 文件重命名响应DTO
    /// </summary>
    public class FileRenameResponseDto
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        /// 旧文件名
        /// </summary>
        public string OldFileName { get; set; }

        /// <summary>
        /// 新文件名
        /// </summary>
        public string NewFileName { get; set; }

        /// <summary>
        /// 新文件路径
        /// </summary>
        public string NewFilePath { get; set; }

        /// <summary>
        /// 重命名是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 文件验证响应DTO
    /// </summary>
    public class FileValidationResponseDto
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        /// 验证是否通过
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 当前MD5哈希值
        /// </summary>
        public string CurrentMD5 { get; set; }

        /// <summary>
        /// 存储的MD5哈希值
        /// </summary>
        public string StoredMD5 { get; set; }

        /// <summary>
        /// 当前文件大小
        /// </summary>
        public long CurrentFileSize { get; set; }

        /// <summary>
        /// 存储的文件大小
        /// </summary>
        public long StoredFileSize { get; set; }

        /// <summary>
        /// 验证时间
        /// </summary>
        public DateTime ValidationTime { get; set; }

        /// <summary>
        /// 验证消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 文件哈希响应DTO
    /// </summary>
    public class FileHashResponseDto
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        /// MD5哈希值
        /// </summary>
        public string MD5Hash { get; set; }

        /// <summary>
        /// SHA256哈希值
        /// </summary>
        public string SHA256Hash { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 计算时间
        /// </summary>
        public DateTime CalculatedAt { get; set; }

        /// <summary>
        /// 计算耗时（毫秒）
        /// </summary>
        public long CalculationTimeMs { get; set; }
    }
}
