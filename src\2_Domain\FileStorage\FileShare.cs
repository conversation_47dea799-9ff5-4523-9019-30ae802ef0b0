using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件分享实体
    /// 用于管理文件的短链分享功能，支持权限控制和访问统计
    /// </summary>
    public class FileShare : FullAuditedAggregateRoot<Guid>, IMultiTenant
    {
        /// <summary>
        /// 租户ID
        /// </summary>
        public virtual Guid? TenantId { get; set; }

        /// <summary>
        /// 关联的文件信息ID
        /// </summary>
        public virtual Guid FileInfoId { get; set; }

        /// <summary>
        /// 分享码（短链标识符）
        /// 格式：8-12位字符串，包含字母和数字
        /// </summary>
        [Required]
        [StringLength(12)]
        public virtual string ShareCode { get; set; }

        /// <summary>
        /// 分享标题
        /// </summary>
        [StringLength(200)]
        public virtual string Title { get; set; }

        /// <summary>
        /// 分享描述
        /// </summary>
        [StringLength(500)]
        public virtual string Description { get; set; }

        /// <summary>
        /// 访问密码（可选）
        /// </summary>
        [StringLength(50)]
        public virtual string AccessPassword { get; set; }

        /// <summary>
        /// 分享类型
        /// </summary>
        public virtual ShareType ShareType { get; set; }

        /// <summary>
        /// 分享状态
        /// </summary>
        public virtual ShareStatus Status { get; set; }

        /// <summary>
        /// 过期时间（可选，null表示永不过期）
        /// </summary>
        public virtual DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 最大访问次数（可选，null表示无限制）
        /// </summary>
        public virtual int? MaxAccessCount { get; set; }

        /// <summary>
        /// 当前访问次数
        /// </summary>
        public virtual int AccessCount { get; set; }

        /// <summary>
        /// 最大下载次数（可选，null表示无限制）
        /// </summary>
        public virtual int? MaxDownloadCount { get; set; }

        /// <summary>
        /// 当前下载次数
        /// </summary>
        public virtual int DownloadCount { get; set; }

        /// <summary>
        /// 最后访问时间
        /// </summary>
        public virtual DateTime? LastAccessTime { get; set; }

        /// <summary>
        /// 最后访问IP地址
        /// </summary>
        [StringLength(45)]
        public virtual string LastAccessIP { get; set; }

        /// <summary>
        /// 最后访问用户代理
        /// </summary>
        [StringLength(500)]
        public virtual string LastAccessUserAgent { get; set; }

        /// <summary>
        /// 允许的IP地址列表（JSON格式，可选）
        /// </summary>
        [StringLength(1000)]
        public virtual string AllowedIPs { get; set; }

        /// <summary>
        /// 是否允许下载
        /// </summary>
        public virtual bool AllowDownload { get; set; }

        /// <summary>
        /// 是否允许预览
        /// </summary>
        public virtual bool AllowPreview { get; set; }

        /// <summary>
        /// 是否需要登录访问
        /// </summary>
        public virtual bool RequireLogin { get; set; }

        /// <summary>
        /// 扩展属性（JSON格式）
        /// </summary>
        [StringLength(1000)]
        public new virtual string ExtraProperties { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(500)]
        public virtual string Remarks { get; set; }

        /// <summary>
        /// 导航属性：关联的文件信息
        /// </summary>
        public virtual FileInfo FileInfo { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        protected FileShare()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="id">实体ID</param>
        /// <param name="fileInfoId">文件信息ID</param>
        /// <param name="shareCode">分享码</param>
        /// <param name="shareType">分享类型</param>
        /// <param name="tenantId">租户ID</param>
        public FileShare(
            Guid id,
            Guid fileInfoId,
            string shareCode,
            ShareType shareType,
            Guid? tenantId = null)
        {
            Id = id;
            FileInfoId = fileInfoId;
            ShareCode = shareCode;
            ShareType = shareType;
            TenantId = tenantId;
            Status = ShareStatus.Active;
            AccessCount = 0;
            DownloadCount = 0;
            AllowDownload = true;
            AllowPreview = true;
            RequireLogin = false;
        }

        /// <summary>
        /// 记录访问
        /// </summary>
        /// <param name="ipAddress">访问IP地址</param>
        /// <param name="userAgent">用户代理</param>
        public virtual void RecordAccess(string ipAddress = null, string userAgent = null)
        {
            AccessCount++;
            LastAccessTime = DateTime.UtcNow;
            LastAccessIP = ipAddress;
            LastAccessUserAgent = userAgent;
        }

        /// <summary>
        /// 记录下载
        /// </summary>
        public virtual void RecordDownload()
        {
            DownloadCount++;
        }

        /// <summary>
        /// 检查是否可以访问
        /// </summary>
        /// <returns></returns>
        public virtual bool CanAccess()
        {
            if (Status != ShareStatus.Active)
                return false;

            if (IsExpired())
                return false;

            if (MaxAccessCount.HasValue && AccessCount >= MaxAccessCount.Value)
                return false;

            return true;
        }

        /// <summary>
        /// 检查是否可以下载
        /// </summary>
        /// <returns></returns>
        public virtual bool CanDownload()
        {
            if (!CanAccess())
                return false;

            if (!AllowDownload)
                return false;

            if (MaxDownloadCount.HasValue && DownloadCount >= MaxDownloadCount.Value)
                return false;

            return true;
        }

        /// <summary>
        /// 检查是否过期
        /// </summary>
        /// <returns></returns>
        public virtual bool IsExpired()
        {
            return ExpirationTime.HasValue && ExpirationTime.Value <= DateTime.UtcNow;
        }

        /// <summary>
        /// 验证访问密码
        /// </summary>
        /// <param name="password">输入的密码</param>
        /// <returns></returns>
        public virtual bool ValidatePassword(string password)
        {
            if (string.IsNullOrEmpty(AccessPassword))
                return true; // 无密码保护

            return AccessPassword == password;
        }

        /// <summary>
        /// 禁用分享
        /// </summary>
        public virtual void Disable()
        {
            Status = ShareStatus.Disabled;
        }

        /// <summary>
        /// 启用分享
        /// </summary>
        public virtual void Enable()
        {
            Status = ShareStatus.Active;
        }

        /// <summary>
        /// 设置过期时间
        /// </summary>
        /// <param name="expirationTime">过期时间</param>
        public virtual void SetExpirationTime(DateTime? expirationTime)
        {
            ExpirationTime = expirationTime;
        }

        /// <summary>
        /// 设置访问限制
        /// </summary>
        /// <param name="maxAccessCount">最大访问次数</param>
        /// <param name="maxDownloadCount">最大下载次数</param>
        public virtual void SetAccessLimits(int? maxAccessCount, int? maxDownloadCount)
        {
            MaxAccessCount = maxAccessCount;
            MaxDownloadCount = maxDownloadCount;
        }
    }


}
