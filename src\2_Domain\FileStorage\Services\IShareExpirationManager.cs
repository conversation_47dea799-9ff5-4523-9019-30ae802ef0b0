using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage.Services
{
    /// <summary>
    /// 分享过期管理服务接口
    /// 提供分享链接的过期检查、自动清理和通知功能
    /// </summary>
    public interface IShareExpirationManager
    {
        /// <summary>
        /// 检查并处理过期的分享
        /// </summary>
        /// <param name="batchSize">批处理大小</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理结果</returns>
        Task<ShareExpirationProcessResult> ProcessExpiredSharesAsync(int batchSize = 100, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取即将过期的分享列表
        /// </summary>
        /// <param name="warningDays">提前警告天数</param>
        /// <param name="maxCount">最大数量</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>即将过期的分享列表</returns>
        Task<List<FileShare>> GetExpiringSharesAsync(int warningDays = 7, int maxCount = 1000, CancellationToken cancellationToken = default);

        /// <summary>
        /// 发送过期通知
        /// </summary>
        /// <param name="shares">分享列表</param>
        /// <param name="notificationType">通知类型</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task SendExpirationNotificationsAsync(List<FileShare> shares, ShareExpirationNotificationType notificationType, CancellationToken cancellationToken = default);

        /// <summary>
        /// 延长分享有效期
        /// </summary>
        /// <param name="shareId">分享ID</param>
        /// <param name="extensionDays">延长天数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<bool> ExtendShareExpirationAsync(Guid shareId, int extensionDays, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量延长分享有效期
        /// </summary>
        /// <param name="shareIds">分享ID列表</param>
        /// <param name="extensionDays">延长天数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理结果</returns>
        Task<ShareExpirationExtensionResult> BatchExtendShareExpirationAsync(List<Guid> shareIds, int extensionDays, CancellationToken cancellationToken = default);

        /// <summary>
        /// 设置分享自动延期规则
        /// </summary>
        /// <param name="shareId">分享ID</param>
        /// <param name="rule">自动延期规则</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<bool> SetAutoExtensionRuleAsync(Guid shareId, ShareAutoExtensionRule rule, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取分享过期统计信息
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>统计信息</returns>
        Task<ShareExpirationStatistics> GetExpirationStatisticsAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 分享过期处理结果
    /// </summary>
    public class ShareExpirationProcessResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 处理开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 处理结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 处理耗时（毫秒）
        /// </summary>
        public long DurationMs => (long)(EndTime - StartTime).TotalMilliseconds;

        /// <summary>
        /// 扫描的分享总数
        /// </summary>
        public int ScannedCount { get; set; }

        /// <summary>
        /// 过期的分享数量
        /// </summary>
        public int ExpiredCount { get; set; }

        /// <summary>
        /// 已处理的分享数量
        /// </summary>
        public int ProcessedCount { get; set; }

        /// <summary>
        /// 处理失败的分享数量
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 发送通知的数量
        /// </summary>
        public int NotificationsSent { get; set; }

        /// <summary>
        /// 详细日志
        /// </summary>
        public List<string> ProcessingLog { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ShareExpirationProcessResult()
        {
            StartTime = DateTime.UtcNow;
            ProcessingLog = new List<string>();
        }

        /// <summary>
        /// 完成处理
        /// </summary>
        public void Complete()
        {
            EndTime = DateTime.UtcNow;
        }

        /// <summary>
        /// 添加日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void AddLog(string message)
        {
            ProcessingLog.Add($"[{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}] {message}");
        }
    }

    /// <summary>
    /// 分享过期通知类型
    /// </summary>
    public enum ShareExpirationNotificationType
    {
        /// <summary>
        /// 即将过期警告
        /// </summary>
        ExpirationWarning = 0,

        /// <summary>
        /// 已过期通知
        /// </summary>
        ExpiredNotification = 1,

        /// <summary>
        /// 自动延期通知
        /// </summary>
        AutoExtensionNotification = 2,

        /// <summary>
        /// 清理通知
        /// </summary>
        CleanupNotification = 3
    }

    /// <summary>
    /// 分享延期结果
    /// </summary>
    public class ShareExpirationExtensionResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 成功延期的分享数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 延期失败的分享数量
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 延期失败的分享详情
        /// </summary>
        public List<ShareExtensionFailure> Failures { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ShareExpirationExtensionResult()
        {
            Failures = new List<ShareExtensionFailure>();
        }
    }

    /// <summary>
    /// 分享延期失败详情
    /// </summary>
    public class ShareExtensionFailure
    {
        /// <summary>
        /// 分享ID
        /// </summary>
        public Guid ShareId { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; set; }
    }

    /// <summary>
    /// 分享自动延期规则
    /// </summary>
    public class ShareAutoExtensionRule
    {
        /// <summary>
        /// 是否启用自动延期
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 延期天数
        /// </summary>
        public int ExtensionDays { get; set; }

        /// <summary>
        /// 最大延期次数（0表示无限制）
        /// </summary>
        public int MaxExtensions { get; set; }

        /// <summary>
        /// 当前延期次数
        /// </summary>
        public int CurrentExtensions { get; set; }

        /// <summary>
        /// 延期条件
        /// </summary>
        public ShareAutoExtensionCondition Condition { get; set; }

        /// <summary>
        /// 最小访问次数（条件为访问次数时使用）
        /// </summary>
        public int? MinAccessCount { get; set; }

        /// <summary>
        /// 最小下载次数（条件为下载次数时使用）
        /// </summary>
        public int? MinDownloadCount { get; set; }

        /// <summary>
        /// 检查周期（天）
        /// </summary>
        public int CheckIntervalDays { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ShareAutoExtensionRule()
        {
            ExtensionDays = 30;
            CheckIntervalDays = 1;
            Condition = ShareAutoExtensionCondition.Always;
        }
    }

    /// <summary>
    /// 分享自动延期条件
    /// </summary>
    public enum ShareAutoExtensionCondition
    {
        /// <summary>
        /// 总是延期
        /// </summary>
        Always = 0,

        /// <summary>
        /// 有访问时延期
        /// </summary>
        HasAccess = 1,

        /// <summary>
        /// 有下载时延期
        /// </summary>
        HasDownload = 2,

        /// <summary>
        /// 访问次数达到阈值时延期
        /// </summary>
        AccessThreshold = 3,

        /// <summary>
        /// 下载次数达到阈值时延期
        /// </summary>
        DownloadThreshold = 4,

        /// <summary>
        /// 从不自动延期
        /// </summary>
        Never = 5
    }

    /// <summary>
    /// 分享过期统计信息
    /// </summary>
    public class ShareExpirationStatistics
    {
        /// <summary>
        /// 总分享数量
        /// </summary>
        public int TotalShares { get; set; }

        /// <summary>
        /// 活跃分享数量
        /// </summary>
        public int ActiveShares { get; set; }

        /// <summary>
        /// 已过期分享数量
        /// </summary>
        public int ExpiredShares { get; set; }

        /// <summary>
        /// 即将过期分享数量（7天内）
        /// </summary>
        public int ExpiringShares { get; set; }

        /// <summary>
        /// 永不过期分享数量
        /// </summary>
        public int NeverExpireShares { get; set; }

        /// <summary>
        /// 启用自动延期的分享数量
        /// </summary>
        public int AutoExtensionShares { get; set; }

        /// <summary>
        /// 平均剩余天数
        /// </summary>
        public double AverageRemainingDays { get; set; }

        /// <summary>
        /// 统计时间
        /// </summary>
        public DateTime StatisticsTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ShareExpirationStatistics()
        {
            StatisticsTime = DateTime.UtcNow;
        }
    }
}
