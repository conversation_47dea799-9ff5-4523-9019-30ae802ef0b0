# 文件存储模型类重构说明

## 重构概述

将原来的 `StorageModels.cs` 和 `StorageModels2.cs` 两个大文件拆分成了多个单独的文件，每个类一个文件。这样做的好处包括：

1. **更好的可维护性** - 每个类有独立的文件，便于查找和修改
2. **更清晰的代码组织** - 符合单一职责原则
3. **更好的版本控制** - 修改单个类不会影响其他类的版本历史
4. **更好的团队协作** - 减少合并冲突
5. **符合.NET命名约定** - 一个文件一个类是标准做法

## 文件结构

### 文件上传相关
- `FileUploadRequest.cs` - 文件上传请求
- `FileUploadResult.cs` - 文件上传结果

### 分片上传相关
- `ChunkedUploadRequest.cs` - 分片上传请求
- `ChunkedUploadResult.cs` - 分片上传结果
- `CompleteChunkedUploadRequest.cs` - 完成分片上传请求
- `ChunkInfo.cs` - 分片信息

### 文件下载相关
- `FileDownloadRequest.cs` - 文件下载请求

### 文件管理相关
- `FileDeleteRequest.cs` - 文件删除请求
- `BatchDeleteRequest.cs` - 批量删除请求
- `BatchDeleteResult.cs` - 批量删除结果
- `DeleteError.cs` - 删除错误信息
- `FileCopyRequest.cs` - 文件复制请求
- `FileCopyResult.cs` - 文件复制结果
- `FileMoveRequest.cs` - 文件移动请求
- `FileMoveResult.cs` - 文件移动结果

### 文件元数据相关
- `FileMetadata.cs` - 文件元数据

### 预签名URL相关
- `PresignedUrlRequest.cs` - 预签名URL请求
- `PresignedUrlOperation.cs` - 预签名URL操作类型枚举

### 文件列表相关
- `FileListRequest.cs` - 文件列表请求
- `FileListResult.cs` - 文件列表结果

### 存储统计相关
- `StorageStatistics.cs` - 存储统计信息
- `StorageClassStatistics.cs` - 存储类别统计

### 健康检查相关
- `HealthCheckResult.cs` - 健康检查结果

### 存储配置相关
- `StorageConfiguration.cs` - 存储配置基类
- `S3StorageConfiguration.cs` - S3存储配置

### 验证相关
- `ValidationResult.cs` - 验证结果

## 命名空间

所有类都在 `TSZ.ServiceBase.FileStorageCenter.FileStorage` 命名空间下，保持了与原来的一致性。

## 使用说明

重构后，所有的类引用保持不变，只是文件组织结构更加清晰。如果需要使用某个特定的模型类，可以直接通过类名查找对应的文件。

## 注意事项

1. 所有原有的功能和接口保持不变
2. 命名空间保持一致
3. 类的定义和属性完全相同
4. 只是文件组织结构发生了变化
