(function (factory) {
  if (typeof define === 'function' && define.amd) {
    define(['jquery'], factory);
  } else if (typeof module === 'object' && typeof module.exports === 'object') {
    factory(require('jquery'));
  } else {
    factory(jQuery);
  }
}(function (jQuery) {
  // Hungarian
  jQuery.timeago.settings.strings = {
    prefixAgo: null,
    prefixFromNow: null,
    suffixAgo: null,
    suffixFromNow: null,
    seconds: "kevesebb mint egy perce",
    minute: "kör<PERSON><PERSON>belül egy perce",
    minutes: "%d perce",
    hour: "körülbelül egy órája",
    hours: "körülbelül %d órája",
    day: "körülbelül egy napja",
    days: "%d napja",
    month: "körülbelül egy hónapja",
    months: "%d hónapja",
    year: "kör<PERSON><PERSON>bel<PERSON>l egy éve",
    years: "%d éve"
  };
}));
