﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<RootNamespace>TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.DbMigrations</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" >
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
			<PrivateAssets>compile; contentFiles; build; buildMultitargeting; buildTransitive; analyzers; native</PrivateAssets>
		</PackageReference>
		<PackageReference Include="TSZ.Abp.Modulies"  />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\4_EntityFrameworkCore\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.csproj" />
	</ItemGroup>

</Project>
