namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 存储统计结果
    /// </summary>
    public class StorageStatisticsResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 总文件数量
        /// </summary>
        public long TotalFiles { get; set; }

        /// <summary>
        /// 总存储大小（字节）
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// 已使用存储大小（字节）
        /// </summary>
        public long UsedSize { get; set; }

        /// <summary>
        /// 已使用存储空间（别名，保持兼容性）
        /// </summary>
        public long UsedSpace
        {
            get => UsedSize;
            set => UsedSize = value;
        }

        /// <summary>
        /// 可用存储大小（字节）
        /// </summary>
        public long AvailableSize { get; set; }

        /// <summary>
        /// 可用存储空间（别名，保持兼容性）
        /// </summary>
        public long AvailableSpace
        {
            get => AvailableSize;
            set => AvailableSize = value;
        }

        /// <summary>
        /// 存储使用率（百分比）
        /// </summary>
        public double UsagePercentage { get; set; }
    }
}
