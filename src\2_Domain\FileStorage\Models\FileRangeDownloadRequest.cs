namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件范围下载请求
    /// </summary>
    public class FileRangeDownloadRequest
    {
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 版本ID
        /// </summary>
        public string VersionId { get; set; }

        /// <summary>
        /// 范围开始位置
        /// </summary>
        public long RangeStart { get; set; }

        /// <summary>
        /// 范围结束位置
        /// </summary>
        public long RangeEnd { get; set; }

        /// <summary>
        /// 是否解密
        /// </summary>
        public bool Decrypt { get; set; }

        /// <summary>
        /// 解密密钥
        /// </summary>
        public string DecryptionKey { get; set; }
    }
}
