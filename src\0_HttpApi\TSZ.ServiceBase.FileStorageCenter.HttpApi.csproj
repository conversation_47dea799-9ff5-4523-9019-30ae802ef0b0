<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <RootNamespace>TSZ.ServiceBase.FileStorageCenter</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Account.HttpApi"  />
    <PackageReference Include="Volo.Abp.Identity.HttpApi"  />
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi"  />
    <PackageReference Include="Volo.Abp.TenantManagement.HttpApi"  />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi"  />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi"  />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\1_Application.Contracts\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj" />
  </ItemGroup>

</Project>
