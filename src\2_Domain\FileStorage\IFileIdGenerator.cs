using System.Threading.Tasks;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件ID生成器接口
    /// 提供文件ID和分享码的生成策略
    /// </summary>
    public interface IFileIdGenerator
    {
        /// <summary>
        /// 生成文件ID
        /// 基于文件内容和路径生成唯一的32位十六进制字符串
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="fileContent">文件内容（可选，用于基于内容生成ID）</param>
        /// <param name="md5Hash">文件MD5哈希值（可选，如果已计算）</param>
        /// <returns>32位十六进制文件ID</returns>
        Task<string> GenerateFileIdAsync(string filePath, byte[] fileContent = null, string md5Hash = null);

        /// <summary>
        /// 生成分享码
        /// 生成8-12位的短链分享码，包含字母和数字
        /// </summary>
        /// <param name="length">分享码长度（默认8位）</param>
        /// <returns>分享码</returns>
        Task<string> GenerateShareCodeAsync(int length = 8);

        /// <summary>
        /// 生成高级分享码（带前缀和校验）
        /// </summary>
        /// <param name="prefix">前缀（可选）</param>
        /// <param name="length">长度</param>
        /// <param name="includeChecksum">是否包含校验码</param>
        /// <returns></returns>
        Task<string> GenerateAdvancedShareCodeAsync(string prefix = null, int length = 8, bool includeChecksum = false);

        /// <summary>
        /// 生成基于时间戳的分享码（确保唯一性）
        /// </summary>
        /// <param name="length">长度</param>
        /// <returns></returns>
        Task<string> GenerateTimestampBasedShareCodeAsync(int length = 8);

        /// <summary>
        /// 验证文件ID格式
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>是否为有效格式</returns>
        bool ValidateFileId(string fileId);

        /// <summary>
        /// 验证分享码格式
        /// </summary>
        /// <param name="shareCode">分享码</param>
        /// <returns>是否为有效格式</returns>
        bool ValidateShareCode(string shareCode);

        /// <summary>
        /// 基于文件内容生成MD5哈希值
        /// </summary>
        /// <param name="fileContent">文件内容</param>
        /// <returns>MD5哈希值</returns>
        string GenerateMD5Hash(byte[] fileContent);

        /// <summary>
        /// 基于文件内容生成SHA256哈希值
        /// </summary>
        /// <param name="fileContent">文件内容</param>
        /// <returns>SHA256哈希值</returns>
        string GenerateSHA256Hash(byte[] fileContent);

        /// <summary>
        /// 基于文件路径生成MD5哈希值
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>MD5哈希值</returns>
        Task<string> GenerateMD5HashFromFileAsync(string filePath);

        /// <summary>
        /// 基于文件路径生成SHA256哈希值
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>SHA256哈希值</returns>
        Task<string> GenerateSHA256HashFromFileAsync(string filePath);
    }
}
