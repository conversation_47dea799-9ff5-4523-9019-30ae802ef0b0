using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TSZ.ServiceBase.FileStorageCenter.FileStorage;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage.Providers
{
    /// <summary>
    /// 文件存储提供者工厂实现
    /// 负责创建和管理不同类型的存储提供者实例
    /// </summary>
    public class FileStorageProviderFactory : IFileStorageProviderFactory
    {
        private readonly Dictionary<StorageType, Func<string, Task<IFileStorageProvider>>> _providerFactories;

        public FileStorageProviderFactory()
        {
            _providerFactories = [];
        }

        /// <summary>
        /// 创建存储提供者
        /// </summary>
        public async Task<IFileStorageProvider> CreateProviderAsync(StorageType storageType, string configurationName = null)
        {
            if (!_providerFactories.TryGetValue(storageType, out var factory))
            {
                throw new NotSupportedException($"不支持的存储类型: {storageType}");
            }

            return await factory(configurationName);
        }

        /// <summary>
        /// 获取默认存储提供者
        /// </summary>
        public async Task<IFileStorageProvider> GetDefaultProviderAsync()
        {
            // 默认使用本地存储
            return await CreateProviderAsync(StorageType.Local);
        }

        /// <summary>
        /// 获取指定配置的存储提供者
        /// </summary>
        public async Task<IFileStorageProvider> GetProviderAsync(string configurationName)
        {
            // 简化实现，根据配置名称推断存储类型
            var storageType = configurationName?.ToLowerInvariant() switch
            {
                "s3" => StorageType.S3,
                "minio" => StorageType.MinIO,
                "huaweiobs" => StorageType.HuaweiOBS,
                "tencentcos" => StorageType.TencentCOS,
                _ => StorageType.Local
            };

            return await CreateProviderAsync(storageType, configurationName);
        }

        /// <summary>
        /// 获取所有可用的存储提供者
        /// </summary>
        public async Task<IEnumerable<IFileStorageProvider>> GetAllProvidersAsync()
        {
            var providers = new List<IFileStorageProvider>();

            foreach (var storageType in _providerFactories.Keys)
            {
                try
                {
                    var provider = await CreateProviderAsync(storageType);
                    providers.Add(provider);
                }
                catch
                {
                    // 忽略创建失败的提供者
                }
            }

            return providers;
        }

        /// <summary>
        /// 获取支持的存储类型
        /// </summary>
        public IEnumerable<StorageType> GetSupportedStorageTypes()
        {
            return _providerFactories.Keys;
        }

        /// <summary>
        /// 检查存储类型是否受支持
        /// </summary>
        public bool IsStorageTypeSupported(StorageType storageType)
        {
            return _providerFactories.ContainsKey(storageType);
        }

        /// <summary>
        /// 注册存储提供者
        /// </summary>
        public void RegisterProvider(StorageType storageType, Func<string, Task<IFileStorageProvider>> providerFactory)
        {
            _providerFactories[storageType] = providerFactory;
        }

        /// <summary>
        /// 注销存储提供者
        /// </summary>
        public void UnregisterProvider(StorageType storageType)
        {
            _providerFactories.Remove(storageType);
        }

        /// <summary>
        /// 获取存储配置
        /// </summary>
        public async Task<StorageConfiguration> GetStorageConfigurationAsync(string configurationName)
        {
            // 简化实现，返回基本配置
            return await Task.FromResult(new StorageConfiguration
            {
                Name = configurationName,
                StorageType = StorageType.Local,
                IsEnabled = true
            });
        }

        /// <summary>
        /// 获取所有存储配置
        /// </summary>
        public async Task<IEnumerable<StorageConfiguration>> GetAllStorageConfigurationsAsync()
        {
            // 简化实现，返回默认配置
            var configurations = new List<StorageConfiguration>
            {
                new() { Name = "Local", StorageType = StorageType.Local, IsEnabled = true }
            };

            return await Task.FromResult(configurations);
        }

        /// <summary>
        /// 验证存储配置
        /// </summary>
        public async Task<ValidationResult> ValidateConfigurationAsync(StorageConfiguration configuration)
        {
            var result = new ValidationResult();

            if (string.IsNullOrEmpty(configuration.Name))
            {
                result.AddError("配置名称不能为空");
            }

            if (!IsStorageTypeSupported(configuration.StorageType))
            {
                result.AddError($"不支持的存储类型: {configuration.StorageType}");
            }

            return await Task.FromResult(result);
        }

        /// <summary>
        /// 测试存储连接
        /// </summary>
        public async Task<HealthCheckResult> TestConnectionAsync(StorageConfiguration configuration)
        {
            try
            {
                var provider = await CreateProviderAsync(configuration.StorageType, configuration.Name);
                return await provider.HealthCheckAsync();
            }
            catch (Exception ex)
            {
                return new HealthCheckResult
                {
                    IsHealthy = false,
                    StatusMessage = "连接测试失败",
                    ErrorMessage = ex.Message
                };
            }
        }
    }
}
