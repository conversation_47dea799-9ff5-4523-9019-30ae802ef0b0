{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"TSZ.ServiceBase.FileStorageCenter.Domain.Shared/1.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "1.0.0", "Volo.Abp.AuditLogging.Domain.Shared": "0.4.1", "Volo.Abp.BackgroundJobs.Domain.Shared": "0.4.1", "Volo.Abp.FeatureManagement.Domain.Shared": "0.15.0", "Volo.Abp.Identity.Domain.Shared": "0.3.0", "Volo.Abp.IdentityServer.Domain.Shared": "0.6.0", "Volo.Abp.Localization": "0.3.0", "Volo.Abp.ObjectExtending": "2.4.0", "Volo.Abp.PermissionManagement.Domain.Shared": "0.3.0", "Volo.Abp.SettingManagement.Domain.Shared": "0.3.0", "Volo.Abp.TenantManagement.Domain.Shared": "0.3.0", "Volo.Abp.Validation": "0.3.0"}, "runtime": {"TSZ.ServiceBase.FileStorageCenter.Domain.Shared.dll": {}}}, "ConfigureAwait.Fody/3.3.1": {"dependencies": {"Fody": "6.0.2"}, "runtime": {"lib/netstandard2.0/ConfigureAwait.dll": {"assemblyVersion": "3.3.1.0", "fileVersion": "3.3.1.0"}}}, "Fody/6.0.2": {}, "JetBrains.Annotations/2019.1.3": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "2019.1.3.0", "fileVersion": "2019.1.3.0"}}}, "Microsoft.Extensions.Configuration/3.1.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.2": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Configuration.Binder/3.1.2": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Configuration.CommandLine/3.1.2": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/3.1.2": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Configuration.FileExtensions/3.1.2": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.2", "Microsoft.Extensions.FileProviders.Physical": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Configuration.Json/3.1.2": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.2", "Microsoft.Extensions.Configuration.FileExtensions": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Configuration.UserSecrets/3.1.2": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.DependencyInjection/3.1.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.2": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.2": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.FileProviders.Composite/2.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "3.1.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.18136"}}}, "Microsoft.Extensions.FileProviders.Embedded/1.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "3.1.2", "System.Runtime.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.20622"}}}, "Microsoft.Extensions.FileProviders.Physical/3.1.2": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "3.1.2", "Microsoft.Extensions.FileSystemGlobbing": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.FileSystemGlobbing/3.1.2": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Hosting.Abstractions/3.1.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.2", "Microsoft.Extensions.FileProviders.Abstractions": "3.1.2", "Microsoft.Extensions.Logging.Abstractions": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Localization/3.1.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.2", "Microsoft.Extensions.Localization.Abstractions": "3.1.2", "Microsoft.Extensions.Logging.Abstractions": "3.1.2", "Microsoft.Extensions.Options": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Localization.Abstractions/3.1.2": {"runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Logging/3.1.2": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "3.1.2", "Microsoft.Extensions.DependencyInjection": "3.1.2", "Microsoft.Extensions.Logging.Abstractions": "3.1.2", "Microsoft.Extensions.Options": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Logging.Abstractions/3.1.2": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Options/3.1.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.2", "Microsoft.Extensions.Primitives": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/3.1.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.2", "Microsoft.Extensions.Configuration.Binder": "3.1.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.2", "Microsoft.Extensions.Options": "3.1.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.Extensions.Primitives/3.1.2": {"runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.220.6706"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Newtonsoft.Json/11.0.2": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.0.2.21924"}}}, "Nito.AsyncEx.Context/5.0.0": {"dependencies": {"Nito.AsyncEx.Tasks": "5.0.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Nito.AsyncEx.Coordination/5.0.0": {"dependencies": {"Nito.AsyncEx.Tasks": "5.0.0", "Nito.Collections.Deque": "1.0.4", "Nito.Disposables": "2.0.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Nito.AsyncEx.Tasks/5.0.0": {"dependencies": {"Nito.Disposables": "2.0.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Nito.Collections.Deque/1.0.4": {"runtime": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"assemblyVersion": "1.0.4.0", "fileVersion": "1.0.4.0"}}}, "Nito.Disposables/2.0.0": {"dependencies": {"System.Collections.Immutable": "1.7.0"}, "runtime": {"lib/netstandard2.0/Nito.Disposables.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Immutable/1.7.0": {}, "System.ComponentModel.Annotations/4.7.0": {}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.0.19": {"runtime": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "1.0.19.0", "fileVersion": "1.0.19.0"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Volo.Abp.AuditLogging.Domain.Shared/0.4.1": {"dependencies": {"Volo.Abp.Core": "2.4.0", "Volo.Abp.Localization": "0.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.BackgroundJobs.Domain.Shared/0.4.1": {"dependencies": {"Volo.Abp.Core": "2.4.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Core/2.4.0": {"dependencies": {"ConfigureAwait.Fody": "3.3.1", "JetBrains.Annotations": "2019.1.3", "Microsoft.Extensions.Configuration.CommandLine": "3.1.2", "Microsoft.Extensions.Configuration.EnvironmentVariables": "3.1.2", "Microsoft.Extensions.Configuration.UserSecrets": "3.1.2", "Microsoft.Extensions.DependencyInjection": "3.1.2", "Microsoft.Extensions.Hosting.Abstractions": "3.1.2", "Microsoft.Extensions.Localization": "3.1.2", "Microsoft.Extensions.Logging": "3.1.2", "Microsoft.Extensions.Options": "3.1.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "3.1.2", "Nito.AsyncEx.Context": "5.0.0", "Nito.AsyncEx.Coordination": "5.0.0", "System.Collections.Immutable": "1.7.0", "System.ComponentModel.Annotations": "4.7.0", "System.Linq.Dynamic.Core": "1.0.19", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.FeatureManagement.Domain.Shared/0.15.0": {"dependencies": {"Volo.Abp.Localization": "0.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.Shared.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Volo.Abp.Identity.Domain.Shared/0.3.0": {"dependencies": {"Volo.Abp.Localization": "0.3.0", "Volo.Abp.Users.Domain.Shared": "0.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.dll": {"assemblyVersion": "0.3.0.0", "fileVersion": "0.3.0.0"}}}, "Volo.Abp.IdentityServer.Domain.Shared/0.6.0": {"dependencies": {"Volo.Abp.Core": "2.4.0", "Volo.Abp.Localization": "0.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.Shared.dll": {"assemblyVersion": "0.6.0.0", "fileVersion": "0.6.0.0"}}}, "Volo.Abp.Localization/0.3.0": {"dependencies": {"Newtonsoft.Json": "11.0.2", "Volo.Abp.Core": "2.4.0", "Volo.Abp.VirtualFileSystem": "0.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Localization.dll": {"assemblyVersion": "0.3.0.0", "fileVersion": "0.3.0.0"}}}, "Volo.Abp.ObjectExtending/2.4.0": {"dependencies": {"ConfigureAwait.Fody": "3.3.1", "Volo.Abp.Core": "2.4.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.ObjectExtending.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.PermissionManagement.Domain.Shared/0.3.0": {"dependencies": {"Volo.Abp.Core": "2.4.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Shared.dll": {"assemblyVersion": "0.3.0.0", "fileVersion": "0.3.0.0"}}}, "Volo.Abp.SettingManagement.Domain.Shared/0.3.0": {"dependencies": {"Volo.Abp.Core": "2.4.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.Shared.dll": {"assemblyVersion": "0.3.0.0", "fileVersion": "0.3.0.0"}}}, "Volo.Abp.TenantManagement.Domain.Shared/0.3.0": {"dependencies": {"Volo.Abp.Localization": "0.3.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.TenantManagement.Domain.Shared.dll": {"assemblyVersion": "0.3.0.0", "fileVersion": "0.3.0.0"}}}, "Volo.Abp.Users.Domain.Shared/0.3.0": {"dependencies": {"Volo.Abp.Core": "2.4.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.dll": {"assemblyVersion": "0.3.0.0", "fileVersion": "0.3.0.0"}}}, "Volo.Abp.Validation/0.3.0": {"dependencies": {"Volo.Abp.Core": "2.4.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Validation.dll": {"assemblyVersion": "0.3.0.0", "fileVersion": "0.3.0.0"}}}, "Volo.Abp.VirtualFileSystem/0.3.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Composite": "2.1.0", "Microsoft.Extensions.FileProviders.Physical": "3.1.2", "Volo.Abp.Core": "2.4.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll": {"assemblyVersion": "0.3.0.0", "fileVersion": "0.3.0.0"}}}}}, "libraries": {"TSZ.ServiceBase.FileStorageCenter.Domain.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ConfigureAwait.Fody/3.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-R9PQYf0AT4RBZcUXm22xWkCpSmNHdTzQ0dOyLIsxIK6dwXH4S9pY/rZdXU/63i8vZvSzZ99sB1kP7xer8MCe6w==", "path": "configureawait.fody/3.3.1", "hashPath": "configureawait.fody.3.3.1.nupkg.sha512"}, "Fody/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Oq9dxiHWkw/tPKu9LSmfp6uuCNDZLDkxwHB0sJuwyQRSmvFSB3Ab54WgCQWIsGDO9Z+va9expamqkKpFfdd1sQ==", "path": "fody/6.0.2", "hashPath": "fody.6.0.2.nupkg.sha512"}, "JetBrains.Annotations/2019.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-E0x48BwZJKoNMNCekWGKsV4saQS89lf58ydT2szseV44CMYIbaHXjc7+305WLw6up3ibZN9yH6QdGSZo5tQhLg==", "path": "jetbrains.annotations/2019.1.3", "hashPath": "jetbrains.annotations.2019.1.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-BxwRSBab309SYMCDCFyB6eSc7FnX5m9kOJQHw2IQIyb5PEtpfslhscTw63Gwhl3dPnaM1VGFXIyI0BVgpiLgOw==", "path": "microsoft.extensions.configuration/3.1.2", "hashPath": "microsoft.extensions.configuration.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-xmfdVdazTslWJ8od7uNS9QSPqn1wBC84RLprPrFS20EdAqd3lV0g0IZAitYbCiiICpjktnhzbUb85aLHNZ3RQw==", "path": "microsoft.extensions.configuration.abstractions/3.1.2", "hashPath": "microsoft.extensions.configuration.abstractions.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-IWrc9/voGki2pc5g8bRXIqs+P50tXOjNf47qgFKSu/pL50InRuXxh/nj5AG9Po8YRpvT/bYIUk3XQqHH7yUg5w==", "path": "microsoft.extensions.configuration.binder/3.1.2", "hashPath": "microsoft.extensions.configuration.binder.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-voJoqXRGnfB4nw3LRL6QY/rnYdaZA2vFCMbRzPP2iE13XbZciJhGR0fvTsDKyFA9VfQJzPgIj+F/0S0Zqdxt4w==", "path": "microsoft.extensions.configuration.commandline/3.1.2", "hashPath": "microsoft.extensions.configuration.commandline.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-AdpldFyx0PlwbgUatdj89jC/n5n2dqXP865NwM77bu9LcnEmWX37QTSAKeZT5a13c6G5MQ1v4lAGz2a9wpPf/g==", "path": "microsoft.extensions.configuration.environmentvariables/3.1.2", "hashPath": "microsoft.extensions.configuration.environmentvariables.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-itZcJUf2IRa4e4NFTQgR4JUmwndEU5O0isQsKkZXHiHXwExgLkX9D09R7YIK272w3jpKaYw/DejntAC7zzsNWg==", "path": "microsoft.extensions.configuration.fileextensions/3.1.2", "hashPath": "microsoft.extensions.configuration.fileextensions.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-AQ64UCqGXP2UTfkVE1fdUJdlKEEiFZIOXpt6lkIz+tunuJWh1m+/eIppY+ITgjoKsfFc2W8ldNonIntHx5ybNQ==", "path": "microsoft.extensions.configuration.json/3.1.2", "hashPath": "microsoft.extensions.configuration.json.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-1YOSOCsOUPVbcTDU+bifeT1z5dtMdwaZywWdKYocDBHwoILmxRsIKYS8CWVYPIggliHPEwjonNZfpdIktJkNiw==", "path": "microsoft.extensions.configuration.usersecrets/3.1.2", "hashPath": "microsoft.extensions.configuration.usersecrets.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-e+F6/wjQPOFHB/sGWTAqC8FX/C6+JZWWLpryXTAQYIS3tr+17lByADdP9Y6RtxfJ4kW/IPrU6RuxTNZNdAQz1A==", "path": "microsoft.extensions.dependencyinjection/3.1.2", "hashPath": "microsoft.extensions.dependencyinjection.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-/CZzCSCIm/3FFoXHfUpsfov/Elo268dcvlz/MMINT0vPgphqg2pAgdEn/EjCDyoAT3NAmsRmjfGwBumC1uYJtA==", "path": "microsoft.extensions.dependencyinjection.abstractions/3.1.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-O9+N6KuA7kiPIYpdgRFFveKRyI3X2hLgdqdEwQki0MOA5XtCVOkxz8O+6CK1+b1a7Y1TildGfx3i+h/652vyHg==", "path": "microsoft.extensions.fileproviders.abstractions/3.1.2", "hashPath": "microsoft.extensions.fileproviders.abstractions.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vc2oy4OlHaTH3hMZlMGymk4vzi93Nz3T1T+kMHBpoq9Ka287DHP8vZxK9MhgTsnNYP7S4hrqiUxsc5wEPyDYGg==", "path": "microsoft.extensions.fileproviders.composite/2.1.0", "hashPath": "microsoft.extensions.fileproviders.composite.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mMCI3/BLXAyBCDneqOI4ohETd0IXjbXZdoiCm1dYdnOdV193ByEOCFQ6/Vn9RVdU5UlC4Nn1P4J5Df7pXG/vGg==", "path": "microsoft.extensions.fileproviders.embedded/1.0.0", "hashPath": "microsoft.extensions.fileproviders.embedded.1.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-lAbbwKapBfwGLVcfNL7TG4o7zRqLOiVY7/ylUKgnh2D9TotJ2riXzNTmQldksIYrmcJcNrq/WBalTpawSSAkJg==", "path": "microsoft.extensions.fileproviders.physical/3.1.2", "hashPath": "microsoft.extensions.fileproviders.physical.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-/EgWQ25z1RZgzAT6JSOJiuQ/PFm53Kl1H3kzAgs5JIh52UaD1RmxW1znv5VbQlTfgLzRSeQZ3aPPA9SNakuSzw==", "path": "microsoft.extensions.filesystemglobbing/3.1.2", "hashPath": "microsoft.extensions.filesystemglobbing.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-oafEsTwy1ed4zycyjzgFet58IW3I/aC1uUJTWpFAs3mjkQzW52LqVlE/9AAW2IVk4q8EPw+GPsiFB17qYksNXQ==", "path": "microsoft.extensions.hosting.abstractions/3.1.2", "hashPath": "microsoft.extensions.hosting.abstractions.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Localization/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-s4y5gt/N1rcNnTXSPd5g+Z6EyjCKzsXmQcFfP7s6GKXDstOS+KGoCQEnQCdlGlz8Jin/v8Ep+40yA1ngvNFvZw==", "path": "microsoft.extensions.localization/3.1.2", "hashPath": "microsoft.extensions.localization.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-IWbB3w1ITn2KwQcVZYSoHzNGjEqObJGnwPZS2O6BE9SbkaHh7PLatyM78LjIIgyuEg/m1HP3t/GuRCUH15CliQ==", "path": "microsoft.extensions.localization.abstractions/3.1.2", "hashPath": "microsoft.extensions.localization.abstractions.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Logging/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-AIIRgKamzEqJNLZsHd37VogFX9YpxgrBmf/b3dznD7S0qjxWQnAs498ulLV1n6AKJ8XVjTCBNzsvQiSwCa7dIw==", "path": "microsoft.extensions.logging/3.1.2", "hashPath": "microsoft.extensions.logging.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-cIXPw7VVX3fON4uuHwJFmCi0qDl8uY75xZMKB2oM3In0ZDEB1Ee+p9Ti1DSw92AwRtJ2Zh+QG1joTBednJMzvA==", "path": "microsoft.extensions.logging.abstractions/3.1.2", "hashPath": "microsoft.extensions.logging.abstractions.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Options/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6F4anwt9yMlnQckac2etjrasRFyqZNIp46p+i9qVps0DXNsOLZIKRkqq4AY4FlxXxKeGkEJC7M77RQEkvd3p8Q==", "path": "microsoft.extensions.options/3.1.2", "hashPath": "microsoft.extensions.options.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-NJRuISEgTUh3/ehm0mwGx1FhepKQuUxfMm0BKJ0b8UNABuDaXFLtlV/5Bd9hT5vmeZTGGB4hvM02uRaCiSACNw==", "path": "microsoft.extensions.options.configurationextensions/3.1.2", "hashPath": "microsoft.extensions.options.configurationextensions.3.1.2.nupkg.sha512"}, "Microsoft.Extensions.Primitives/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-WGtoFWY9yc9HGMG6ObDNQPz9dBP+xz/GqFe2dKjdE/cSdXFEKxCFTyYCzL/e8kxVkc/Bq9qjOsXRWydvn0g9Uw==", "path": "microsoft.extensions.primitives/3.1.2", "hashPath": "microsoft.extensions.primitives.3.1.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Newtonsoft.Json/11.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-IvJe1pj7JHEsP8B8J8DwlMEx8UInrs/x+9oVY+oCD13jpLu4JbJU2WCIsMRn5C4yW9+DgkaO8uiVE5VHKjpmdQ==", "path": "newtonsoft.json/11.0.2", "hashPath": "newtonsoft.json.11.0.2.nupkg.sha512"}, "Nito.AsyncEx.Context/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qnth1Ye+QSLg8P3fSFYzk7ue6oUUHQcKpLitgAig8xRFqTK5W1KTlfxF/Z8Eo0BuqZ17a5fAGtXrdKJsLqivZw==", "path": "nito.asyncex.context/5.0.0", "hashPath": "nito.asyncex.context.5.0.0.nupkg.sha512"}, "Nito.AsyncEx.Coordination/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kjauyO8UMo/FGZO/M8TdjXB8ZlBPFOiRN8yakThaGQbYOywazQ0kGZ39SNr2gNNzsTxbZOUudBMYNo+IrtscbA==", "path": "nito.asyncex.coordination/5.0.0", "hashPath": "nito.asyncex.coordination.5.0.0.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZtvotignafOLteP4oEjVcF3k2L8h73QUCaFpVKWbU+EOlW/I+JGkpMoXIl0rlwPcDmR84RxzggLRUNMaWlOosA==", "path": "nito.asyncex.tasks/5.0.0", "hashPath": "nito.asyncex.tasks.5.0.0.nupkg.sha512"}, "Nito.Collections.Deque/1.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-yGDKqCQ61i97MyfEUYG6+ln5vxpx11uA5M9+VV9B7stticbFm19YMI/G9w4AFYVBj5PbPi138P8IovkMFAL0Aw==", "path": "nito.collections.deque/1.0.4", "hashPath": "nito.collections.deque.1.0.4.nupkg.sha512"}, "Nito.Disposables/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ExJl/jTjegSLHGcwnmaYaI5xIlrefAsVdeLft7VLtXI2+W5irihiu36LizWvlaUpzY1/llo+YSh09uSHMu2VFw==", "path": "nito.disposables/2.0.0", "hashPath": "nito.disposables.2.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-RVSM6wZUo6L2y6P3vN6gjUtyJ2IF2RVtrepF3J7nrDKfFQd5u/SnSUFclchYQis8/k5scHy9E+fVeKVQLnnkzw==", "path": "system.collections.immutable/1.7.0", "hashPath": "system.collections.immutable.1.7.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "path": "system.componentmodel.annotations/4.7.0", "hashPath": "system.componentmodel.annotations.4.7.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-GFYslLz/1ZZ0+gbqYED2zlD0H95BIK4hOpIcEEEfTDDXAcKE5vpXQQseOGduNVjcJZOF3Wx+4npa2EjdFpuDgA==", "path": "system.linq.dynamic.core/1.0.19", "hashPath": "system.linq.dynamic.core.1.0.19.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "Volo.Abp.AuditLogging.Domain.Shared/0.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-8I+v3WTuweRCHxnrcibyeQ9K2h0/kacaiscpPPgg06eo82vvqFihbj6ArkXwWiPPi1TGz2i1W3xmD6u+wtrCjQ==", "path": "volo.abp.auditlogging.domain.shared/0.4.1", "hashPath": "volo.abp.auditlogging.domain.shared.0.4.1.nupkg.sha512"}, "Volo.Abp.BackgroundJobs.Domain.Shared/0.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-hAwebW8U1bB/OBBS6RmVhaLnNVf7GLE8Z03Tca3mcG/ug7P7x4f2vkEYW+gS5rvtG5/oB5/Dc5C5s5TjmXvoKw==", "path": "volo.abp.backgroundjobs.domain.shared/0.4.1", "hashPath": "volo.abp.backgroundjobs.domain.shared.0.4.1.nupkg.sha512"}, "Volo.Abp.Core/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-wwYm+2cZOEWz7c6uxNtNO35wP9AQDiEbz8sruHoaJ292Y77nqh7eu83H757KHVwjpChf4dK4oARmZ/6Og9aI4w==", "path": "volo.abp.core/2.4.0", "hashPath": "volo.abp.core.2.4.0.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Domain.Shared/0.15.0": {"type": "package", "serviceable": true, "sha512": "sha512-pOzZ6FpDrOxw47YEvDS5G6CzV8CbtCwRiPt0oa27D1+Ht7+NebxevdNlArkR5f2W2M7u4ZCgz7+ZCWdlGhymoA==", "path": "volo.abp.featuremanagement.domain.shared/0.15.0", "hashPath": "volo.abp.featuremanagement.domain.shared.0.15.0.nupkg.sha512"}, "Volo.Abp.Identity.Domain.Shared/0.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VNXIl8HvkyaHxSqAicL3p1aR1VCv74ROvZ7mmfVdOjpX450R4KnQ/80eFSFUatHodhytYg7wO/gxUHTNxmNYew==", "path": "volo.abp.identity.domain.shared/0.3.0", "hashPath": "volo.abp.identity.domain.shared.0.3.0.nupkg.sha512"}, "Volo.Abp.IdentityServer.Domain.Shared/0.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-6aBzeF2bcYBS8sOMP3BYdGJVNe6hoODLCfV/nyYsGVMOuwq3Zk2/NlPK2355EIHmhanx2NXsPsY3GaQrfdpQeA==", "path": "volo.abp.identityserver.domain.shared/0.6.0", "hashPath": "volo.abp.identityserver.domain.shared.0.6.0.nupkg.sha512"}, "Volo.Abp.Localization/0.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VGjyBKlfsB9no2E2q09LS/I7O/Vbqg3mJcLwtXSNibXwwZFEDTKnqo4k3pPJfIFJmG+n2ThtzUG8OpA69Cgswg==", "path": "volo.abp.localization/0.3.0", "hashPath": "volo.abp.localization.0.3.0.nupkg.sha512"}, "Volo.Abp.ObjectExtending/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-K8K3ZjbX7k6rF7Q1fBizcI2cSaG/qauDdq9lFoldpCuQbkgBzNup9KpcZytMKgnufIWtnFBx1+Ysn2ScIQIlaQ==", "path": "volo.abp.objectextending/2.4.0", "hashPath": "volo.abp.objectextending.2.4.0.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Domain.Shared/0.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HRpGxHawNqQKx6ySG1AjVn6F6m8QTFNphZ/Wx9HijengnbXGsz/JN85ahXrj/ftOTby2sy9R+VspL73DBzmTxg==", "path": "volo.abp.permissionmanagement.domain.shared/0.3.0", "hashPath": "volo.abp.permissionmanagement.domain.shared.0.3.0.nupkg.sha512"}, "Volo.Abp.SettingManagement.Domain.Shared/0.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lVxsLlQf7ZalX7GtDD3K6KRUcStqb6FiVDCBs4HW0V60qp2lO9P2Ei681TJAO6aTPk0DSvaoyDbtK7ls0fZyGQ==", "path": "volo.abp.settingmanagement.domain.shared/0.3.0", "hashPath": "volo.abp.settingmanagement.domain.shared.0.3.0.nupkg.sha512"}, "Volo.Abp.TenantManagement.Domain.Shared/0.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-e6rOKpbOX0e0l+0juEcKom9t/4tEL6mtzjkhthLWwCeXYwCkMt7CUMWNIMP+7j9j7EQBsXOnIqd4lcsh19pI1g==", "path": "volo.abp.tenantmanagement.domain.shared/0.3.0", "hashPath": "volo.abp.tenantmanagement.domain.shared.0.3.0.nupkg.sha512"}, "Volo.Abp.Users.Domain.Shared/0.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7C21JDHOg1BZ/hWQGjs2shLETJ8dn0bH/kIoWVLEQ18Pra4GIeT2MURaut/tnZoDibuXKj6k3JRiB0iUSGMtnw==", "path": "volo.abp.users.domain.shared/0.3.0", "hashPath": "volo.abp.users.domain.shared.0.3.0.nupkg.sha512"}, "Volo.Abp.Validation/0.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qu/vW3VES3VjBu/2aj1P8MSkxfEo/ihD05i7Io9CQm5oKFFlx23q6cgxEpnhhMcQIb1Rw5MjvigWozrioFZlKA==", "path": "volo.abp.validation/0.3.0", "hashPath": "volo.abp.validation.0.3.0.nupkg.sha512"}, "Volo.Abp.VirtualFileSystem/0.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+bvsvGJbq2tYGOHMPWEQEJlCESgdB5JMxN2VD0t8vAKpoPlwNMVSG+kNJS+bBYF9NxYpJkUPCdqnXZzWTkKwdg==", "path": "volo.abp.virtualfilesystem/0.3.0", "hashPath": "volo.abp.virtualfilesystem.0.3.0.nupkg.sha512"}}}