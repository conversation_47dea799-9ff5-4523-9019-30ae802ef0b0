{
  //"apollo": {
  //  "AppId": "TSZ.MicroservicePlat",
  //  "RefreshInterval": 36000000,
  //  "MetaServer": "http://*************:8080/",
  //  "Secret": "2653ae08803c4adb8703e0a311da968c",
  //  "Namespaces": [ "TSZ_BaseCommon.json", "Base_FileStorageCenter.json" ],
  //  "Env": "Pro",
  //  "Meta": {
  //    "PRO": "http://*************:8080/"
  //  }
  //}
  "NacosConfig": {
    "Listeners": [
      {
        "Optional": false,
        "DataId": "Base_FileStorageCenter",
        "Group": "DEFAULT_GROUP"
      },
      {
        "Optional": false,
        "DataId": "TSZ_BaseCommon",
        "Group": "DEFAULT_GROUP"
      }
    ],
    "Namespace": "Development",
    "ServerAddresses": [ "http://*************:8848/" ],
    "UserName": "nacos",
    "Password": "nacos",
    "Ip": "", //nacos客户端自动检测本机IP
    "Port": 10040,
    "ListenerInterval": 3600000,//毫秒，客户端多久检查一次配置是否有更新
    "ServiceName": "tsz-base-filestoragecenter"
    
  }
}