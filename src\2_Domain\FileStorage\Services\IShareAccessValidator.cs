using System;
using System.Threading.Tasks;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage.Services
{
    /// <summary>
    /// 分享访问验证服务接口
    /// 提供分享链接的访问权限验证功能
    /// </summary>
    public interface IShareAccessValidator
    {
        /// <summary>
        /// 验证分享访问权限
        /// </summary>
        /// <param name="request">访问验证请求</param>
        /// <returns>验证结果</returns>
        Task<ShareAccessValidationResult> ValidateAccessAsync(ShareAccessValidationRequest request);

        /// <summary>
        /// 验证IP地址是否被允许
        /// </summary>
        /// <param name="shareId">分享ID</param>
        /// <param name="ipAddress">IP地址</param>
        /// <returns>是否允许</returns>
        Task<bool> ValidateIPAccessAsync(Guid shareId, string ipAddress);

        /// <summary>
        /// 验证用户是否有访问权限
        /// </summary>
        /// <param name="shareId">分享ID</param>
        /// <param name="userId">用户ID</param>
        /// <returns>是否允许</returns>
        Task<bool> ValidateUserAccessAsync(Guid shareId, Guid? userId);

        /// <summary>
        /// 验证时间段限制
        /// </summary>
        /// <param name="shareId">分享ID</param>
        /// <param name="accessTime">访问时间</param>
        /// <returns>是否允许</returns>
        Task<bool> ValidateTimeRestrictionAsync(Guid shareId, DateTime accessTime);

        /// <summary>
        /// 验证设备类型限制
        /// </summary>
        /// <param name="shareId">分享ID</param>
        /// <param name="userAgent">用户代理字符串</param>
        /// <returns>是否允许</returns>
        Task<bool> ValidateDeviceRestrictionAsync(Guid shareId, string userAgent);

        /// <summary>
        /// 验证访问频率限制
        /// </summary>
        /// <param name="shareId">分享ID</param>
        /// <param name="ipAddress">IP地址</param>
        /// <returns>是否允许</returns>
        Task<bool> ValidateRateLimitAsync(Guid shareId, string ipAddress);

        /// <summary>
        /// 记录访问尝试
        /// </summary>
        /// <param name="request">访问记录请求</param>
        /// <returns></returns>
        Task RecordAccessAttemptAsync(ShareAccessAttemptRequest request);
    }

    /// <summary>
    /// 分享访问验证请求
    /// </summary>
    public class ShareAccessValidationRequest
    {
        /// <summary>
        /// 分享ID
        /// </summary>
        public Guid ShareId { get; set; }

        /// <summary>
        /// 访问密码（如果需要）
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 访问者IP地址
        /// </summary>
        public string IPAddress { get; set; }

        /// <summary>
        /// 用户ID（如果已登录）
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 用户代理字符串
        /// </summary>
        public string UserAgent { get; set; }

        /// <summary>
        /// 引用页面
        /// </summary>
        public string Referrer { get; set; }

        /// <summary>
        /// 访问时间
        /// </summary>
        public DateTime AccessTime { get; set; }

        /// <summary>
        /// 验证码（如果需要）
        /// </summary>
        public string Captcha { get; set; }

        /// <summary>
        /// 验证码会话ID
        /// </summary>
        public string CaptchaSessionId { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ShareAccessValidationRequest()
        {
            AccessTime = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 分享访问验证结果
    /// </summary>
    public class ShareAccessValidationResult
    {
        /// <summary>
        /// 是否允许访问
        /// </summary>
        public bool IsAllowed { get; set; }

        /// <summary>
        /// 拒绝原因
        /// </summary>
        public string DenialReason { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public ShareAccessDenialCode DenialCode { get; set; }

        /// <summary>
        /// 是否需要密码
        /// </summary>
        public bool RequirePassword { get; set; }

        /// <summary>
        /// 是否需要登录
        /// </summary>
        public bool RequireLogin { get; set; }

        /// <summary>
        /// 是否需要验证码
        /// </summary>
        public bool RequireCaptcha { get; set; }

        /// <summary>
        /// 剩余访问次数
        /// </summary>
        public int? RemainingAccessCount { get; set; }

        /// <summary>
        /// 剩余下载次数
        /// </summary>
        public int? RemainingDownloadCount { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 额外信息
        /// </summary>
        public string AdditionalInfo { get; set; }

        /// <summary>
        /// 创建允许访问的结果
        /// </summary>
        /// <returns></returns>
        public static ShareAccessValidationResult Allow()
        {
            return new ShareAccessValidationResult
            {
                IsAllowed = true
            };
        }

        /// <summary>
        /// 创建拒绝访问的结果
        /// </summary>
        /// <param name="reason">拒绝原因</param>
        /// <param name="code">错误代码</param>
        /// <returns></returns>
        public static ShareAccessValidationResult Deny(string reason, ShareAccessDenialCode code)
        {
            return new ShareAccessValidationResult
            {
                IsAllowed = false,
                DenialReason = reason,
                DenialCode = code
            };
        }
    }

    /// <summary>
    /// 分享访问拒绝代码
    /// </summary>
    public enum ShareAccessDenialCode
    {
        /// <summary>
        /// 未知错误
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// 分享不存在
        /// </summary>
        ShareNotFound = 1,

        /// <summary>
        /// 分享已过期
        /// </summary>
        ShareExpired = 2,

        /// <summary>
        /// 分享已禁用
        /// </summary>
        ShareDisabled = 3,

        /// <summary>
        /// 密码错误
        /// </summary>
        InvalidPassword = 4,

        /// <summary>
        /// IP地址被禁止
        /// </summary>
        IPBlocked = 5,

        /// <summary>
        /// 用户无权限
        /// </summary>
        UserNotAllowed = 6,

        /// <summary>
        /// 超出访问次数限制
        /// </summary>
        AccessLimitExceeded = 7,

        /// <summary>
        /// 超出下载次数限制
        /// </summary>
        DownloadLimitExceeded = 8,

        /// <summary>
        /// 时间段限制
        /// </summary>
        TimeRestricted = 9,

        /// <summary>
        /// 设备类型限制
        /// </summary>
        DeviceRestricted = 10,

        /// <summary>
        /// 访问频率限制
        /// </summary>
        RateLimited = 11,

        /// <summary>
        /// 需要登录
        /// </summary>
        LoginRequired = 12,

        /// <summary>
        /// 验证码错误
        /// </summary>
        InvalidCaptcha = 13,

        /// <summary>
        /// 地理位置限制
        /// </summary>
        GeoRestricted = 14,

        /// <summary>
        /// 引用页面限制
        /// </summary>
        ReferrerRestricted = 15
    }

    /// <summary>
    /// 分享访问尝试记录请求
    /// </summary>
    public class ShareAccessAttemptRequest
    {
        /// <summary>
        /// 分享ID
        /// </summary>
        public Guid ShareId { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        public string IPAddress { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        public string UserAgent { get; set; }

        /// <summary>
        /// 访问时间
        /// </summary>
        public DateTime AccessTime { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// 拒绝原因
        /// </summary>
        public string DenialReason { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ShareAccessAttemptRequest()
        {
            AccessTime = DateTime.UtcNow;
        }
    }
}
