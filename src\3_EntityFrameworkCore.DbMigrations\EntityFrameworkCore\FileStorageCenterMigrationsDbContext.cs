﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using TSZ.Abp.Modulies;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.IdentityServer.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement.EntityFrameworkCore;

namespace TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore
{
    /* This DbContext is only used for database migrations.
     * It is not used on runtime. See FileStorageCenterDbContext for the runtime DbContext.
     * It is a unified model that includes configuration for
     * all used modules and your application.
     */
    public class FileStorageCenterMigrationsDbContext : TszAbpDbContext<FileStorageCenterMigrationsDbContext>
    {
        public FileStorageCenterMigrationsDbContext(DbContextOptions<FileStorageCenterMigrationsDbContext> options, IConfiguration configration)
            : base(options, configration)
        {

        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            /* Include modules to your migration db context */

            builder.ConfigurePermissionManagement();
            builder.ConfigureSettingManagement();
            builder.ConfigureBackgroundJobs();
            builder.ConfigureAuditLogging();
            builder.ConfigureIdentity();
            builder.ConfigureIdentityServer();
            builder.ConfigureFeatureManagement();
            builder.ConfigureTenantManagement();

            /* Configure your own tables/entities inside the ConfigureFileStorageCenter method */

            builder.ConfigureFileStorageCenter();
        }
    }
}