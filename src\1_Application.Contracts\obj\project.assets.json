{"version": 3, "targets": {"net9.0": {"AlibabaCloud.EndpointUtil/0.1.1": {"type": "package", "dependencies": {"Newtonsoft.Json": "9.0.1", "Tea": "0.3.2"}, "compile": {"lib/netstandard2.0/AlibabaCloud.EndpointUtil.dll": {}}, "runtime": {"lib/netstandard2.0/AlibabaCloud.EndpointUtil.dll": {}}}, "AlibabaCloud.OpenApiClient/0.0.6": {"type": "package", "dependencies": {"AlibabaCloud.OpenApiUtil": "1.0.3", "AlibabaCloud.TeaUtil": "0.1.9", "Aliyun.Credentials": "1.3.1", "Tea": "1.0.4"}, "compile": {"lib/netstandard2.0/AlibabaCloud.OpenApiClient.dll": {}}, "runtime": {"lib/netstandard2.0/AlibabaCloud.OpenApiClient.dll": {}}}, "AlibabaCloud.OpenApiUtil/1.0.3": {"type": "package", "dependencies": {"Newtonsoft.Json": "9.0.1", "Tea": "1.0.3"}, "compile": {"lib/netstandard2.0/AlibabaCloud.OpenApiUtil.dll": {}}, "runtime": {"lib/netstandard2.0/AlibabaCloud.OpenApiUtil.dll": {}}}, "AlibabaCloud.SDK.Sts20150401/1.0.0": {"type": "package", "dependencies": {"AlibabaCloud.EndpointUtil": "0.1.1", "AlibabaCloud.OpenApiClient": "0.0.6", "AlibabaCloud.TeaUtil": "0.1.9", "Tea": "1.0.4"}, "compile": {"lib/netstandard2.0/AlibabaCloud.SDK.Sts20150401.dll": {}}, "runtime": {"lib/netstandard2.0/AlibabaCloud.SDK.Sts20150401.dll": {}}}, "AlibabaCloud.TeaUtil/0.1.9": {"type": "package", "dependencies": {"Newtonsoft.Json": "9.0.1", "Tea": "1.0.2"}, "compile": {"lib/netstandard2.0/AlibabaCloud.TeaUtil.dll": {}}, "runtime": {"lib/netstandard2.0/AlibabaCloud.TeaUtil.dll": {}}}, "Aliyun.Credentials/1.3.1": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.5.0", "Newtonsoft.Json": "9.0.1", "Tea": "1.0.1"}, "compile": {"lib/netstandard2.0/Aliyun.Credentials.dll": {}}, "runtime": {"lib/netstandard2.0/Aliyun.Credentials.dll": {}}}, "BouncyCastle/1.8.5": {"type": "package", "compile": {"lib/BouncyCastle.Crypto.dll": {}}, "runtime": {"lib/BouncyCastle.Crypto.dll": {}}}, "ConfigureAwait.Fody/3.3.1": {"type": "package", "dependencies": {"Fody": "6.0.2"}, "compile": {"lib/netstandard2.0/ConfigureAwait.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/ConfigureAwait.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "Fody/6.0.2": {"type": "package", "build": {"build/_._": {}}}, "JetBrains.Annotations/2020.3.0": {"type": "package", "compile": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}, "runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}}, "Microsoft.AspNetCore.Authorization/5.0.5": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "5.0.5", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "1.0.0", "Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "Microsoft.Extensions.Configuration.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "1.0.0", "Microsoft.Extensions.Configuration.Abstractions": "1.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "Microsoft.AspNetCore.WebUtilities": "1.0.0", "Microsoft.Extensions.ObjectPool": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "Microsoft.Net.Http.Headers": "1.0.0", "System.Buffers": "4.0.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "1.0.0", "System.Globalization.Extensions": "4.0.1", "System.Linq.Expressions": "4.1.0", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encodings.Web": "4.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Extensions/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "Microsoft.Net.Http.Headers": "1.0.0", "System.Buffers": "4.0.0", "System.IO.FileSystem": "4.0.1"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Features/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.Collections": "4.0.11", "System.ComponentModel": "4.0.1", "System.Linq": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Net.WebSockets": "4.0.0", "System.Runtime.Extensions": "4.1.0", "System.Security.Claims": "4.0.1", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Security.Principal": "4.0.1"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Metadata/5.0.5": {"type": "package", "compile": {"lib/net5.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "1.0.0", "Microsoft.CSharp": "4.0.1", "Microsoft.Net.Http.Headers": "1.0.0", "System.ComponentModel.TypeConverter": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Text.Encoding.Extensions": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Core/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "1.0.0", "Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.AspNetCore.Http": "1.0.0", "Microsoft.AspNetCore.Mvc.Abstractions": "1.0.0", "Microsoft.AspNetCore.Routing": "1.0.0", "Microsoft.Extensions.DependencyModel": "1.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.PlatformAbstractions": "1.0.0", "System.Buffers": "4.0.0", "System.Diagnostics.DiagnosticSource": "4.0.0", "System.Text.Encoding": "4.0.11"}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Routing/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.AspNetCore.Routing.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.ObjectPool": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Collections": "4.0.11", "System.Text.RegularExpressions": "4.1.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Routing.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Routing.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Routing.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "System.Collections.Concurrent": "4.0.12", "System.Reflection.Extensions": "4.0.1", "System.Threading.Tasks": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Routing.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Routing.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.WebUtilities/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.Buffers": "4.0.0", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Text.Encodings.Web": "4.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}}, "Microsoft.CSharp/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.DotNet.InternalAbstractions/1.0.0": {"type": "package", "dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.DotNet.InternalAbstractions.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.InternalAbstractions.dll": {}}}, "Microsoft.Extensions.Configuration/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.CommandLine/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.UserSecrets/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"build/netstandard2.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/5.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyModel/1.0.0": {"type": "package", "dependencies": {"Microsoft.DotNet.InternalAbstractions": "1.0.0", "Newtonsoft.Json": "9.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Linq": "4.1.0"}, "compile": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Composite/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Embedded/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileSystemGlobbing": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Hosting.Abstractions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization/5.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Localization.Abstractions": "5.0.5", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization.Abstractions/5.0.5": {"type": "package", "compile": {"lib/net5.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.ObjectPool/1.0.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.PlatformAbstractions/1.0.0": {"type": "package", "dependencies": {"System.AppContext": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.PlatformAbstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.PlatformAbstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}}, "Microsoft.Net.Http.Headers/1.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.0.0", "System.Collections": "4.0.11", "System.Diagnostics.Contracts": "4.0.1", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.0.11"}, "compile": {"lib/netstandard1.1/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.1/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Primitives/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.1": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Context/5.1.0": {"type": "package", "dependencies": {"Nito.AsyncEx.Tasks": "5.1.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Coordination/5.1.0": {"type": "package", "dependencies": {"Nito.AsyncEx.Tasks": "5.1.0", "Nito.Collections.Deque": "1.1.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Tasks/5.1.0": {"type": "package", "dependencies": {"Nito.Disposables": "2.2.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}}, "Nito.Collections.Deque/1.1.0": {"type": "package", "compile": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"related": ".xml"}}}, "Nito.Disposables/2.2.0": {"type": "package", "dependencies": {"System.Collections.Immutable": "1.4.0"}, "compile": {"lib/netstandard2.1/Nito.Disposables.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"related": ".xml"}}}, "runtime.native.System/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Net.Http/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Security.Cryptography/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Scriban/3.6.0": {"type": "package", "compile": {"lib/net5.0/Scriban.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Scriban.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "System.AppContext/4.1.0": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.6/System.AppContext.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.AppContext.dll": {}}}, "System.Buffers/4.0.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.1/System.Buffers.dll": {}}, "runtime": {"lib/netstandard1.1/System.Buffers.dll": {}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {"related": ".xml"}}}, "System.Collections.Concurrent/4.0.12": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Collections.Concurrent.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.Concurrent.dll": {}}}, "System.Collections.Immutable/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}}, "System.Collections.NonGeneric/4.0.1": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Collections.NonGeneric.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.NonGeneric.dll": {}}}, "System.Collections.Specialized/4.0.1": {"type": "package", "dependencies": {"System.Collections.NonGeneric": "4.0.1", "System.Globalization": "4.0.11", "System.Globalization.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.Specialized.dll": {}}}, "System.ComponentModel/4.0.1": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ComponentModel.dll": {}}}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}}, "System.ComponentModel.Primitives/4.1.0": {"type": "package", "dependencies": {"System.ComponentModel": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.ComponentModel.Primitives.dll": {}}}, "System.ComponentModel.TypeConverter/4.1.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Collections.NonGeneric": "4.0.1", "System.Collections.Specialized": "4.0.1", "System.ComponentModel": "4.0.1", "System.ComponentModel.Primitives": "4.1.0", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.5/System.ComponentModel.TypeConverter.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.ComponentModel.TypeConverter.dll": {}}}, "System.Diagnostics.Contracts/4.0.1": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Diagnostics.Contracts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.Diagnostics.Contracts.dll": {}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Diagnostics.Debug.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/4.0.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}}, "System.Diagnostics.Tracing/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Diagnostics.Tracing.dll": {"related": ".xml"}}}, "System.Dynamic.Runtime/4.0.11": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Dynamic.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Dynamic.Runtime.dll": {}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Globalization.dll": {"related": ".xml"}}}, "System.Globalization.Calendars/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Globalization": "4.0.11", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Globalization.Extensions/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Globalization.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.FileSystem/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.IO.FileSystem.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.dll": {"related": ".xml"}}}, "System.IO.FileSystem.Primitives/4.0.1": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Linq.Dynamic.Core/1.2.9": {"type": "package", "compile": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}}, "System.Linq.Expressions/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {}}}, "System.Linq.Queryable/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Linq.Queryable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Linq.Queryable.dll": {}}}, "System.Net.Primitives/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}, "compile": {"ref/netstandard1.3/System.Net.Primitives.dll": {"related": ".xml"}}}, "System.Net.WebSockets/4.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Net.WebSockets.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Net.WebSockets.dll": {}}}, "System.ObjectModel/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.ObjectModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.1/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Extensions.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.TypeExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Resources.ResourceManager.dll": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Extensions.dll": {"related": ".xml"}}}, "System.Runtime.Handles/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Handles.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}, "compile": {"ref/netstandard1.5/System.Runtime.InteropServices.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0"}, "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.Loader/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Loader.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Runtime.Loader.dll": {}}}, "System.Runtime.Numerics/4.0.1": {"type": "package", "dependencies": {"System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"ref/netstandard1.1/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.Numerics.dll": {}}}, "System.Security.Claims/4.0.1": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Security.Principal": "4.0.1"}, "compile": {"ref/netstandard1.3/System.Security.Claims.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Security.Claims.dll": {}}}, "System.Security.Cryptography.Algorithms/4.2.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/4.2.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11"}, "compile": {"ref/netstandard1.6/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Csp/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Csp.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Csp.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Encoding/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.OpenSsl/4.0.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.6/_._": {}}, "runtime": {"lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll": {"assetType": "runtime", "rid": "unix"}}}, "System.Security.Cryptography.Primitives/4.0.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Primitives.dll": {}}, "runtime": {"lib/netstandard1.3/System.Security.Cryptography.Primitives.dll": {}}}, "System.Security.Cryptography.X509Certificates/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Globalization.Calendars": "4.0.1", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Cng": "4.2.0", "System.Security.Cryptography.Csp": "4.0.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0", "runtime.native.System.Net.Http": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.4/System.Security.Cryptography.X509Certificates.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal/4.0.1": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Security.Principal.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.Security.Principal.dll": {}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.Extensions/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.Extensions.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/4.0.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.RegularExpressions/4.1.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.6/System.Text.RegularExpressions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Text.RegularExpressions.dll": {}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "Tea/1.0.4": {"type": "package", "dependencies": {"BouncyCastle": "1.8.5"}, "compile": {"lib/netstandard2.0/Tea.dll": {}}, "runtime": {"lib/netstandard2.0/Tea.dll": {}}}, "TimeZoneConverter/3.4.0": {"type": "package", "compile": {"lib/netstandard2.0/TimeZoneConverter.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/TimeZoneConverter.dll": {"related": ".xml"}}}, "Volo.Abp.Account.Application.Contracts/0.19.0": {"type": "package", "dependencies": {"Volo.Abp.Identity.Application.Contracts": "0.19.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Account.Application.Contracts.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Account.Application.Contracts.dll": {"related": ".pdb"}}}, "Volo.Abp.Auditing/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Data": "4.3.0", "Volo.Abp.Json": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Security": "4.3.0", "Volo.Abp.Threading": "4.3.0", "Volo.Abp.Timing": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Auditing.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Auditing.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AuditLogging.Domain.Shared/0.4.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.4.1", "Volo.Abp.Localization": "0.4.1"}, "compile": {"lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.Authorization/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Security": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Authorization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Authorization.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Authorization.Abstractions/4.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "5.0.5", "Volo.Abp.MultiTenancy": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.BackgroundJobs.Abstractions/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Json": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.BackgroundJobs.Domain.Shared/0.4.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.4.1"}, "compile": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.Core/4.3.0": {"type": "package", "dependencies": {"JetBrains.Annotations": "2020.3.0", "Microsoft.Extensions.Configuration.CommandLine": "5.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "5.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.1", "Microsoft.Extensions.Hosting.Abstractions": "5.0.0", "Microsoft.Extensions.Localization": "5.0.5", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "5.0.0", "Nito.AsyncEx.Context": "5.1.0", "Nito.AsyncEx.Coordination": "5.1.0", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Linq.Dynamic.Core": "1.2.9", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Core.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Data/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.EventBus.Abstractions": "4.3.0", "Volo.Abp.ObjectExtending": "4.3.0", "Volo.Abp.Uow": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Data.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Data.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Application/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0", "Volo.Abp.Ddd.Domain": "4.3.0", "Volo.Abp.Features": "4.3.0", "Volo.Abp.GlobalFeatures": "4.3.0", "Volo.Abp.Http.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.ObjectMapping": "4.3.0", "Volo.Abp.Security": "4.3.0", "Volo.Abp.Settings": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Application.Contracts/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Auditing": "4.3.0", "Volo.Abp.Localization": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Domain/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Auditing": "4.3.0", "Volo.Abp.Data": "4.3.0", "Volo.Abp.EventBus": "4.3.0", "Volo.Abp.ExceptionHandling": "4.3.0", "Volo.Abp.Guids": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.ObjectMapping": "4.3.0", "Volo.Abp.Specifications": "4.3.0", "Volo.Abp.Threading": "4.3.0", "Volo.Abp.Timing": "4.3.0", "Volo.Abp.Uow": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Emailing/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.BackgroundJobs.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Settings": "4.3.0", "Volo.Abp.TextTemplating": "4.3.0", "Volo.Abp.VirtualFileSystem": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Emailing.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Emailing.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.EventBus/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.EventBus.Abstractions": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.EventBus.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.EventBus.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.EventBus.Abstractions/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.ExceptionHandling/4.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.5", "Volo.Abp.Localization": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.ExceptionHandling.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.ExceptionHandling.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.FeatureManagement.Application.Contracts/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Ddd.Application": "0.15.0", "Volo.Abp.FeatureManagement.Domain.Shared": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Application.Contracts.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Application.Contracts.dll": {"related": ".pdb"}}}, "Volo.Abp.FeatureManagement.Domain.Shared/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Localization": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.Features/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Features.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Features.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.GlobalFeatures/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "4.3.0", "Volo.Abp.Core": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.VirtualFileSystem": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.GlobalFeatures.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.GlobalFeatures.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Guids/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Guids.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Guids.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Http.Abstractions/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Http.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Http.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Identity.Application.Contracts/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Authorization": "0.3.0", "Volo.Abp.Ddd.Application": "0.3.0", "Volo.Abp.Identity.Domain.Shared": "0.3.0", "Volo.Abp.PermissionManagement.Application.Contracts": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Identity.Application.Contracts.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Identity.Application.Contracts.dll": {"related": ".pdb"}}}, "Volo.Abp.Identity.Domain.Shared/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Localization": "0.3.0", "Volo.Abp.Users.Domain.Shared": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.IdentityServer.Domain.Shared/0.6.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.6.0", "Volo.Abp.Localization": "0.6.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.Json/4.3.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "Volo.Abp.ObjectExtending": "4.3.0", "Volo.Abp.Timing": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Json.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Json.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Localization/0.3.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "11.0.2", "Volo.Abp.Core": "0.3.0", "Volo.Abp.VirtualFileSystem": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Localization.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Localization.dll": {"related": ".pdb"}}}, "Volo.Abp.Localization.Abstractions/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.MultiTenancy/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Data": "4.3.0", "Volo.Abp.EventBus.Abstractions": "4.3.0", "Volo.Abp.Security": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.MultiTenancy.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.MultiTenancy.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.ObjectExtending/2.4.0": {"type": "package", "dependencies": {"ConfigureAwait.Fody": "3.3.1", "Volo.Abp.Core": "2.4.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.ObjectExtending.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.ObjectExtending.dll": {"related": ".pdb"}}}, "Volo.Abp.ObjectMapping/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.ObjectMapping.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.ObjectMapping.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.PermissionManagement.Application.Contracts/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Authorization": "0.3.0", "Volo.Abp.Ddd.Application": "0.3.0", "Volo.Abp.PermissionManagement.Domain.Shared": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Application.Contracts.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Application.Contracts.dll": {"related": ".pdb"}}}, "Volo.Abp.PermissionManagement.Domain.Shared/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.Security/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Security.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Security.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.SettingManagement.Application.Contracts/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Ddd.Application": "4.3.0", "Volo.Abp.Emailing": "4.3.0", "Volo.Abp.SettingManagement.Domain.Shared": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Application.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Application.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Settings/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Localization.Abstractions": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Security": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Settings.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Settings.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Specifications/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Specifications.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Specifications.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.TenantManagement.Application.Contracts/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Ddd.Application": "0.3.0", "Volo.Abp.TenantManagement.Domain.Shared": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.TenantManagement.Application.Contracts.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.TenantManagement.Application.Contracts.dll": {"related": ".pdb"}}}, "Volo.Abp.TenantManagement.Domain.Shared/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Localization": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.TenantManagement.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.TenantManagement.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.TextTemplating/4.3.0": {"type": "package", "dependencies": {"Scriban": "3.6.0", "Volo.Abp.Localization.Abstractions": "4.3.0", "Volo.Abp.VirtualFileSystem": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.TextTemplating.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.TextTemplating.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Threading/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Threading.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Threading.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Timing/4.3.0": {"type": "package", "dependencies": {"TimeZoneConverter": "3.4.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Settings": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Timing.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Timing.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Uow/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Uow.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Uow.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Users.Domain.Shared/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.Validation/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Validation.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Validation.dll": {"related": ".pdb"}}}, "Volo.Abp.VirtualFileSystem/4.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Composite": "5.0.0", "Microsoft.Extensions.FileProviders.Embedded": "5.0.5", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll": {"related": ".pdb;.xml"}}}, "TSZ.ServiceBase.FileStorageCenter.Domain.Shared/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "(, )", "Volo.Abp.AuditLogging.Domain.Shared": "(, )", "Volo.Abp.BackgroundJobs.Domain.Shared": "(, )", "Volo.Abp.FeatureManagement.Domain.Shared": "(, )", "Volo.Abp.Identity.Domain.Shared": "(, )", "Volo.Abp.IdentityServer.Domain.Shared": "(, )", "Volo.Abp.Localization": "(, )", "Volo.Abp.ObjectExtending": "(, )", "Volo.Abp.PermissionManagement.Domain.Shared": "(, )", "Volo.Abp.SettingManagement.Domain.Shared": "(, )", "Volo.Abp.TenantManagement.Domain.Shared": "(, )", "Volo.Abp.Validation": "(, )"}, "compile": {"bin/placeholder/TSZ.ServiceBase.FileStorageCenter.Domain.Shared.dll": {}}, "runtime": {"bin/placeholder/TSZ.ServiceBase.FileStorageCenter.Domain.Shared.dll": {}}}}}, "libraries": {"AlibabaCloud.EndpointUtil/0.1.1": {"sha512": "p/vsdJoVIfc1QREW4JX1tpzKdZZcFdw6/qfrylfcFXc0e2BDMQ2kPrv3nkyr2u+p4BF0PmOYl4EDqRtqLiBc+g==", "type": "package", "path": "alibabacloud.endpointutil/0.1.1", "files": [".nupkg.metadata", ".signature.p7s", "alibabacloud.endpointutil.0.1.1.nupkg.sha512", "alibabacloud.endpointutil.nuspec", "lib/net45/AlibabaCloud.EndpointUtil.dll", "lib/netstandard2.0/AlibabaCloud.EndpointUtil.dll"]}, "AlibabaCloud.OpenApiClient/0.0.6": {"sha512": "its5Bh2i4ZiYobDgF+9vwCIT7zb3ZJpkE2RZiyDqSJ3v+KpIAnGcd27ux8OBe2mvuf0+y5e/1XqyWQNaHWHM3g==", "type": "package", "path": "alibabacloud.openapiclient/0.0.6", "files": [".nupkg.metadata", ".signature.p7s", "alibabacloud.openapiclient.0.0.6.nupkg.sha512", "alibabacloud.openapiclient.nuspec", "lib/net45/AlibabaCloud.OpenApiClient.dll", "lib/netstandard2.0/AlibabaCloud.OpenApiClient.dll"]}, "AlibabaCloud.OpenApiUtil/1.0.3": {"sha512": "MN+tNnqRKrpllzyIb5JeEF7Ub9mK59jR7LZJuaJaHlpcyje2oaGzUCTX9nfwo6myUmuI/8bp6M67PHuBjGFt9w==", "type": "package", "path": "alibabacloud.openapiutil/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "alibabacloud.openapiutil.1.0.3.nupkg.sha512", "alibabacloud.openapiutil.nuspec", "lib/net45/AlibabaCloud.OpenApiUtil.dll", "lib/netstandard2.0/AlibabaCloud.OpenApiUtil.dll"]}, "AlibabaCloud.SDK.Sts20150401/1.0.0": {"sha512": "ttcObkeAaW7szjcxDLQQ+pr/M6FI2G2dWV2zPYbz6x2Dj3TUvuL8x5EbmzK5Y+g02Vgjxq7cHp7Bw+pMZVK/+w==", "type": "package", "path": "alibabacloud.sdk.sts20150401/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "alibabacloud.sdk.sts20150401.1.0.0.nupkg.sha512", "alibabacloud.sdk.sts20150401.nuspec", "lib/net45/AlibabaCloud.SDK.Sts20150401.dll", "lib/netstandard2.0/AlibabaCloud.SDK.Sts20150401.dll"]}, "AlibabaCloud.TeaUtil/0.1.9": {"sha512": "kc41l4hfkUnjYSIKvqD2s2yz5FoLAZEeSXD9vC1EfFqPOMc7DyUUCZgNGQeXEUrlg5d85/TQ2BVfQ8wQjfj10A==", "type": "package", "path": "alibabacloud.teautil/0.1.9", "files": [".nupkg.metadata", ".signature.p7s", "alibabacloud.teautil.0.1.9.nupkg.sha512", "alibabacloud.teautil.nuspec", "lib/net45/AlibabaCloud.TeaUtil.dll", "lib/netstandard2.0/AlibabaCloud.TeaUtil.dll"]}, "Aliyun.Credentials/1.3.1": {"sha512": "6j5cCN16nrT9j9lSn2sxm8BJn4R8F/h6+LCDt2R8PUVeoiWf2f6m8ci8I9RL4Gb9xwmdU5hEW1C/4Txf3DKk+A==", "type": "package", "path": "aliyun.credentials/1.3.1", "files": [".nupkg.metadata", ".signature.p7s", "aliyun.credentials.1.3.1.nupkg.sha512", "aliyun.credentials.nuspec", "lib/net45/Aliyun.Credentials.dll", "lib/netstandard2.0/Aliyun.Credentials.dll"]}, "BouncyCastle/1.8.5": {"sha512": "196QWfvglefGd/Cr2C4hIdmA9HYkLFZovxJ7L0BAOFlNn+qlmVxqpxpcKGoC5AF4rOuoUq3ylzLgKZW5j4nmDQ==", "type": "package", "path": "bouncycastle/1.8.5", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "bouncycastle.1.8.5.nupkg.sha512", "bouncycastle.nuspec", "lib/BouncyCastle.Crypto.dll"]}, "ConfigureAwait.Fody/3.3.1": {"sha512": "R9PQYf0AT4RBZcUXm22xWkCpSmNHdTzQ0dOyLIsxIK6dwXH4S9pY/rZdXU/63i8vZvSzZ99sB1kP7xer8MCe6w==", "type": "package", "path": "configureawait.fody/3.3.1", "files": [".nupkg.metadata", ".signature.p7s", "build/ConfigureAwait.Fody.props", "configureawait.fody.3.3.1.nupkg.sha512", "configureawait.fody.nuspec", "lib/net452/ConfigureAwait.dll", "lib/net452/ConfigureAwait.xml", "lib/netstandard2.0/ConfigureAwait.dll", "lib/netstandard2.0/ConfigureAwait.xml", "weaver/ConfigureAwait.Fody.dll", "weaver/ConfigureAwait.Fody.xcf"]}, "Fody/6.0.2": {"sha512": "Oq9dxiHWkw/tPKu9LSmfp6uuCNDZLDkxwHB0sJuwyQRSmvFSB3Ab54WgCQWIsGDO9Z+va9expamqkKpFfdd1sQ==", "type": "package", "path": "fody/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "build/Fody.targets", "fody.6.0.2.nupkg.sha512", "fody.nuspec", "netclassictask/Fody.dll", "netclassictask/FodyCommon.dll", "netclassictask/FodyHelpers.dll", "netclassictask/FodyIsolated.dll", "netclassictask/Mono.Cecil.Pdb.dll", "netclassictask/Mono.Cecil.Pdb.pdb", "netclassictask/Mono.Cecil.Rocks.dll", "netclassictask/Mono.Cecil.Rocks.pdb", "netclassictask/Mono.Cecil.dll", "netclassictask/Mono.Cecil.pdb", "netstandardtask/Fody.dll", "netstandardtask/FodyCommon.dll", "netstandardtask/FodyHelpers.dll", "netstandardtask/FodyIsolated.dll", "netstandardtask/Mono.Cecil.Pdb.dll", "netstandardtask/Mono.Cecil.Pdb.pdb", "netstandardtask/Mono.Cecil.Rocks.dll", "netstandardtask/Mono.Cecil.Rocks.pdb", "netstandardtask/Mono.Cecil.dll", "netstandardtask/Mono.Cecil.pdb"]}, "JetBrains.Annotations/2020.3.0": {"sha512": "FnX06vtxuoZnhZdR6UHt5kJ7HUC/syODfGLnhPDn1x5sXvvepNyCl4jMtPUzJfsPWh7q0Jo+AIYz5xaVbbyikA==", "type": "package", "path": "jetbrains.annotations/2020.3.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "jetbrains.annotations.2020.3.0.nupkg.sha512", "jetbrains.annotations.nuspec", "lib/net20/JetBrains.Annotations.dll", "lib/net20/JetBrains.Annotations.xml", "lib/netstandard1.0/JetBrains.Annotations.deps.json", "lib/netstandard1.0/JetBrains.Annotations.dll", "lib/netstandard1.0/JetBrains.Annotations.xml", "lib/netstandard2.0/JetBrains.Annotations.deps.json", "lib/netstandard2.0/JetBrains.Annotations.dll", "lib/netstandard2.0/JetBrains.Annotations.xml", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.dll", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.xml"]}, "Microsoft.AspNetCore.Authorization/5.0.5": {"sha512": "pQSx1MrLJlKwlEclliO9aUwKxe9EKI2Mff39VE1t5VYOjqsMyI2ujWKGI6XAUsnmC0Bta67GZ1k4DbQZd7tJKg==", "type": "package", "path": "microsoft.aspnetcore.authorization/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.AspNetCore.Authorization.dll", "lib/net461/Microsoft.AspNetCore.Authorization.xml", "lib/net5.0/Microsoft.AspNetCore.Authorization.dll", "lib/net5.0/Microsoft.AspNetCore.Authorization.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.5.0.5.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Hosting.Abstractions/1.0.0": {"sha512": "8r6qOl1jYyC523ZKM1QNl+6ijIoYWELWm0tpEWqtTIOg9DytHJWshB7usgqiuRmfHXM0EUziR6ouFY7iP7Tuzw==", "type": "package", "path": "microsoft.aspnetcore.hosting.abstractions/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.AspNetCore.Hosting.Abstractions.dll", "lib/net451/Microsoft.AspNetCore.Hosting.Abstractions.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Abstractions.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Abstractions.xml", "microsoft.aspnetcore.hosting.abstractions.1.0.0.nupkg.sha512", "microsoft.aspnetcore.hosting.abstractions.nuspec"]}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/1.0.0": {"sha512": "sHZyhQEoW15T9E36rfdm5Ux6a6RZB0KNM79ccf2IplWASqmlRGhX4ydU3dzQRLhkHpLx16fnWOL0KScsO6BevQ==", "type": "package", "path": "microsoft.aspnetcore.hosting.server.abstractions/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll", "lib/net451/Microsoft.AspNetCore.Hosting.Server.Abstractions.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Server.Abstractions.xml", "microsoft.aspnetcore.hosting.server.abstractions.1.0.0.nupkg.sha512", "microsoft.aspnetcore.hosting.server.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http/1.0.0": {"sha512": "c/+eWVWQ8fX5hBHhL1BY4k2n4kVyUnqJLSCj0sTTXwRTU6IKoGbTOUqHT9as8C71Vk54YpAsi/VPmGW7T/ap3A==", "type": "package", "path": "microsoft.aspnetcore.http/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.AspNetCore.Http.dll", "lib/net451/Microsoft.AspNetCore.Http.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Http.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Http.xml", "microsoft.aspnetcore.http.1.0.0.nupkg.sha512", "microsoft.aspnetcore.http.nuspec"]}, "Microsoft.AspNetCore.Http.Abstractions/1.0.0": {"sha512": "OJHlqdJOWKKBfsiVdX4Z4KCNuqvBIu6+1MVKuejRDyHnGyMkNHNoP/dtVzhPqvJXaJg9N4HlD0XNc6GDCFVffg==", "type": "package", "path": "microsoft.aspnetcore.http.abstractions/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/net451/Microsoft.AspNetCore.Http.Abstractions.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Http.Abstractions.xml", "microsoft.aspnetcore.http.abstractions.1.0.0.nupkg.sha512", "microsoft.aspnetcore.http.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http.Extensions/1.0.0": {"sha512": "GlvCPRpnw2jjHLdbGf/C28NQZLMeX1mugv5BS1a3FCQOJYyuwQZil4JwblR0frLyVrUVoJQ7UXRNZIzEVlO5XA==", "type": "package", "path": "microsoft.aspnetcore.http.extensions/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.AspNetCore.Http.Extensions.dll", "lib/net451/Microsoft.AspNetCore.Http.Extensions.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Http.Extensions.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Http.Extensions.xml", "microsoft.aspnetcore.http.extensions.1.0.0.nupkg.sha512", "microsoft.aspnetcore.http.extensions.nuspec"]}, "Microsoft.AspNetCore.Http.Features/1.0.0": {"sha512": "6x7zgfbTo1gL9xMEb7EMO2ES/48bqwnWyfH09z+ubWhnzxdhHls8rtqstPylu5FPD9nid6Vo2pgDm5vufRAy5Q==", "type": "package", "path": "microsoft.aspnetcore.http.features/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.AspNetCore.Http.Features.dll", "lib/net451/Microsoft.AspNetCore.Http.Features.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Http.Features.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Http.Features.xml", "microsoft.aspnetcore.http.features.1.0.0.nupkg.sha512", "microsoft.aspnetcore.http.features.nuspec"]}, "Microsoft.AspNetCore.Metadata/5.0.5": {"sha512": "SD+puPsFLQXWwzoMiXv8lpFGHyZg0gKp3OP2EXg3eRlwJQOMaUGMIqbCdfHGR4MBaUZtqY/tU68H1bzb6+FSxA==", "type": "package", "path": "microsoft.aspnetcore.metadata/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.AspNetCore.Metadata.dll", "lib/net461/Microsoft.AspNetCore.Metadata.xml", "lib/net5.0/Microsoft.AspNetCore.Metadata.dll", "lib/net5.0/Microsoft.AspNetCore.Metadata.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.xml", "microsoft.aspnetcore.metadata.5.0.5.nupkg.sha512", "microsoft.aspnetcore.metadata.nuspec"]}, "Microsoft.AspNetCore.Mvc.Abstractions/1.0.0": {"sha512": "d7KEexDwxSwVeZv+SDbsMRPl2WuKMVckOCp/KTGuI1NJhd/7GvNGW101iRIC3tC/yym0PaajcWwTZNVfjhyoJw==", "type": "package", "path": "microsoft.aspnetcore.mvc.abstractions/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.AspNetCore.Mvc.Abstractions.dll", "lib/net451/Microsoft.AspNetCore.Mvc.Abstractions.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Mvc.Abstractions.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Mvc.Abstractions.xml", "microsoft.aspnetcore.mvc.abstractions.1.0.0.nupkg.sha512", "microsoft.aspnetcore.mvc.abstractions.nuspec"]}, "Microsoft.AspNetCore.Mvc.Core/1.0.0": {"sha512": "tjCOZJheOAKStHs4LIcrLsbF/00wEwSinC+vCFpsmdqGVl3/tX9jnID20E1NlkKOW68DOLBavoC23BWFiHa0JA==", "type": "package", "path": "microsoft.aspnetcore.mvc.core/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.AspNetCore.Mvc.Core.dll", "lib/net451/Microsoft.AspNetCore.Mvc.Core.xml", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Core.dll", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Core.xml", "microsoft.aspnetcore.mvc.core.1.0.0.nupkg.sha512", "microsoft.aspnetcore.mvc.core.nuspec"]}, "Microsoft.AspNetCore.Routing/1.0.0": {"sha512": "NvFvRtYHXWjBbXz5/7F7JDNcdhrE+tG1/Q9R6LmMxFgu8tkl1bqtFZQbMy17FYFkmm8Fn/T81blRGE2nxCbDRA==", "type": "package", "path": "microsoft.aspnetcore.routing/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.AspNetCore.Routing.dll", "lib/net451/Microsoft.AspNetCore.Routing.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Routing.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Routing.xml", "microsoft.aspnetcore.routing.1.0.0.nupkg.sha512", "microsoft.aspnetcore.routing.nuspec"]}, "Microsoft.AspNetCore.Routing.Abstractions/1.0.0": {"sha512": "Ne5CFiD1xCGSHrGICw7PsVnj7gijfkMfsw52AO6ingcUhE01dc87cJPpfGLnY22MIvqn11ECLbNZYmzFp/Rs+A==", "type": "package", "path": "microsoft.aspnetcore.routing.abstractions/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.AspNetCore.Routing.Abstractions.dll", "lib/net451/Microsoft.AspNetCore.Routing.Abstractions.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Routing.Abstractions.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Routing.Abstractions.xml", "microsoft.aspnetcore.routing.abstractions.1.0.0.nupkg.sha512", "microsoft.aspnetcore.routing.abstractions.nuspec"]}, "Microsoft.AspNetCore.WebUtilities/1.0.0": {"sha512": "D0licSnS1JgqQ/gYlN41wXbeYG3dFIdjY781YzMHZ5gBB7kczacshW+H6plZkXRr/cCnAJWGa31o1R8c5GEy/A==", "type": "package", "path": "microsoft.aspnetcore.webutilities/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.AspNetCore.WebUtilities.dll", "lib/net451/Microsoft.AspNetCore.WebUtilities.xml", "lib/netstandard1.3/Microsoft.AspNetCore.WebUtilities.dll", "lib/netstandard1.3/Microsoft.AspNetCore.WebUtilities.xml", "microsoft.aspnetcore.webutilities.1.0.0.nupkg.sha512", "microsoft.aspnetcore.webutilities.nuspec"]}, "Microsoft.CSharp/4.5.0": {"sha512": "kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "type": "package", "path": "microsoft.csharp/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.5.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.DotNet.InternalAbstractions/1.0.0": {"sha512": "AAguUq7YyKk3yDWPoWA8DrLZvURxB/LrDdTn1h5lmPeznkFUpfC3p459w5mQYQE0qpquf/CkSQZ0etiV5vRHFA==", "type": "package", "path": "microsoft.dotnet.internalabstractions/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.DotNet.InternalAbstractions.dll", "lib/netstandard1.3/Microsoft.DotNet.InternalAbstractions.dll", "microsoft.dotnet.internalabstractions.1.0.0.nupkg.sha512", "microsoft.dotnet.internalabstractions.nuspec"]}, "Microsoft.Extensions.Configuration/5.0.0": {"sha512": "LN322qEKHjuVEhhXueTUe7RNePooZmS8aGid5aK2woX3NPjSnONFyKUc6+JknOS6ce6h2tCLfKPTBXE3mN/6Ag==", "type": "package", "path": "microsoft.extensions.configuration/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.dll", "lib/net461/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"sha512": "ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net461/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"sha512": "Of1Irt1+NzWO+yEYkuDh5TpT4On7LKl98Q9iLqCdOZps6XXEWDj3AKtmyvzJPVXZe4apmkJJIiDL7rR1yC+hjQ==", "type": "package", "path": "microsoft.extensions.configuration.binder/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Binder.dll", "lib/net461/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/5.0.0": {"sha512": "OelM+VQdhZ0XMXsEQBq/bt3kFzD+EBGqR4TAgFDRAye0JfvHAaRi+3BxCRcwqUAwDhV0U0HieljBGHlTgYseRA==", "type": "package", "path": "microsoft.extensions.configuration.commandline/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net461/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"sha512": "fqh6y6hAi0Z0fRsb4B/mP9OkKkSlifh5osa+N/YSQ+/S2a//+zYApZMUC1XeP9fdjlgZoPQoZ72Q2eLHyKLddQ==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"sha512": "rRdspYKA18ViPOISwAihhCMbusHsARCOtDMwa23f+BGEdIjpKPlhs3LLjmKlxfhpGXBjIsS0JpXcChjRUN+PAw==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net461/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"sha512": "Pak8ymSUfdzPfBTLHxeOwcR32YDbuVfhnH2hkfOLnJNQd19ItlBdpMjIDY9C5O/nS2Sn9bzDMai0ZrvF7KyY/Q==", "type": "package", "path": "microsoft.extensions.configuration.json/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Json.dll", "lib/net461/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/5.0.0": {"sha512": "+tK3seG68106lN277YWQvqmfyI/89w0uTu/5Gz5VYSUu5TI4mqwsaWLlSmT9Bl1yW/i1Nr06gHJxqaqB5NU9Tw==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "build/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net461/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net461/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyInjection/5.0.1": {"sha512": "//mDNrYeiJ0eh/awFhDFJQzkRVra/njU5Y4fyK7X29g5HScrzbUkKOKlyTtygthcGFt4zNC8G5CFCjb/oizomA==", "type": "package", "path": "microsoft.extensions.dependencyinjection/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.DependencyInjection.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.xml", "lib/net5.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net5.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.5.0.1.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"sha512": "ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyModel/1.0.0": {"sha512": "n55Y2T4qMgCNMrJaqAN+nlG2EH4XL+e9uxIg4vdFsQeF+L8UKxRdD3C35Bt+xk3vO3Zwp3g+6KFq2VPH2COSmg==", "type": "package", "path": "microsoft.extensions.dependencymodel/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll", "microsoft.extensions.dependencymodel.1.0.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec"]}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"sha512": "iuZIiZ3mteEb+nsUqpGXKx2cGF+cv6gWPd5jqQI4hzqdiJ6I94ddLjKhQOuRW1lueHwocIw30xbSHGhQj0zjdQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net461/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.FileProviders.Composite/5.0.0": {"sha512": "0IoXXfkgKpYJB1t2lC0jPXAxuaywRNc9y2Mq96ZZNKBthL38vusa2UK73+Bm6Kq/9a5xNHJS6NhsSN+i5TEtkA==", "type": "package", "path": "microsoft.extensions.fileproviders.composite/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net461/Microsoft.Extensions.FileProviders.Composite.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.xml", "microsoft.extensions.fileproviders.composite.5.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.composite.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.FileProviders.Embedded/1.0.0": {"sha512": "mMCI3/BLXAyBCDneqOI4ohETd0IXjbXZdoiCm1dYdnOdV193ByEOCFQ6/Vn9RVdU5UlC4Nn1P4J5Df7pXG/vGg==", "type": "package", "path": "microsoft.extensions.fileproviders.embedded/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard1.0/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/netstandard1.0/Microsoft.Extensions.FileProviders.Embedded.xml", "microsoft.extensions.fileproviders.embedded.1.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.embedded.nuspec"]}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"sha512": "1rkd8UO2qf21biwO7X0hL9uHP7vtfmdv/NLvKgCRHkdz1XnW8zVQJXyEYiN68WYpExgtVWn55QF0qBzgfh1mGg==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net461/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"sha512": "ArliS8lGk8sWRtrWpqI8yUVYJpRruPjCDT+EIjrgkA/AAPRctlAkRISVZ334chAKktTLzD1+PK8F5IZpGedSqA==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net461/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/5.0.0": {"sha512": "cbUOCePYBl1UhM+N2zmDSUyJ6cODulbtUd9gEzMFIK3RQDtP/gJsE08oLcBSXH3Q1RAQ0ex7OAB3HeTKB9bXpg==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net461/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Localization/5.0.5": {"sha512": "XBX02xG84g6q+sGnUnBLuRHZt+ZfKIKeY+oLsSqSb/0Hy53lmCGiufCpMH4TZVqmpT3xmFb47YKhA4ROt0SwVQ==", "type": "package", "path": "microsoft.extensions.localization/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Localization.dll", "lib/net461/Microsoft.Extensions.Localization.xml", "lib/net5.0/Microsoft.Extensions.Localization.dll", "lib/net5.0/Microsoft.Extensions.Localization.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.xml", "microsoft.extensions.localization.5.0.5.nupkg.sha512", "microsoft.extensions.localization.nuspec"]}, "Microsoft.Extensions.Localization.Abstractions/5.0.5": {"sha512": "NIKR1AE1gt4QfThQCxJnzQIYcp0sDijX61GtkrgDce0kqatAME7oZDnYQAZTYlm/QYXKNYqu+S58BW53QRM7oQ==", "type": "package", "path": "microsoft.extensions.localization.abstractions/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net461/Microsoft.Extensions.Localization.Abstractions.xml", "lib/net5.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net5.0/Microsoft.Extensions.Localization.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.xml", "microsoft.extensions.localization.abstractions.5.0.5.nupkg.sha512", "microsoft.extensions.localization.abstractions.nuspec"]}, "Microsoft.Extensions.Logging/5.0.0": {"sha512": "MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "type": "package", "path": "microsoft.extensions.logging/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.dll", "lib/net461/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.5.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"sha512": "NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "type": "package", "path": "microsoft.extensions.logging.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net461/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.ObjectPool/1.0.0": {"sha512": "BTXoWSTrv/saLlNSg8l41YOoSKeUUanQLykUqRTtiUJz2xxQOCgm4ckPzrdmSK6w0mdjR2h7IrUDGdBF78Z7yg==", "type": "package", "path": "microsoft.extensions.objectpool/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.Extensions.ObjectPool.dll", "lib/net451/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard1.3/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard1.3/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.1.0.0.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.Extensions.Options/5.0.0": {"sha512": "CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "type": "package", "path": "microsoft.extensions.options/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Options.dll", "lib/net461/Microsoft.Extensions.Options.xml", "lib/net5.0/Microsoft.Extensions.Options.dll", "lib/net5.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.5.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"sha512": "280RxNJqOeQqq47aJLy5D9LN61CAWeuRA83gPToQ8B9jl9SNdQ5EXjlfvF66zQI5AXMl+C/3hGnbtIEN+X3mqA==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net461/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.5.0.0.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.PlatformAbstractions/1.0.0": {"sha512": "zyjUzrOmuevOAJpIo3Mt5GmpALVYCVdLZ99keMbmCxxgQH7oxzU58kGHzE6hAgYEiWsdfMJLjVR7r+vSmaJmtg==", "type": "package", "path": "microsoft.extensions.platformabstractions/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.Extensions.PlatformAbstractions.dll", "lib/net451/Microsoft.Extensions.PlatformAbstractions.xml", "lib/netstandard1.3/Microsoft.Extensions.PlatformAbstractions.dll", "lib/netstandard1.3/Microsoft.Extensions.PlatformAbstractions.xml", "microsoft.extensions.platformabstractions.1.0.0.nupkg.sha512", "microsoft.extensions.platformabstractions.nuspec"]}, "Microsoft.Extensions.Primitives/5.0.0": {"sha512": "cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "type": "package", "path": "microsoft.extensions.primitives/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Primitives.dll", "lib/net461/Microsoft.Extensions.Primitives.xml", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.5.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Net.Http.Headers/1.0.0": {"sha512": "1lr92itF1fKR2oEQ6gk1IUsuCgp7UMlf/b1sjlAyuDeUnttj39ra59GQHYpomglJX1UVNpi1/cSBbEsXoNeIhw==", "type": "package", "path": "microsoft.net.http.headers/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard1.1/Microsoft.Net.Http.Headers.dll", "lib/netstandard1.1/Microsoft.Net.Http.Headers.xml", "microsoft.net.http.headers.1.0.0.nupkg.sha512", "microsoft.net.http.headers.nuspec"]}, "Microsoft.NETCore.Platforms/1.1.0": {"sha512": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "type": "package", "path": "microsoft.netcore.platforms/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.Win32.Primitives/4.0.1": {"sha512": "fQnBHO9DgcmkC9dYSJoBqo6sH1VJwJprUHh8F3hbcRlxiQiBUuTntdk8tUwV490OqC2kQUrinGwZyQHTieuXRA==", "type": "package", "path": "microsoft.win32.primitives/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/Microsoft.Win32.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.win32.primitives.4.0.1.nupkg.sha512", "microsoft.win32.primitives.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/de/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/es/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/it/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "Newtonsoft.Json/13.0.1": {"sha512": "ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "type": "package", "path": "newtonsoft.json/13.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.1.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Nito.AsyncEx.Context/5.1.0": {"sha512": "EE7M37c5E/kvulzEkpUR6v1AnK34b2wysOLJHSjl78p/3hL7grte0XCPRqCfLZDwq98AD9GHMTCRfZy7TEeHhw==", "type": "package", "path": "nito.asyncex.context/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.3/Nito.AsyncEx.Context.dll", "lib/netstandard1.3/Nito.AsyncEx.Context.xml", "lib/netstandard2.0/Nito.AsyncEx.Context.dll", "lib/netstandard2.0/Nito.AsyncEx.Context.xml", "nito.asyncex.context.5.1.0.nupkg.sha512", "nito.asyncex.context.nuspec"]}, "Nito.AsyncEx.Coordination/5.1.0": {"sha512": "Nv+oA+cSxidjOImiKcz2FJgMIDxiK0A6xormKmsUklUBjTNqQpjtdJsACMgTQG56PkTHdbMi5QijPTTUsmcCeg==", "type": "package", "path": "nito.asyncex.coordination/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.3/Nito.AsyncEx.Coordination.dll", "lib/netstandard1.3/Nito.AsyncEx.Coordination.xml", "lib/netstandard2.0/Nito.AsyncEx.Coordination.dll", "lib/netstandard2.0/Nito.AsyncEx.Coordination.xml", "nito.asyncex.coordination.5.1.0.nupkg.sha512", "nito.asyncex.coordination.nuspec"]}, "Nito.AsyncEx.Tasks/5.1.0": {"sha512": "tU3Ib4zs8ivM+uS8n7F7ReWZlA3mODyLqwPE+v+WJI94hZ8xLXl+a9npfj/IcmeXo9a6fGKLWkswKQHOeTWqwA==", "type": "package", "path": "nito.asyncex.tasks/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.3/Nito.AsyncEx.Tasks.dll", "lib/netstandard1.3/Nito.AsyncEx.Tasks.xml", "lib/netstandard2.0/Nito.AsyncEx.Tasks.dll", "lib/netstandard2.0/Nito.AsyncEx.Tasks.xml", "nito.asyncex.tasks.5.1.0.nupkg.sha512", "nito.asyncex.tasks.nuspec"]}, "Nito.Collections.Deque/1.1.0": {"sha512": "RXHe531Oaw2IathDr0Q2kbid0iuudBxtgZsfBZ2eUPuFI8I1P7HMiuUeaIefqYykcDYFTDQsFAPAljduIjihLA==", "type": "package", "path": "nito.collections.deque/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.0/Nito.Collections.Deque.dll", "lib/netstandard1.0/Nito.Collections.Deque.xml", "lib/netstandard2.0/Nito.Collections.Deque.dll", "lib/netstandard2.0/Nito.Collections.Deque.xml", "nito.collections.deque.1.1.0.nupkg.sha512", "nito.collections.deque.nuspec"]}, "Nito.Disposables/2.2.0": {"sha512": "QcL+uBwUCEoK8GKp/WzjdCiG8/3G1WLlVNJgLJUNG7bIIVAcEV+Mro4s53VT4Nd8xMSplv0gy+Priw44vRvLaA==", "type": "package", "path": "nito.disposables/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/Nito.Disposables.dll", "lib/net461/Nito.Disposables.xml", "lib/netstandard1.0/Nito.Disposables.dll", "lib/netstandard1.0/Nito.Disposables.xml", "lib/netstandard2.0/Nito.Disposables.dll", "lib/netstandard2.0/Nito.Disposables.xml", "lib/netstandard2.1/Nito.Disposables.dll", "lib/netstandard2.1/Nito.Disposables.xml", "nito.disposables.2.2.0.nupkg.sha512", "nito.disposables.nuspec"]}, "runtime.native.System/4.0.0": {"sha512": "QfS/nQI7k/BLgmLrw7qm7YBoULEvgWnPI+cYsbfCVFTW8Aj+i8JhccxcFMu1RWms0YZzF+UHguNBK4Qn89e2Sg==", "type": "package", "path": "runtime.native.system/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.4.0.0.nupkg.sha512", "runtime.native.system.nuspec"]}, "runtime.native.System.Net.Http/4.0.1": {"sha512": "Nh0UPZx2Vifh8r+J+H2jxifZUD3sBrmolgiFWJd2yiNrxO0xTa6bAw3YwRn1VOiSen/tUXMS31ttNItCZ6lKuA==", "type": "package", "path": "runtime.native.system.net.http/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.net.http.4.0.1.nupkg.sha512", "runtime.native.system.net.http.nuspec"]}, "runtime.native.System.Security.Cryptography/4.0.0": {"sha512": "2CQK0jmO6Eu7ZeMgD+LOFbNJSXHFVQbCJJkEyEwowh1SCgYnrn9W9RykMfpeeVGw7h4IBvYikzpGUlmZTUafJw==", "type": "package", "path": "runtime.native.system.security.cryptography/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.security.cryptography.4.0.0.nupkg.sha512", "runtime.native.system.security.cryptography.nuspec"]}, "Scriban/3.6.0": {"sha512": "DErH4z5gy+KGznkrH6hqox4p9GNcN47EGrEppb1fTlryDTwt4dIr3zNSwxnU/5fDxhW0ZJsZNWzvK2Ox0lir1w==", "type": "package", "path": "scriban/3.6.0", "files": [".nupkg.metadata", ".signature.p7s", "build/Scriban.props", "build/Scriban.targets", "lib/net5.0/Scriban.dll", "lib/net5.0/Scriban.xml", "lib/netstandard2.0/Scriban.dll", "lib/netstandard2.0/Scriban.xml", "scriban.3.6.0.nupkg.sha512", "scriban.nuspec", "scriban.png", "src/Scriban/Functions/ArrayFunctions.cs", "src/Scriban/Functions/BuiltinFunctions.cs", "src/Scriban/Functions/DateTimeFunctions.cs", "src/Scriban/Functions/HtmlFunctions.cs", "src/Scriban/Functions/IncludeFunction.cs", "src/Scriban/Functions/LiquidBuiltinsFunctions.cs", "src/Scriban/Functions/MathFunctions.cs", "src/Scriban/Functions/ObjectFunctions.cs", "src/Scriban/Functions/RegexFunctions.cs", "src/Scriban/Functions/StringFunctions.cs", "src/Scriban/Functions/TimeSpanFunctions.cs", "src/Scriban/Helpers/BoxHelper.cs", "src/Scriban/Helpers/CharHelper.cs", "src/Scriban/Helpers/FastStack.cs", "src/Scriban/Helpers/InlineList.cs", "src/Scriban/Helpers/MethodImplOptionsPortable.cs", "src/Scriban/Helpers/ReflectionHelper.cs", "src/Scriban/Helpers/StringHelper.cs", "src/Scriban/Helpers/ThrowHelper.cs", "src/Scriban/LogMessageBag.cs", "src/Scriban/Parsing/Lexer.cs", "src/Scriban/Parsing/LexerOptions.cs", "src/Scriban/Parsing/LogMessage.cs", "src/Scriban/Parsing/Parser.Expressions.cs", "src/Scriban/Parsing/Parser.Statements.Liquid.cs", "src/Scriban/Parsing/Parser.Statements.Scriban.cs", "src/Scriban/Parsing/Parser.Statements.cs", "src/Scriban/Parsing/Parser.Terminals.cs", "src/Scriban/Parsing/Parser.cs", "src/Scriban/Parsing/ParserOptions.cs", "src/Scriban/Parsing/ScriptLang.cs", "src/Scriban/Parsing/ScriptMode.cs", "src/Scriban/Parsing/SourceSpan.cs", "src/Scriban/Parsing/TextPosition.cs", "src/Scriban/Parsing/Token.cs", "src/Scriban/Parsing/TokenTextAttribute.cs", "src/Scriban/Parsing/TokenType.cs", "src/Scriban/Parsing/TokenTypeExtensions.cs", "src/Scriban/Parsing/Util.cs", "src/Scriban/Runtime/Accessors/ArrayAccessor.cs", "src/Scriban/Runtime/Accessors/DictionaryAccessor.cs", "src/Scriban/Runtime/Accessors/ListAccessor.cs", "src/Scriban/Runtime/Accessors/NullAccessor.cs", "src/Scriban/Runtime/Accessors/PrimitiveAccessor.cs", "src/Scriban/Runtime/Accessors/ScriptObjectAccessor.cs", "src/Scriban/Runtime/Accessors/StringAccessor.cs", "src/Scriban/Runtime/Accessors/TypedObjectAccessor.cs", "src/Scriban/Runtime/CustomFunction.Generated.cs", "src/Scriban/Runtime/DelegateCustomFunction.cs", "src/Scriban/Runtime/DynamicCustomFunction.cs", "src/Scriban/Runtime/EmptyScriptObject.cs", "src/Scriban/Runtime/IListAccessor.cs", "src/Scriban/Runtime/IObjectAccessor.cs", "src/Scriban/Runtime/IScriptCustomFunction.cs", "src/Scriban/Runtime/IScriptObject.cs", "src/Scriban/Runtime/IScriptOutput.cs", "src/Scriban/Runtime/IScriptTransformable.cs", "src/Scriban/Runtime/ITemplateLoader.cs", "src/Scriban/Runtime/MemberFilterDelegate.cs", "src/Scriban/Runtime/MemberRenamerDelegate.cs", "src/Scriban/Runtime/ScriptArray.cs", "src/Scriban/Runtime/ScriptMemberIgnoreAttribute.cs", "src/Scriban/Runtime/ScriptMemberImportFlags.cs", "src/Scriban/Runtime/ScriptObject.cs", "src/Scriban/Runtime/ScriptObjectExtensions.cs", "src/Scriban/Runtime/ScriptPipeArguments.cs", "src/Scriban/Runtime/ScriptRange.cs", "src/Scriban/Runtime/StandardMemberRenamer.cs", "src/Scriban/Runtime/StringBuilderOutput.cs", "src/Scriban/Runtime/TextWriterOutput.cs", "src/Scriban/ScribanAsync.generated.cs", "src/Scriban/ScribanVisitors.generated.cs", "src/Scriban/ScriptPrinter.cs", "src/Scriban/ScriptPrinterOptions.cs", "src/Scriban/Syntax/Expressions/IScriptTerminal.cs", "src/Scriban/Syntax/Expressions/ScriptAnonymousFunction.cs", "src/Scriban/Syntax/Expressions/ScriptArgumentBinary.cs", "src/Scriban/Syntax/Expressions/ScriptArrayInitializerExpression.cs", "src/Scriban/Syntax/Expressions/ScriptAssignExpression.cs", "src/Scriban/Syntax/Expressions/ScriptBinaryExpression.cs", "src/Scriban/Syntax/Expressions/ScriptBinaryOperator.cs", "src/Scriban/Syntax/Expressions/ScriptConditionalExpression.cs", "src/Scriban/Syntax/Expressions/ScriptExpression.cs", "src/Scriban/Syntax/Expressions/ScriptFunctionCall.cs", "src/Scriban/Syntax/Expressions/ScriptIndexerExpression.cs", "src/Scriban/Syntax/Expressions/ScriptIsEmptyExpression.cs", "src/Scriban/Syntax/Expressions/ScriptLiteral.cs", "src/Scriban/Syntax/Expressions/ScriptMemberExpression.cs", "src/Scriban/Syntax/Expressions/ScriptNamedArgument.cs", "src/Scriban/Syntax/Expressions/ScriptNestedExpression.cs", "src/Scriban/Syntax/Expressions/ScriptObjectInitializerExpression.cs", "src/Scriban/Syntax/Expressions/ScriptObjectMember.cs", "src/Scriban/Syntax/Expressions/ScriptPipeCall.cs", "src/Scriban/Syntax/Expressions/ScriptThisExpression.cs", "src/Scriban/Syntax/Expressions/ScriptUnaryExpression.cs", "src/Scriban/Syntax/Expressions/ScriptUnaryOperator.cs", "src/Scriban/Syntax/Expressions/ScriptVariable.cs", "src/Scriban/Syntax/Expressions/ScriptVariableScope.cs", "src/Scriban/Syntax/IScriptConvertibleFrom.cs", "src/Scriban/Syntax/IScriptConvertibleTo.cs", "src/Scriban/Syntax/IScriptCustomBinaryOperation.cs", "src/Scriban/Syntax/IScriptCustomImplicitMultiplyPrecedence.cs", "src/Scriban/Syntax/IScriptCustomType.cs", "src/Scriban/Syntax/IScriptCustomTypeInfo.cs", "src/Scriban/Syntax/IScriptCustomUnaryOperation.cs", "src/Scriban/Syntax/IScriptNamedArgumentContainer.cs", "src/Scriban/Syntax/IScriptVariablePath.cs", "src/Scriban/Syntax/IScriptVisitorContext.cs", "src/Scriban/Syntax/ScientificFunctionCallRewriter.cs", "src/Scriban/Syntax/ScriptArgumentException.cs", "src/Scriban/Syntax/ScriptCloner.cs", "src/<PERSON>riban/Syntax/ScriptFormatter.cs", "src/<PERSON>riban/Syntax/ScriptFormatterExtensions.cs", "src/Scriban/Syntax/ScriptFormatterFlags.cs", "src/Scriban/Syntax/ScriptFormatterOptions.cs", "src/Scriban/Syntax/ScriptFrontMatter.cs", "src/Scriban/Syntax/ScriptIdentifier.cs", "src/Scriban/Syntax/ScriptKeyword.cs", "src/Scriban/Syntax/ScriptList.cs", "src/Scriban/Syntax/ScriptNode.cs", "src/Scriban/Syntax/ScriptNodeExtensions.cs", "src/Scriban/Syntax/ScriptPage.cs", "src/Scriban/Syntax/ScriptParameterContainerExtensions.cs", "src/Scriban/Syntax/ScriptRewriter.cs", "src/Scriban/Syntax/ScriptRuntimeException.cs", "src/Scriban/Syntax/ScriptStringSlice.cs", "src/Scriban/Syntax/ScriptSyntaxAttribute.cs", "src/Scriban/Syntax/ScriptToken.cs", "src/Scriban/Syntax/ScriptTrivia.cs", "src/Scriban/Syntax/ScriptTriviaType.cs", "src/Scriban/Syntax/ScriptTriviaTypeExtensions.cs", "src/Scriban/Syntax/ScriptTrivias.cs", "src/Scriban/Syntax/ScriptVerbatim.cs", "src/Scriban/Syntax/ScriptVisitor.cs", "src/Scriban/Syntax/Statements/ScriptBlockStatement.cs", "src/Scriban/Syntax/Statements/ScriptBreakStatement.cs", "src/Scriban/Syntax/Statements/ScriptCaptureStatement.cs", "src/Scriban/Syntax/Statements/ScriptCaseStatement.cs", "src/Scriban/Syntax/Statements/ScriptConditionStatement.cs", "src/Scriban/Syntax/Statements/ScriptContinueStatement.cs", "src/Scriban/Syntax/Statements/ScriptElseStatement.cs", "src/Scriban/Syntax/Statements/ScriptEndStatement.cs", "src/Scriban/Syntax/Statements/ScriptEscapeStatement.cs", "src/Scriban/Syntax/Statements/ScriptExpressionStatement.cs", "src/Scriban/Syntax/Statements/ScriptFlowState.cs", "src/Scriban/Syntax/Statements/ScriptForStatement.cs", "src/Scriban/Syntax/Statements/ScriptFunction.cs", "src/Scriban/Syntax/Statements/ScriptIfStatement.cs", "src/Scriban/Syntax/Statements/ScriptImportStatement.cs", "src/Scriban/Syntax/Statements/ScriptLoopStatementBase.cs", "src/Scriban/Syntax/Statements/ScriptNopStatement.cs", "src/Scriban/Syntax/Statements/ScriptParameter.cs", "src/Scriban/Syntax/Statements/ScriptRawStatement.cs", "src/Scriban/Syntax/Statements/ScriptReadOnlyStatement.cs", "src/Scriban/Syntax/Statements/ScriptReturnStatement.cs", "src/Scriban/Syntax/Statements/ScriptStatement.cs", "src/Scriban/Syntax/Statements/ScriptTableRowStatement.cs", "src/Scriban/Syntax/Statements/ScriptWhenStatement.cs", "src/Scriban/Syntax/Statements/ScriptWhileStatement.cs", "src/Scriban/Syntax/Statements/ScriptWhitespaceMode.cs", "src/Scriban/Syntax/Statements/ScriptWithStatement.cs", "src/Scriban/Syntax/Statements/ScriptWrapStatement.cs", "src/Scriban/Template.cs", "src/Scriban/TemplateContext.Helpers.cs", "src/Scriban/TemplateContext.Variables.cs", "src/Scriban/TemplateContext.cs"]}, "System.AppContext/4.1.0": {"sha512": "3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "type": "package", "path": "system.appcontext/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.AppContext.dll", "lib/net463/System.AppContext.dll", "lib/netcore50/System.AppContext.dll", "lib/netstandard1.6/System.AppContext.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.AppContext.dll", "ref/net463/System.AppContext.dll", "ref/netstandard/_._", "ref/netstandard1.3/System.AppContext.dll", "ref/netstandard1.3/System.AppContext.xml", "ref/netstandard1.3/de/System.AppContext.xml", "ref/netstandard1.3/es/System.AppContext.xml", "ref/netstandard1.3/fr/System.AppContext.xml", "ref/netstandard1.3/it/System.AppContext.xml", "ref/netstandard1.3/ja/System.AppContext.xml", "ref/netstandard1.3/ko/System.AppContext.xml", "ref/netstandard1.3/ru/System.AppContext.xml", "ref/netstandard1.3/zh-hans/System.AppContext.xml", "ref/netstandard1.3/zh-hant/System.AppContext.xml", "ref/netstandard1.6/System.AppContext.dll", "ref/netstandard1.6/System.AppContext.xml", "ref/netstandard1.6/de/System.AppContext.xml", "ref/netstandard1.6/es/System.AppContext.xml", "ref/netstandard1.6/fr/System.AppContext.xml", "ref/netstandard1.6/it/System.AppContext.xml", "ref/netstandard1.6/ja/System.AppContext.xml", "ref/netstandard1.6/ko/System.AppContext.xml", "ref/netstandard1.6/ru/System.AppContext.xml", "ref/netstandard1.6/zh-hans/System.AppContext.xml", "ref/netstandard1.6/zh-hant/System.AppContext.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.AppContext.dll", "system.appcontext.4.1.0.nupkg.sha512", "system.appcontext.nuspec"]}, "System.Buffers/4.0.0": {"sha512": "msXumHfjjURSkvxUjYuq4N2ghHoRi2VpXcKMA7gK6ujQfU3vGpl+B6ld0ATRg+FZFpRyA6PgEPA+VlIkTeNf2w==", "type": "package", "path": "system.buffers/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.1/.xml", "lib/netstandard1.1/System.Buffers.dll", "system.buffers.4.0.0.nupkg.sha512", "system.buffers.nuspec"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.Concurrent/4.0.12": {"sha512": "2gBcbb3drMLgxlI0fBfxMA31ec6AEyYCHygGse4vxceJan8mRIWeKJ24BFzN7+bi/NFTgdIgufzb94LWO5EERQ==", "type": "package", "path": "system.collections.concurrent/4.0.12", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Collections.Concurrent.dll", "lib/netstandard1.3/System.Collections.Concurrent.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.Concurrent.dll", "ref/netcore50/System.Collections.Concurrent.xml", "ref/netcore50/de/System.Collections.Concurrent.xml", "ref/netcore50/es/System.Collections.Concurrent.xml", "ref/netcore50/fr/System.Collections.Concurrent.xml", "ref/netcore50/it/System.Collections.Concurrent.xml", "ref/netcore50/ja/System.Collections.Concurrent.xml", "ref/netcore50/ko/System.Collections.Concurrent.xml", "ref/netcore50/ru/System.Collections.Concurrent.xml", "ref/netcore50/zh-hans/System.Collections.Concurrent.xml", "ref/netcore50/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.1/System.Collections.Concurrent.dll", "ref/netstandard1.1/System.Collections.Concurrent.xml", "ref/netstandard1.1/de/System.Collections.Concurrent.xml", "ref/netstandard1.1/es/System.Collections.Concurrent.xml", "ref/netstandard1.1/fr/System.Collections.Concurrent.xml", "ref/netstandard1.1/it/System.Collections.Concurrent.xml", "ref/netstandard1.1/ja/System.Collections.Concurrent.xml", "ref/netstandard1.1/ko/System.Collections.Concurrent.xml", "ref/netstandard1.1/ru/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.3/System.Collections.Concurrent.dll", "ref/netstandard1.3/System.Collections.Concurrent.xml", "ref/netstandard1.3/de/System.Collections.Concurrent.xml", "ref/netstandard1.3/es/System.Collections.Concurrent.xml", "ref/netstandard1.3/fr/System.Collections.Concurrent.xml", "ref/netstandard1.3/it/System.Collections.Concurrent.xml", "ref/netstandard1.3/ja/System.Collections.Concurrent.xml", "ref/netstandard1.3/ko/System.Collections.Concurrent.xml", "ref/netstandard1.3/ru/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hant/System.Collections.Concurrent.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.concurrent.4.0.12.nupkg.sha512", "system.collections.concurrent.nuspec"]}, "System.Collections.Immutable/5.0.0": {"sha512": "FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "type": "package", "path": "system.collections.immutable/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Collections.Immutable.dll", "lib/net461/System.Collections.Immutable.xml", "lib/netstandard1.0/System.Collections.Immutable.dll", "lib/netstandard1.0/System.Collections.Immutable.xml", "lib/netstandard1.3/System.Collections.Immutable.dll", "lib/netstandard1.3/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.dll", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.xml", "system.collections.immutable.5.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections.NonGeneric/4.0.1": {"sha512": "hMxFT2RhhlffyCdKLDXjx8WEC5JfCvNozAZxCablAuFRH74SCV4AgzE8yJCh/73bFnEoZgJ9MJmkjQ0dJmnKqA==", "type": "package", "path": "system.collections.nongeneric/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Collections.NonGeneric.dll", "lib/netstandard1.3/System.Collections.NonGeneric.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.xml", "ref/netstandard1.3/de/System.Collections.NonGeneric.xml", "ref/netstandard1.3/es/System.Collections.NonGeneric.xml", "ref/netstandard1.3/fr/System.Collections.NonGeneric.xml", "ref/netstandard1.3/it/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ja/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ko/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ru/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hans/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hant/System.Collections.NonGeneric.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.nongeneric.4.0.1.nupkg.sha512", "system.collections.nongeneric.nuspec"]}, "System.Collections.Specialized/4.0.1": {"sha512": "/HKQyVP0yH1I0YtK7KJL/28snxHNH/bi+0lgk/+MbURF6ULhAE31MDI+NZDerNWu264YbxklXCCygISgm+HMug==", "type": "package", "path": "system.collections.specialized/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Collections.Specialized.dll", "lib/netstandard1.3/System.Collections.Specialized.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Collections.Specialized.dll", "ref/netstandard1.3/System.Collections.Specialized.dll", "ref/netstandard1.3/System.Collections.Specialized.xml", "ref/netstandard1.3/de/System.Collections.Specialized.xml", "ref/netstandard1.3/es/System.Collections.Specialized.xml", "ref/netstandard1.3/fr/System.Collections.Specialized.xml", "ref/netstandard1.3/it/System.Collections.Specialized.xml", "ref/netstandard1.3/ja/System.Collections.Specialized.xml", "ref/netstandard1.3/ko/System.Collections.Specialized.xml", "ref/netstandard1.3/ru/System.Collections.Specialized.xml", "ref/netstandard1.3/zh-hans/System.Collections.Specialized.xml", "ref/netstandard1.3/zh-hant/System.Collections.Specialized.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.specialized.4.0.1.nupkg.sha512", "system.collections.specialized.nuspec"]}, "System.ComponentModel/4.0.1": {"sha512": "oBZFnm7seFiVfugsIyOvQCWobNZs7FzqDV/B7tx20Ep/l3UUFCPDkdTnCNaJZTU27zjeODmy2C/cP60u3D4c9w==", "type": "package", "path": "system.componentmodel/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ComponentModel.dll", "lib/netstandard1.3/System.ComponentModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ComponentModel.dll", "ref/netcore50/System.ComponentModel.xml", "ref/netcore50/de/System.ComponentModel.xml", "ref/netcore50/es/System.ComponentModel.xml", "ref/netcore50/fr/System.ComponentModel.xml", "ref/netcore50/it/System.ComponentModel.xml", "ref/netcore50/ja/System.ComponentModel.xml", "ref/netcore50/ko/System.ComponentModel.xml", "ref/netcore50/ru/System.ComponentModel.xml", "ref/netcore50/zh-hans/System.ComponentModel.xml", "ref/netcore50/zh-hant/System.ComponentModel.xml", "ref/netstandard1.0/System.ComponentModel.dll", "ref/netstandard1.0/System.ComponentModel.xml", "ref/netstandard1.0/de/System.ComponentModel.xml", "ref/netstandard1.0/es/System.ComponentModel.xml", "ref/netstandard1.0/fr/System.ComponentModel.xml", "ref/netstandard1.0/it/System.ComponentModel.xml", "ref/netstandard1.0/ja/System.ComponentModel.xml", "ref/netstandard1.0/ko/System.ComponentModel.xml", "ref/netstandard1.0/ru/System.ComponentModel.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.4.0.1.nupkg.sha512", "system.componentmodel.nuspec"]}, "System.ComponentModel.Annotations/5.0.0": {"sha512": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "type": "package", "path": "system.componentmodel.annotations/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.5.0.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Primitives/4.1.0": {"sha512": "sc/7eVCdxPrp3ljpgTKVaQGUXiW05phNWvtv/m2kocXqrUQvTVWKou1Edas2aDjTThLPZOxPYIGNb/HN0QjURg==", "type": "package", "path": "system.componentmodel.primitives/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.ComponentModel.Primitives.dll", "lib/netstandard1.0/System.ComponentModel.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.ComponentModel.Primitives.dll", "ref/netstandard1.0/System.ComponentModel.Primitives.dll", "ref/netstandard1.0/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/de/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/es/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/fr/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/it/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ja/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ko/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ru/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.primitives.4.1.0.nupkg.sha512", "system.componentmodel.primitives.nuspec"]}, "System.ComponentModel.TypeConverter/4.1.0": {"sha512": "MnDAlaeJZy9pdB5ZdOlwdxfpI+LJQ6e0hmH7d2+y2LkiD8DRJynyDYl4Xxf3fWFm7SbEwBZh4elcfzONQLOoQw==", "type": "package", "path": "system.componentmodel.typeconverter/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.ComponentModel.TypeConverter.dll", "lib/net462/System.ComponentModel.TypeConverter.dll", "lib/netstandard1.0/System.ComponentModel.TypeConverter.dll", "lib/netstandard1.5/System.ComponentModel.TypeConverter.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.ComponentModel.TypeConverter.dll", "ref/net462/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.0/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.0/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/de/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/es/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/fr/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/it/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ja/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ko/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ru/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.5/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/de/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/es/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/fr/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/it/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ja/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ko/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ru/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/zh-hans/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/zh-hant/System.ComponentModel.TypeConverter.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.typeconverter.4.1.0.nupkg.sha512", "system.componentmodel.typeconverter.nuspec"]}, "System.Diagnostics.Contracts/4.0.1": {"sha512": "HvQQjy712vnlpPxaloZYkuE78Gn353L0SJLJVeLcNASeg9c4qla2a1Xq8I7B3jZoDzKPtHTkyVO7AZ5tpeQGuA==", "type": "package", "path": "system.diagnostics.contracts/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Diagnostics.Contracts.dll", "lib/netstandard1.0/System.Diagnostics.Contracts.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Contracts.dll", "ref/netcore50/System.Diagnostics.Contracts.xml", "ref/netcore50/de/System.Diagnostics.Contracts.xml", "ref/netcore50/es/System.Diagnostics.Contracts.xml", "ref/netcore50/fr/System.Diagnostics.Contracts.xml", "ref/netcore50/it/System.Diagnostics.Contracts.xml", "ref/netcore50/ja/System.Diagnostics.Contracts.xml", "ref/netcore50/ko/System.Diagnostics.Contracts.xml", "ref/netcore50/ru/System.Diagnostics.Contracts.xml", "ref/netcore50/zh-hans/System.Diagnostics.Contracts.xml", "ref/netcore50/zh-hant/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/System.Diagnostics.Contracts.dll", "ref/netstandard1.0/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/de/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/es/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/fr/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/it/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/ja/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/ko/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/ru/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Contracts.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Diagnostics.Contracts.dll", "system.diagnostics.contracts.4.0.1.nupkg.sha512", "system.diagnostics.contracts.nuspec"]}, "System.Diagnostics.Debug/4.3.0": {"sha512": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "type": "package", "path": "system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.3.0.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Diagnostics.DiagnosticSource/4.0.0": {"sha512": "YKglnq4BMTJxfcr6nuT08g+yJ0UxdePIHxosiLuljuHIUR6t4KhFsyaHOaOc1Ofqp0PUvJ0EmcgiEz6T7vEx3w==", "type": "package", "path": "system.diagnostics.diagnosticsource/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net46/System.Diagnostics.DiagnosticSource.dll", "lib/net46/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.4.0.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec"]}, "System.Diagnostics.Tracing/4.1.0": {"sha512": "vDN1PoMZCkkdNjvZLql592oYJZgS7URcJzJ7bxeBgGtx5UtR5leNm49VmfHGqIffX4FKacHbI3H6UyNSHQknBg==", "type": "package", "path": "system.diagnostics.tracing/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Diagnostics.Tracing.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.xml", "ref/netcore50/de/System.Diagnostics.Tracing.xml", "ref/netcore50/es/System.Diagnostics.Tracing.xml", "ref/netcore50/fr/System.Diagnostics.Tracing.xml", "ref/netcore50/it/System.Diagnostics.Tracing.xml", "ref/netcore50/ja/System.Diagnostics.Tracing.xml", "ref/netcore50/ko/System.Diagnostics.Tracing.xml", "ref/netcore50/ru/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/System.Diagnostics.Tracing.dll", "ref/netstandard1.1/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/System.Diagnostics.Tracing.dll", "ref/netstandard1.2/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/System.Diagnostics.Tracing.dll", "ref/netstandard1.3/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/System.Diagnostics.Tracing.dll", "ref/netstandard1.5/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hant/System.Diagnostics.Tracing.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.tracing.4.1.0.nupkg.sha512", "system.diagnostics.tracing.nuspec"]}, "System.Dynamic.Runtime/4.0.11": {"sha512": "db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A==", "type": "package", "path": "system.dynamic.runtime/4.0.11", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Dynamic.Runtime.dll", "lib/netstandard1.3/System.Dynamic.Runtime.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Dynamic.Runtime.dll", "ref/netcore50/System.Dynamic.Runtime.xml", "ref/netcore50/de/System.Dynamic.Runtime.xml", "ref/netcore50/es/System.Dynamic.Runtime.xml", "ref/netcore50/fr/System.Dynamic.Runtime.xml", "ref/netcore50/it/System.Dynamic.Runtime.xml", "ref/netcore50/ja/System.Dynamic.Runtime.xml", "ref/netcore50/ko/System.Dynamic.Runtime.xml", "ref/netcore50/ru/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hans/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.0/System.Dynamic.Runtime.dll", "ref/netstandard1.0/System.Dynamic.Runtime.xml", "ref/netstandard1.0/de/System.Dynamic.Runtime.xml", "ref/netstandard1.0/es/System.Dynamic.Runtime.xml", "ref/netstandard1.0/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.0/it/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.3/System.Dynamic.Runtime.dll", "ref/netstandard1.3/System.Dynamic.Runtime.xml", "ref/netstandard1.3/de/System.Dynamic.Runtime.xml", "ref/netstandard1.3/es/System.Dynamic.Runtime.xml", "ref/netstandard1.3/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.3/it/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Dynamic.Runtime.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Dynamic.Runtime.dll", "system.dynamic.runtime.4.0.11.nupkg.sha512", "system.dynamic.runtime.nuspec"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.Globalization.Calendars/4.0.1": {"sha512": "L1c6IqeQ88vuzC1P81JeHmHA8mxq8a18NUBNXnIY/BVb+TCyAaGIFbhpZt60h9FJNmisymoQkHEFSE9Vslja1Q==", "type": "package", "path": "system.globalization.calendars/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Globalization.Calendars.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Globalization.Calendars.dll", "ref/netstandard1.3/System.Globalization.Calendars.dll", "ref/netstandard1.3/System.Globalization.Calendars.xml", "ref/netstandard1.3/de/System.Globalization.Calendars.xml", "ref/netstandard1.3/es/System.Globalization.Calendars.xml", "ref/netstandard1.3/fr/System.Globalization.Calendars.xml", "ref/netstandard1.3/it/System.Globalization.Calendars.xml", "ref/netstandard1.3/ja/System.Globalization.Calendars.xml", "ref/netstandard1.3/ko/System.Globalization.Calendars.xml", "ref/netstandard1.3/ru/System.Globalization.Calendars.xml", "ref/netstandard1.3/zh-hans/System.Globalization.Calendars.xml", "ref/netstandard1.3/zh-hant/System.Globalization.Calendars.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.calendars.4.0.1.nupkg.sha512", "system.globalization.calendars.nuspec"]}, "System.Globalization.Extensions/4.0.1": {"sha512": "KKo23iKeOaIg61SSXwjANN7QYDr/3op3OWGGzDzz7mypx0Za0fZSeG0l6cco8Ntp8YMYkIQcAqlk8yhm5/Uhcg==", "type": "package", "path": "system.globalization.extensions/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Globalization.Extensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.xml", "ref/netstandard1.3/de/System.Globalization.Extensions.xml", "ref/netstandard1.3/es/System.Globalization.Extensions.xml", "ref/netstandard1.3/fr/System.Globalization.Extensions.xml", "ref/netstandard1.3/it/System.Globalization.Extensions.xml", "ref/netstandard1.3/ja/System.Globalization.Extensions.xml", "ref/netstandard1.3/ko/System.Globalization.Extensions.xml", "ref/netstandard1.3/ru/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Globalization.Extensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll", "runtimes/win/lib/net46/System.Globalization.Extensions.dll", "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll", "system.globalization.extensions.4.0.1.nupkg.sha512", "system.globalization.extensions.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.FileSystem/4.0.1": {"sha512": "IBErlVq5jOggAD69bg1t0pJcHaDbJbWNUZTPI96fkYWzwYbN6D9wRHMULLDd9dHsl7C2YsxXL31LMfPI1SWt8w==", "type": "package", "path": "system.io.filesystem/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.xml", "ref/netstandard1.3/de/System.IO.FileSystem.xml", "ref/netstandard1.3/es/System.IO.FileSystem.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.xml", "ref/netstandard1.3/it/System.IO.FileSystem.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.4.0.1.nupkg.sha512", "system.io.filesystem.nuspec"]}, "System.IO.FileSystem.Primitives/4.0.1": {"sha512": "kWkKD203JJKxJeE74p8aF8y4Qc9r9WQx4C0cHzHPrY3fv/L/IhWnyCHaFJ3H1QPOH6A93whlQ2vG5nHlBDvzWQ==", "type": "package", "path": "system.io.filesystem.primitives/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.Primitives.dll", "lib/netstandard1.3/System.IO.FileSystem.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/de/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/es/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/it/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.primitives.4.0.1.nupkg.sha512", "system.io.filesystem.primitives.nuspec"]}, "System.Linq/4.3.0": {"sha512": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "type": "package", "path": "system.linq/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.3.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Linq.Dynamic.Core/1.2.9": {"sha512": "GjR4CCtIkiJSU6N8cidG4aa0ph+HBzFOq3uhLybuq4zjlKy3hjDrGbcEUeBiGpBmUrnUhTAJ5SCZGDoZS7d9SA==", "type": "package", "path": "system.linq.dynamic.core/1.2.9", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/System.Linq.Dynamic.Core.dll", "lib/net35/System.Linq.Dynamic.Core.pdb", "lib/net35/System.Linq.Dynamic.Core.xml", "lib/net40/System.Linq.Dynamic.Core.dll", "lib/net40/System.Linq.Dynamic.Core.pdb", "lib/net40/System.Linq.Dynamic.Core.xml", "lib/net45/System.Linq.Dynamic.Core.dll", "lib/net45/System.Linq.Dynamic.Core.pdb", "lib/net45/System.Linq.Dynamic.Core.xml", "lib/net46/System.Linq.Dynamic.Core.dll", "lib/net46/System.Linq.Dynamic.Core.pdb", "lib/net46/System.Linq.Dynamic.Core.xml", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.xml", "lib/netstandard1.3/System.Linq.Dynamic.Core.dll", "lib/netstandard1.3/System.Linq.Dynamic.Core.pdb", "lib/netstandard1.3/System.Linq.Dynamic.Core.xml", "lib/netstandard2.0/System.Linq.Dynamic.Core.dll", "lib/netstandard2.0/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.0/System.Linq.Dynamic.Core.xml", "lib/uap10.0.10240/System.Linq.Dynamic.Core.dll", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pdb", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pri", "lib/uap10.0.10240/System.Linq.Dynamic.Core.xml", "system.linq.dynamic.core.1.2.9.nupkg.sha512", "system.linq.dynamic.core.nuspec"]}, "System.Linq.Expressions/4.3.0": {"sha512": "PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "type": "package", "path": "system.linq.expressions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.Expressions.dll", "lib/netcore50/System.Linq.Expressions.dll", "lib/netstandard1.6/System.Linq.Expressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.xml", "ref/netcore50/de/System.Linq.Expressions.xml", "ref/netcore50/es/System.Linq.Expressions.xml", "ref/netcore50/fr/System.Linq.Expressions.xml", "ref/netcore50/it/System.Linq.Expressions.xml", "ref/netcore50/ja/System.Linq.Expressions.xml", "ref/netcore50/ko/System.Linq.Expressions.xml", "ref/netcore50/ru/System.Linq.Expressions.xml", "ref/netcore50/zh-hans/System.Linq.Expressions.xml", "ref/netcore50/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.0/System.Linq.Expressions.dll", "ref/netstandard1.0/System.Linq.Expressions.xml", "ref/netstandard1.0/de/System.Linq.Expressions.xml", "ref/netstandard1.0/es/System.Linq.Expressions.xml", "ref/netstandard1.0/fr/System.Linq.Expressions.xml", "ref/netstandard1.0/it/System.Linq.Expressions.xml", "ref/netstandard1.0/ja/System.Linq.Expressions.xml", "ref/netstandard1.0/ko/System.Linq.Expressions.xml", "ref/netstandard1.0/ru/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.3/System.Linq.Expressions.dll", "ref/netstandard1.3/System.Linq.Expressions.xml", "ref/netstandard1.3/de/System.Linq.Expressions.xml", "ref/netstandard1.3/es/System.Linq.Expressions.xml", "ref/netstandard1.3/fr/System.Linq.Expressions.xml", "ref/netstandard1.3/it/System.Linq.Expressions.xml", "ref/netstandard1.3/ja/System.Linq.Expressions.xml", "ref/netstandard1.3/ko/System.Linq.Expressions.xml", "ref/netstandard1.3/ru/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.6/System.Linq.Expressions.dll", "ref/netstandard1.6/System.Linq.Expressions.xml", "ref/netstandard1.6/de/System.Linq.Expressions.xml", "ref/netstandard1.6/es/System.Linq.Expressions.xml", "ref/netstandard1.6/fr/System.Linq.Expressions.xml", "ref/netstandard1.6/it/System.Linq.Expressions.xml", "ref/netstandard1.6/ja/System.Linq.Expressions.xml", "ref/netstandard1.6/ko/System.Linq.Expressions.xml", "ref/netstandard1.6/ru/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hant/System.Linq.Expressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Linq.Expressions.dll", "system.linq.expressions.4.3.0.nupkg.sha512", "system.linq.expressions.nuspec"]}, "System.Linq.Queryable/4.3.0": {"sha512": "In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "type": "package", "path": "system.linq.queryable/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Linq.Queryable.dll", "lib/netstandard1.3/System.Linq.Queryable.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/net45/_._", "ref/netcore50/System.Linq.Queryable.dll", "ref/netcore50/System.Linq.Queryable.xml", "ref/netcore50/de/System.Linq.Queryable.xml", "ref/netcore50/es/System.Linq.Queryable.xml", "ref/netcore50/fr/System.Linq.Queryable.xml", "ref/netcore50/it/System.Linq.Queryable.xml", "ref/netcore50/ja/System.Linq.Queryable.xml", "ref/netcore50/ko/System.Linq.Queryable.xml", "ref/netcore50/ru/System.Linq.Queryable.xml", "ref/netcore50/zh-hans/System.Linq.Queryable.xml", "ref/netcore50/zh-hant/System.Linq.Queryable.xml", "ref/netstandard1.0/System.Linq.Queryable.dll", "ref/netstandard1.0/System.Linq.Queryable.xml", "ref/netstandard1.0/de/System.Linq.Queryable.xml", "ref/netstandard1.0/es/System.Linq.Queryable.xml", "ref/netstandard1.0/fr/System.Linq.Queryable.xml", "ref/netstandard1.0/it/System.Linq.Queryable.xml", "ref/netstandard1.0/ja/System.Linq.Queryable.xml", "ref/netstandard1.0/ko/System.Linq.Queryable.xml", "ref/netstandard1.0/ru/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hans/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hant/System.Linq.Queryable.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.queryable.4.3.0.nupkg.sha512", "system.linq.queryable.nuspec"]}, "System.Net.Primitives/4.0.11": {"sha512": "hVvfl4405DRjA2408luZekbPhplJK03j2Y2lSfMlny7GHXlkByw1iLnc9mgKW0GdQn73vvMcWrWewAhylXA4Nw==", "type": "package", "path": "system.net.primitives/4.0.11", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Net.Primitives.dll", "ref/netcore50/System.Net.Primitives.xml", "ref/netcore50/de/System.Net.Primitives.xml", "ref/netcore50/es/System.Net.Primitives.xml", "ref/netcore50/fr/System.Net.Primitives.xml", "ref/netcore50/it/System.Net.Primitives.xml", "ref/netcore50/ja/System.Net.Primitives.xml", "ref/netcore50/ko/System.Net.Primitives.xml", "ref/netcore50/ru/System.Net.Primitives.xml", "ref/netcore50/zh-hans/System.Net.Primitives.xml", "ref/netcore50/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.0/System.Net.Primitives.dll", "ref/netstandard1.0/System.Net.Primitives.xml", "ref/netstandard1.0/de/System.Net.Primitives.xml", "ref/netstandard1.0/es/System.Net.Primitives.xml", "ref/netstandard1.0/fr/System.Net.Primitives.xml", "ref/netstandard1.0/it/System.Net.Primitives.xml", "ref/netstandard1.0/ja/System.Net.Primitives.xml", "ref/netstandard1.0/ko/System.Net.Primitives.xml", "ref/netstandard1.0/ru/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.1/System.Net.Primitives.dll", "ref/netstandard1.1/System.Net.Primitives.xml", "ref/netstandard1.1/de/System.Net.Primitives.xml", "ref/netstandard1.1/es/System.Net.Primitives.xml", "ref/netstandard1.1/fr/System.Net.Primitives.xml", "ref/netstandard1.1/it/System.Net.Primitives.xml", "ref/netstandard1.1/ja/System.Net.Primitives.xml", "ref/netstandard1.1/ko/System.Net.Primitives.xml", "ref/netstandard1.1/ru/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.3/System.Net.Primitives.dll", "ref/netstandard1.3/System.Net.Primitives.xml", "ref/netstandard1.3/de/System.Net.Primitives.xml", "ref/netstandard1.3/es/System.Net.Primitives.xml", "ref/netstandard1.3/fr/System.Net.Primitives.xml", "ref/netstandard1.3/it/System.Net.Primitives.xml", "ref/netstandard1.3/ja/System.Net.Primitives.xml", "ref/netstandard1.3/ko/System.Net.Primitives.xml", "ref/netstandard1.3/ru/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hant/System.Net.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.net.primitives.4.0.11.nupkg.sha512", "system.net.primitives.nuspec"]}, "System.Net.WebSockets/4.0.0": {"sha512": "2KJo8hir6Edi9jnMDAMhiJoI691xRBmKcbNpwjrvpIMOCTYOtBpSsSEGBxBDV7PKbasJNaFp1+PZz1D7xS41Hg==", "type": "package", "path": "system.net.websockets/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Net.WebSockets.dll", "lib/netstandard1.3/System.Net.WebSockets.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Net.WebSockets.dll", "ref/netstandard1.3/System.Net.WebSockets.dll", "ref/netstandard1.3/System.Net.WebSockets.xml", "ref/netstandard1.3/de/System.Net.WebSockets.xml", "ref/netstandard1.3/es/System.Net.WebSockets.xml", "ref/netstandard1.3/fr/System.Net.WebSockets.xml", "ref/netstandard1.3/it/System.Net.WebSockets.xml", "ref/netstandard1.3/ja/System.Net.WebSockets.xml", "ref/netstandard1.3/ko/System.Net.WebSockets.xml", "ref/netstandard1.3/ru/System.Net.WebSockets.xml", "ref/netstandard1.3/zh-hans/System.Net.WebSockets.xml", "ref/netstandard1.3/zh-hant/System.Net.WebSockets.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.net.websockets.4.0.0.nupkg.sha512", "system.net.websockets.nuspec"]}, "System.ObjectModel/4.3.0": {"sha512": "bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "type": "package", "path": "system.objectmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ObjectModel.dll", "lib/netstandard1.3/System.ObjectModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ObjectModel.dll", "ref/netcore50/System.ObjectModel.xml", "ref/netcore50/de/System.ObjectModel.xml", "ref/netcore50/es/System.ObjectModel.xml", "ref/netcore50/fr/System.ObjectModel.xml", "ref/netcore50/it/System.ObjectModel.xml", "ref/netcore50/ja/System.ObjectModel.xml", "ref/netcore50/ko/System.ObjectModel.xml", "ref/netcore50/ru/System.ObjectModel.xml", "ref/netcore50/zh-hans/System.ObjectModel.xml", "ref/netcore50/zh-hant/System.ObjectModel.xml", "ref/netstandard1.0/System.ObjectModel.dll", "ref/netstandard1.0/System.ObjectModel.xml", "ref/netstandard1.0/de/System.ObjectModel.xml", "ref/netstandard1.0/es/System.ObjectModel.xml", "ref/netstandard1.0/fr/System.ObjectModel.xml", "ref/netstandard1.0/it/System.ObjectModel.xml", "ref/netstandard1.0/ja/System.ObjectModel.xml", "ref/netstandard1.0/ko/System.ObjectModel.xml", "ref/netstandard1.0/ru/System.ObjectModel.xml", "ref/netstandard1.0/zh-hans/System.ObjectModel.xml", "ref/netstandard1.0/zh-hant/System.ObjectModel.xml", "ref/netstandard1.3/System.ObjectModel.dll", "ref/netstandard1.3/System.ObjectModel.xml", "ref/netstandard1.3/de/System.ObjectModel.xml", "ref/netstandard1.3/es/System.ObjectModel.xml", "ref/netstandard1.3/fr/System.ObjectModel.xml", "ref/netstandard1.3/it/System.ObjectModel.xml", "ref/netstandard1.3/ja/System.ObjectModel.xml", "ref/netstandard1.3/ko/System.ObjectModel.xml", "ref/netstandard1.3/ru/System.ObjectModel.xml", "ref/netstandard1.3/zh-hans/System.ObjectModel.xml", "ref/netstandard1.3/zh-hant/System.ObjectModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.objectmodel.4.3.0.nupkg.sha512", "system.objectmodel.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Emit/4.3.0": {"sha512": "228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "type": "package", "path": "system.reflection.emit/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/net45/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/xamarinmac20/_._", "system.reflection.emit.4.3.0.nupkg.sha512", "system.reflection.emit.nuspec"]}, "System.Reflection.Emit.ILGeneration/4.3.0": {"sha512": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec"]}, "System.Reflection.Emit.Lightweight/4.3.0": {"sha512": "oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "type": "package", "path": "system.reflection.emit.lightweight/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec"]}, "System.Reflection.Extensions/4.3.0": {"sha512": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "type": "package", "path": "system.reflection.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.extensions.4.3.0.nupkg.sha512", "system.reflection.extensions.nuspec"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Reflection.TypeExtensions/4.3.0": {"sha512": "7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "type": "package", "path": "system.reflection.typeextensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net462/System.Reflection.TypeExtensions.dll", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net462/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll", "system.reflection.typeextensions.4.3.0.nupkg.sha512", "system.reflection.typeextensions.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Handles/4.0.1": {"sha512": "nCJvEKguXEvk2ymk1gqj625vVnlK3/xdGzx0vOKicQkoquaTBJTP13AIYkocSUwHCLNBwUbXTqTWGDxBTWpt7g==", "type": "package", "path": "system.runtime.handles/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/_._", "ref/netstandard1.3/System.Runtime.Handles.dll", "ref/netstandard1.3/System.Runtime.Handles.xml", "ref/netstandard1.3/de/System.Runtime.Handles.xml", "ref/netstandard1.3/es/System.Runtime.Handles.xml", "ref/netstandard1.3/fr/System.Runtime.Handles.xml", "ref/netstandard1.3/it/System.Runtime.Handles.xml", "ref/netstandard1.3/ja/System.Runtime.Handles.xml", "ref/netstandard1.3/ko/System.Runtime.Handles.xml", "ref/netstandard1.3/ru/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Handles.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.handles.4.0.1.nupkg.sha512", "system.runtime.handles.nuspec"]}, "System.Runtime.InteropServices/4.1.0": {"sha512": "16eu3kjHS633yYdkjwShDHZLRNMKVi/s0bY8ODiqJ2RfMhDMAwxZaUaWVnZ2P71kr/or+X9o/xFWtNqz8ivieQ==", "type": "package", "path": "system.runtime.interopservices/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.InteropServices.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.xml", "ref/netcore50/de/System.Runtime.InteropServices.xml", "ref/netcore50/es/System.Runtime.InteropServices.xml", "ref/netcore50/fr/System.Runtime.InteropServices.xml", "ref/netcore50/it/System.Runtime.InteropServices.xml", "ref/netcore50/ja/System.Runtime.InteropServices.xml", "ref/netcore50/ko/System.Runtime.InteropServices.xml", "ref/netcore50/ru/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.xml", "ref/netstandard1.1/de/System.Runtime.InteropServices.xml", "ref/netstandard1.1/es/System.Runtime.InteropServices.xml", "ref/netstandard1.1/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.1/it/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.2/System.Runtime.InteropServices.dll", "ref/netstandard1.2/System.Runtime.InteropServices.xml", "ref/netstandard1.2/de/System.Runtime.InteropServices.xml", "ref/netstandard1.2/es/System.Runtime.InteropServices.xml", "ref/netstandard1.2/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.2/it/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.3/System.Runtime.InteropServices.dll", "ref/netstandard1.3/System.Runtime.InteropServices.xml", "ref/netstandard1.3/de/System.Runtime.InteropServices.xml", "ref/netstandard1.3/es/System.Runtime.InteropServices.xml", "ref/netstandard1.3/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.3/it/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.5/System.Runtime.InteropServices.dll", "ref/netstandard1.5/System.Runtime.InteropServices.xml", "ref/netstandard1.5/de/System.Runtime.InteropServices.xml", "ref/netstandard1.5/es/System.Runtime.InteropServices.xml", "ref/netstandard1.5/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.5/it/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hant/System.Runtime.InteropServices.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.interopservices.4.1.0.nupkg.sha512", "system.runtime.interopservices.nuspec"]}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"sha512": "hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "type": "package", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/win8/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/wpa81/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512", "system.runtime.interopservices.runtimeinformation.nuspec"]}, "System.Runtime.Loader/4.3.0": {"sha512": "DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "type": "package", "path": "system.runtime.loader/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/netstandard1.5/System.Runtime.Loader.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard1.5/System.Runtime.Loader.dll", "ref/netstandard1.5/System.Runtime.Loader.xml", "ref/netstandard1.5/de/System.Runtime.Loader.xml", "ref/netstandard1.5/es/System.Runtime.Loader.xml", "ref/netstandard1.5/fr/System.Runtime.Loader.xml", "ref/netstandard1.5/it/System.Runtime.Loader.xml", "ref/netstandard1.5/ja/System.Runtime.Loader.xml", "ref/netstandard1.5/ko/System.Runtime.Loader.xml", "ref/netstandard1.5/ru/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Loader.xml", "system.runtime.loader.4.3.0.nupkg.sha512", "system.runtime.loader.nuspec"]}, "System.Runtime.Numerics/4.0.1": {"sha512": "+XbKFuzdmLP3d1o9pdHu2nxjNr2OEPqGzKeegPLCUMM71a0t50A/rOcIRmGs9wR7a8KuHX6hYs/7/TymIGLNqg==", "type": "package", "path": "system.runtime.numerics/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.Numerics.dll", "lib/netstandard1.3/System.Runtime.Numerics.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.Numerics.dll", "ref/netcore50/System.Runtime.Numerics.xml", "ref/netcore50/de/System.Runtime.Numerics.xml", "ref/netcore50/es/System.Runtime.Numerics.xml", "ref/netcore50/fr/System.Runtime.Numerics.xml", "ref/netcore50/it/System.Runtime.Numerics.xml", "ref/netcore50/ja/System.Runtime.Numerics.xml", "ref/netcore50/ko/System.Runtime.Numerics.xml", "ref/netcore50/ru/System.Runtime.Numerics.xml", "ref/netcore50/zh-hans/System.Runtime.Numerics.xml", "ref/netcore50/zh-hant/System.Runtime.Numerics.xml", "ref/netstandard1.1/System.Runtime.Numerics.dll", "ref/netstandard1.1/System.Runtime.Numerics.xml", "ref/netstandard1.1/de/System.Runtime.Numerics.xml", "ref/netstandard1.1/es/System.Runtime.Numerics.xml", "ref/netstandard1.1/fr/System.Runtime.Numerics.xml", "ref/netstandard1.1/it/System.Runtime.Numerics.xml", "ref/netstandard1.1/ja/System.Runtime.Numerics.xml", "ref/netstandard1.1/ko/System.Runtime.Numerics.xml", "ref/netstandard1.1/ru/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hans/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hant/System.Runtime.Numerics.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.numerics.4.0.1.nupkg.sha512", "system.runtime.numerics.nuspec"]}, "System.Security.Claims/4.0.1": {"sha512": "4Jlp0OgJLS/Voj1kyFP6MJlIYp3crgfH8kNQk2p7+4JYfc1aAmh9PZyAMMbDhuoolGNtux9HqSOazsioRiDvCw==", "type": "package", "path": "system.security.claims/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Claims.dll", "lib/netstandard1.3/System.Security.Claims.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Claims.dll", "ref/netstandard1.3/System.Security.Claims.dll", "ref/netstandard1.3/System.Security.Claims.xml", "ref/netstandard1.3/de/System.Security.Claims.xml", "ref/netstandard1.3/es/System.Security.Claims.xml", "ref/netstandard1.3/fr/System.Security.Claims.xml", "ref/netstandard1.3/it/System.Security.Claims.xml", "ref/netstandard1.3/ja/System.Security.Claims.xml", "ref/netstandard1.3/ko/System.Security.Claims.xml", "ref/netstandard1.3/ru/System.Security.Claims.xml", "ref/netstandard1.3/zh-hans/System.Security.Claims.xml", "ref/netstandard1.3/zh-hant/System.Security.Claims.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.claims.4.0.1.nupkg.sha512", "system.security.claims.nuspec"]}, "System.Security.Cryptography.Algorithms/4.2.0": {"sha512": "8JQFxbLVdrtIOKMDN38Fn0GWnqYZw/oMlwOUG/qz1jqChvyZlnUmu+0s7wLx7JYua/nAXoESpHA3iw11QFWhXg==", "type": "package", "path": "system.security.cryptography.algorithms/4.2.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Algorithms.dll", "lib/net461/System.Security.Cryptography.Algorithms.dll", "lib/net463/System.Security.Cryptography.Algorithms.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Algorithms.dll", "ref/net461/System.Security.Cryptography.Algorithms.dll", "ref/net463/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.3/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.4/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net463/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "system.security.cryptography.algorithms.4.2.0.nupkg.sha512", "system.security.cryptography.algorithms.nuspec"]}, "System.Security.Cryptography.Cng/4.2.0": {"sha512": "cUJ2h+ZvONDe28Szw3st5dOHdjndhJzQ2WObDEXAWRPEQBtVItVoxbXM/OEsTthl3cNn2dk2k0I3y45igCQcLw==", "type": "package", "path": "system.security.cryptography.cng/4.2.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net463/System.Security.Cryptography.Cng.dll", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net463/System.Security.Cryptography.Cng.dll", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net463/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "system.security.cryptography.cng.4.2.0.nupkg.sha512", "system.security.cryptography.cng.nuspec"]}, "System.Security.Cryptography.Csp/4.0.0": {"sha512": "/i1Usuo4PgAqgbPNC0NjbO3jPW//BoBlTpcWFD1EHVbidH21y4c1ap5bbEMSGAXjAShhMH4abi/K8fILrnu4BQ==", "type": "package", "path": "system.security.cryptography.csp/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Csp.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Csp.dll", "ref/netstandard1.3/System.Security.Cryptography.Csp.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Csp.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Csp.dll", "runtimes/win/lib/netcore50/_._", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Csp.dll", "system.security.cryptography.csp.4.0.0.nupkg.sha512", "system.security.cryptography.csp.nuspec"]}, "System.Security.Cryptography.Encoding/4.0.0": {"sha512": "FbKgE5MbxSQMPcSVRgwM6bXN3GtyAh04NkV8E5zKCBE26X0vYW0UtTa2FIgkH33WVqBVxRgxljlVYumWtU+HcQ==", "type": "package", "path": "system.security.cryptography.encoding/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Encoding.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/de/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/es/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/it/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.Encoding.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "system.security.cryptography.encoding.4.0.0.nupkg.sha512", "system.security.cryptography.encoding.nuspec"]}, "System.Security.Cryptography.OpenSsl/4.0.0": {"sha512": "HUG/zNUJwEiLkoURDixzkzZdB5yGA5pQhDP93ArOpDPQMteURIGERRNzzoJlmTreLBWr5lkFSjjMSk8ySEpQMw==", "type": "package", "path": "system.security.cryptography.openssl/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "ref/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "system.security.cryptography.openssl.4.0.0.nupkg.sha512", "system.security.cryptography.openssl.nuspec"]}, "System.Security.Cryptography.Primitives/4.0.0": {"sha512": "Wkd7QryWYjkQclX0bngpntW5HSlMzeJU24UaLJQ7YTfI8ydAVAaU2J+HXLLABOVJlKTVvAeL0Aj39VeTe7L+oA==", "type": "package", "path": "system.security.cryptography.primitives/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Primitives.dll", "lib/netstandard1.3/System.Security.Cryptography.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Primitives.dll", "ref/netstandard1.3/System.Security.Cryptography.Primitives.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.cryptography.primitives.4.0.0.nupkg.sha512", "system.security.cryptography.primitives.nuspec"]}, "System.Security.Cryptography.X509Certificates/4.1.0": {"sha512": "4HEfsQIKAhA1+ApNn729Gi09zh+lYWwyIuViihoMDWp1vQnEkL2ct7mAbhBlLYm+x/L4Rr/pyGge1lIY635e0w==", "type": "package", "path": "system.security.cryptography.x509certificates/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.X509Certificates.dll", "lib/net461/System.Security.Cryptography.X509Certificates.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.X509Certificates.dll", "ref/net461/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.3/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.3/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/de/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/es/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/it/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.4/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/de/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/es/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/fr/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/it/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ja/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ko/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ru/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/zh-hans/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/zh-hant/System.Security.Cryptography.X509Certificates.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net46/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net461/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll", "system.security.cryptography.x509certificates.4.1.0.nupkg.sha512", "system.security.cryptography.x509certificates.nuspec"]}, "System.Security.Principal/4.0.1": {"sha512": "On+SKhXY5rzxh/S8wlH1Rm0ogBlu7zyHNxeNBiXauNrhHRXAe9EuX8Yl5IOzLPGU5Z4kLWHMvORDOCG8iu9hww==", "type": "package", "path": "system.security.principal/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Security.Principal.dll", "lib/netstandard1.0/System.Security.Principal.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Security.Principal.dll", "ref/netcore50/System.Security.Principal.xml", "ref/netcore50/de/System.Security.Principal.xml", "ref/netcore50/es/System.Security.Principal.xml", "ref/netcore50/fr/System.Security.Principal.xml", "ref/netcore50/it/System.Security.Principal.xml", "ref/netcore50/ja/System.Security.Principal.xml", "ref/netcore50/ko/System.Security.Principal.xml", "ref/netcore50/ru/System.Security.Principal.xml", "ref/netcore50/zh-hans/System.Security.Principal.xml", "ref/netcore50/zh-hant/System.Security.Principal.xml", "ref/netstandard1.0/System.Security.Principal.dll", "ref/netstandard1.0/System.Security.Principal.xml", "ref/netstandard1.0/de/System.Security.Principal.xml", "ref/netstandard1.0/es/System.Security.Principal.xml", "ref/netstandard1.0/fr/System.Security.Principal.xml", "ref/netstandard1.0/it/System.Security.Principal.xml", "ref/netstandard1.0/ja/System.Security.Principal.xml", "ref/netstandard1.0/ko/System.Security.Principal.xml", "ref/netstandard1.0/ru/System.Security.Principal.xml", "ref/netstandard1.0/zh-hans/System.Security.Principal.xml", "ref/netstandard1.0/zh-hant/System.Security.Principal.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.principal.4.0.1.nupkg.sha512", "system.security.principal.nuspec"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.Extensions/4.0.11": {"sha512": "jtbiTDtvfLYgXn8PTfWI+SiBs51rrmO4AAckx4KR6vFK9Wzf6tI8kcRdsYQNwriUeQ1+CtQbM1W4cMbLXnj/OQ==", "type": "package", "path": "system.text.encoding.extensions/4.0.11", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.Extensions.dll", "ref/netcore50/System.Text.Encoding.Extensions.xml", "ref/netcore50/de/System.Text.Encoding.Extensions.xml", "ref/netcore50/es/System.Text.Encoding.Extensions.xml", "ref/netcore50/fr/System.Text.Encoding.Extensions.xml", "ref/netcore50/it/System.Text.Encoding.Extensions.xml", "ref/netcore50/ja/System.Text.Encoding.Extensions.xml", "ref/netcore50/ko/System.Text.Encoding.Extensions.xml", "ref/netcore50/ru/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/System.Text.Encoding.Extensions.dll", "ref/netstandard1.0/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/System.Text.Encoding.Extensions.dll", "ref/netstandard1.3/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.extensions.4.0.11.nupkg.sha512", "system.text.encoding.extensions.nuspec"]}, "System.Text.Encodings.Web/4.0.0": {"sha512": "TWZnuiJgPDAEEUfobD7njXvSVR2Toz+jvKWds6yL4oSztmKQfnWzucczjzA+6Dv1bktBdY71sZW1YN0X6m9chQ==", "type": "package", "path": "system.text.encodings.web/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/System.Text.Encodings.Web.dll", "lib/netstandard1.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.4.0.0.nupkg.sha512", "system.text.encodings.web.nuspec"]}, "System.Text.RegularExpressions/4.1.0": {"sha512": "i88YCXpRTjCnoSQZtdlHkAOx4KNNik4hMy83n0+Ftlb7jvV6ZiZWMpnEZHhjBp6hQVh8gWd/iKNPzlPF7iyA2g==", "type": "package", "path": "system.text.regularexpressions/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Text.RegularExpressions.dll", "lib/netcore50/System.Text.RegularExpressions.dll", "lib/netstandard1.6/System.Text.RegularExpressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.xml", "ref/netcore50/de/System.Text.RegularExpressions.xml", "ref/netcore50/es/System.Text.RegularExpressions.xml", "ref/netcore50/fr/System.Text.RegularExpressions.xml", "ref/netcore50/it/System.Text.RegularExpressions.xml", "ref/netcore50/ja/System.Text.RegularExpressions.xml", "ref/netcore50/ko/System.Text.RegularExpressions.xml", "ref/netcore50/ru/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hans/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.0/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.xml", "ref/netstandard1.0/de/System.Text.RegularExpressions.xml", "ref/netstandard1.0/es/System.Text.RegularExpressions.xml", "ref/netstandard1.0/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.0/it/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.3/System.Text.RegularExpressions.dll", "ref/netstandard1.3/System.Text.RegularExpressions.xml", "ref/netstandard1.3/de/System.Text.RegularExpressions.xml", "ref/netstandard1.3/es/System.Text.RegularExpressions.xml", "ref/netstandard1.3/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.3/it/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.6/System.Text.RegularExpressions.dll", "ref/netstandard1.6/System.Text.RegularExpressions.xml", "ref/netstandard1.6/de/System.Text.RegularExpressions.xml", "ref/netstandard1.6/es/System.Text.RegularExpressions.xml", "ref/netstandard1.6/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.6/it/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hant/System.Text.RegularExpressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.regularexpressions.4.1.0.nupkg.sha512", "system.text.regularexpressions.nuspec"]}, "System.Threading/4.3.0": {"sha512": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "type": "package", "path": "system.threading/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.3.0.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "Tea/1.0.4": {"sha512": "SZWe+MUf8XzeVAyPmGzxAgfBNz41aRLxeH76xkRlS5Ola2fGf4RJTzzy0XgctuNRrEOtpHWO904jUTw7E/FIEA==", "type": "package", "path": "tea/1.0.4", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Tea.dll", "lib/netstandard2.0/Tea.dll", "tea.1.0.4.nupkg.sha512", "tea.nuspec"]}, "TimeZoneConverter/3.4.0": {"sha512": "4rqXd06uLqaLPGpO+whrZnSTJyG3SG5DEbeldMTvaGit0+gI6XLVb9YMnfM+5wtV0DDM1QPd/QhbE6j5vJCWDw==", "type": "package", "path": "timezoneconverter/3.4.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/TimeZoneConverter.dll", "lib/net35/TimeZoneConverter.xml", "lib/net40/TimeZoneConverter.dll", "lib/net40/TimeZoneConverter.xml", "lib/net45/TimeZoneConverter.dll", "lib/net45/TimeZoneConverter.xml", "lib/net461/TimeZoneConverter.dll", "lib/net461/TimeZoneConverter.xml", "lib/net471/TimeZoneConverter.dll", "lib/net471/TimeZoneConverter.xml", "lib/netstandard1.1/TimeZoneConverter.dll", "lib/netstandard1.1/TimeZoneConverter.xml", "lib/netstandard1.3/TimeZoneConverter.dll", "lib/netstandard1.3/TimeZoneConverter.xml", "lib/netstandard2.0/TimeZoneConverter.dll", "lib/netstandard2.0/TimeZoneConverter.xml", "timezoneconverter.3.4.0.nupkg.sha512", "timezoneconverter.nuspec"]}, "Volo.Abp.Account.Application.Contracts/0.19.0": {"sha512": "ABe8FdTz6KpFCOufVcqJ899u89f3PBxiF5SF7J8YaU6Xy7yd3vN+S8IkqJgOfdSttIKYC8dqv+hA2g4XhG0pBg==", "type": "package", "path": "volo.abp.account.application.contracts/0.19.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Account.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.Account.Application.Contracts.pdb", "volo.abp.account.application.contracts.0.19.0.nupkg.sha512", "volo.abp.account.application.contracts.nuspec"]}, "Volo.Abp.Auditing/4.3.0": {"sha512": "zptBxUin6U/LbdRdG6rRn+NyTQIAqN7wqjx3Ui7G9vvTHrRCreqzvuOsPC767HDUlcu+8o3LNJLQCuE981219A==", "type": "package", "path": "volo.abp.auditing/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Auditing.dll", "lib/netstandard2.0/Volo.Abp.Auditing.pdb", "lib/netstandard2.0/Volo.Abp.Auditing.xml", "volo.abp.auditing.4.3.0.nupkg.sha512", "volo.abp.auditing.nuspec"]}, "Volo.Abp.AuditLogging.Domain.Shared/0.4.1": {"sha512": "8I+v3WTuweRCHxnrcibyeQ9K2h0/kacaiscpPPgg06eo82vvqFihbj6ArkXwWiPPi1TGz2i1W3xmD6u+wtrCjQ==", "type": "package", "path": "volo.abp.auditlogging.domain.shared/0.4.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.Shared.pdb", "volo.abp.auditlogging.domain.shared.0.4.1.nupkg.sha512", "volo.abp.auditlogging.domain.shared.nuspec"]}, "Volo.Abp.Authorization/4.3.0": {"sha512": "9j5U19ntqS8JR+s86xNFm0ieH9CHz/po51d6BHxLTZzJuKbIu9xg453KvhKyuXYYnhp+/afu7Uq5EEi/s7sl9A==", "type": "package", "path": "volo.abp.authorization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Authorization.dll", "lib/netstandard2.0/Volo.Abp.Authorization.pdb", "lib/netstandard2.0/Volo.Abp.Authorization.xml", "volo.abp.authorization.4.3.0.nupkg.sha512", "volo.abp.authorization.nuspec"]}, "Volo.Abp.Authorization.Abstractions/4.3.0": {"sha512": "enL7a1MfJR0yjPVhf2UCgEmmQQ5wNb2GGX3k/iKzW5i//3lcnsDZQLAg17NKcbLc+imQ39RALNTOE4ALZQ4C7Q==", "type": "package", "path": "volo.abp.authorization.abstractions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.xml", "volo.abp.authorization.abstractions.4.3.0.nupkg.sha512", "volo.abp.authorization.abstractions.nuspec"]}, "Volo.Abp.BackgroundJobs.Abstractions/4.3.0": {"sha512": "9pOV9z7ELSrJ231wXJoAQfr/nKU/h/MSi9uwqiitl2oKewqc5Yp21AoJI8kaFGM3qAnFs2deiYd1eKiJWSvHPw==", "type": "package", "path": "volo.abp.backgroundjobs.abstractions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.xml", "volo.abp.backgroundjobs.abstractions.4.3.0.nupkg.sha512", "volo.abp.backgroundjobs.abstractions.nuspec"]}, "Volo.Abp.BackgroundJobs.Domain.Shared/0.4.1": {"sha512": "hAwebW8U1bB/OBBS6RmVhaLnNVf7GLE8Z03Tca3mcG/ug7P7x4f2vkEYW+gS5rvtG5/oB5/Dc5C5s5TjmXvoKw==", "type": "package", "path": "volo.abp.backgroundjobs.domain.shared/0.4.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.Shared.pdb", "volo.abp.backgroundjobs.domain.shared.0.4.1.nupkg.sha512", "volo.abp.backgroundjobs.domain.shared.nuspec"]}, "Volo.Abp.Core/4.3.0": {"sha512": "GTXSY8W+AG5sMczOv40AR85eq9a+R/qI4Ci7h853rudprGWkwtfYw9OQnrVd3Xu74WceSTfCDtHWbu2AuJ3Q+w==", "type": "package", "path": "volo.abp.core/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Core.dll", "lib/netstandard2.0/Volo.Abp.Core.pdb", "lib/netstandard2.0/Volo.Abp.Core.xml", "volo.abp.core.4.3.0.nupkg.sha512", "volo.abp.core.nuspec"]}, "Volo.Abp.Data/4.3.0": {"sha512": "2r42Sq03qH9z/B3fUZebbAeiEAPO3poHtzLslBhm4Qjjf9jlhBrkVjqZNkCDggNFNMVzRwWmrzSzIop8q8Crug==", "type": "package", "path": "volo.abp.data/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Data.dll", "lib/netstandard2.0/Volo.Abp.Data.pdb", "lib/netstandard2.0/Volo.Abp.Data.xml", "volo.abp.data.4.3.0.nupkg.sha512", "volo.abp.data.nuspec"]}, "Volo.Abp.Ddd.Application/4.3.0": {"sha512": "CGyrh+hAV6RPCVk+yT+BUa2itmnjbSCkBZBrUhkFvEJior8BvLVbMT9DGOxoDE3pOzeIkcqdk2bog9Wiq19fkg==", "type": "package", "path": "volo.abp.ddd.application/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Ddd.Application.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Application.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Application.xml", "volo.abp.ddd.application.4.3.0.nupkg.sha512", "volo.abp.ddd.application.nuspec"]}, "Volo.Abp.Ddd.Application.Contracts/4.3.0": {"sha512": "ysaifHH09mGhQLxEBTAKu8F/GsKbZmcs78CIulkqliPM9qMtyqIuNDSPq/LvWbyRBfPWb/wFdHiBtcBGj2tZVw==", "type": "package", "path": "volo.abp.ddd.application.contracts/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.xml", "volo.abp.ddd.application.contracts.4.3.0.nupkg.sha512", "volo.abp.ddd.application.contracts.nuspec"]}, "Volo.Abp.Ddd.Domain/4.3.0": {"sha512": "Nq5cMx0Uifg7GW8E8NedJZ7jaiPJv2Y9axYht5xbHEJ9gpsy1oovOp2VPKZ5Y5YYJY+M/1dlUEMAjM2LAANfpA==", "type": "package", "path": "volo.abp.ddd.domain/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.xml", "volo.abp.ddd.domain.4.3.0.nupkg.sha512", "volo.abp.ddd.domain.nuspec"]}, "Volo.Abp.Emailing/4.3.0": {"sha512": "mdCYf7O/O/xJE78ZnDLlvrLLbFxje7WcMxhhERBunpD1T7oNKpgS4QeR7kYnZ5Y4ByYmvqE3LVjTrLVf5Mhkcw==", "type": "package", "path": "volo.abp.emailing/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Emailing.dll", "lib/netstandard2.0/Volo.Abp.Emailing.pdb", "lib/netstandard2.0/Volo.Abp.Emailing.xml", "volo.abp.emailing.4.3.0.nupkg.sha512", "volo.abp.emailing.nuspec"]}, "Volo.Abp.EventBus/4.3.0": {"sha512": "AjBsTwigVF/LJVJf/5JgwWUX7vRNvHR/HRxst4+mDS/VJZDsgqIZLwDOFnFuA+wKtOW49e9+wuaU/LLat9HPoQ==", "type": "package", "path": "volo.abp.eventbus/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.EventBus.dll", "lib/netstandard2.0/Volo.Abp.EventBus.pdb", "lib/netstandard2.0/Volo.Abp.EventBus.xml", "volo.abp.eventbus.4.3.0.nupkg.sha512", "volo.abp.eventbus.nuspec"]}, "Volo.Abp.EventBus.Abstractions/4.3.0": {"sha512": "DW2Cr8k/g2kWqxLBEZvBjT/BSpbUo3zw8uSBMaAGAAKn4k7cNp7FC3YESu0C3y45Nd8Lo/3JLc9Iw4NN5uFmSA==", "type": "package", "path": "volo.abp.eventbus.abstractions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.xml", "volo.abp.eventbus.abstractions.4.3.0.nupkg.sha512", "volo.abp.eventbus.abstractions.nuspec"]}, "Volo.Abp.ExceptionHandling/4.3.0": {"sha512": "7ht+TOh8YlBZ+06GQ9vXP0FEbCPh7suNqUeKkz034NNNBKYVga9LU6qIpSaquVsM4++8NI/5AZpNh+ocgGhUBw==", "type": "package", "path": "volo.abp.exceptionhandling/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.dll", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.pdb", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.xml", "volo.abp.exceptionhandling.4.3.0.nupkg.sha512", "volo.abp.exceptionhandling.nuspec"]}, "Volo.Abp.FeatureManagement.Application.Contracts/0.15.0": {"sha512": "j/xnvc0jbuq1bfRAmuQK1oSnCq3HGMsRfSF+P7IzVasVD2/wQJsIORl+S2WVaI4XNoJjOJ86T8PJinRNRopb3A==", "type": "package", "path": "volo.abp.featuremanagement.application.contracts/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.FeatureManagement.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.FeatureManagement.Application.Contracts.pdb", "volo.abp.featuremanagement.application.contracts.0.15.0.nupkg.sha512", "volo.abp.featuremanagement.application.contracts.nuspec"]}, "Volo.Abp.FeatureManagement.Domain.Shared/0.15.0": {"sha512": "pOzZ6FpDrOxw47YEvDS5G6CzV8CbtCwRiPt0oa27D1+Ht7+NebxevdNlArkR5f2W2M7u4ZCgz7+ZCWdlGhymoA==", "type": "package", "path": "volo.abp.featuremanagement.domain.shared/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.Shared.pdb", "volo.abp.featuremanagement.domain.shared.0.15.0.nupkg.sha512", "volo.abp.featuremanagement.domain.shared.nuspec"]}, "Volo.Abp.Features/4.3.0": {"sha512": "8pHFDHqOQ54UEDciL8deBMg7nSIteTQWUzOT2ieuTl2WC8Mw198v3DYpXH1BTz0s9A9lM/i2h1qRVzVW+TMjrg==", "type": "package", "path": "volo.abp.features/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Features.dll", "lib/netstandard2.0/Volo.Abp.Features.pdb", "lib/netstandard2.0/Volo.Abp.Features.xml", "volo.abp.features.4.3.0.nupkg.sha512", "volo.abp.features.nuspec"]}, "Volo.Abp.GlobalFeatures/4.3.0": {"sha512": "DvDZ+7aQzEB2gtcRBJVFYiobfU5Rzj3p6Wsbp8XdEOlYLQXP55Ga8wiUMtMH4crOzHYSwHbdPC1s2bHMgVM9rA==", "type": "package", "path": "volo.abp.globalfeatures/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.GlobalFeatures.dll", "lib/netstandard2.0/Volo.Abp.GlobalFeatures.pdb", "lib/netstandard2.0/Volo.Abp.GlobalFeatures.xml", "volo.abp.globalfeatures.4.3.0.nupkg.sha512", "volo.abp.globalfeatures.nuspec"]}, "Volo.Abp.Guids/4.3.0": {"sha512": "Czq7YNU8zrSLQMx+8tpRbxjqN0eOmXAcr7XXy/qJSIoFLGCgh9xkH5CQhKmEFzl0vJFXf4DiHHEPAoRbW6mrhw==", "type": "package", "path": "volo.abp.guids/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Guids.dll", "lib/netstandard2.0/Volo.Abp.Guids.pdb", "lib/netstandard2.0/Volo.Abp.Guids.xml", "volo.abp.guids.4.3.0.nupkg.sha512", "volo.abp.guids.nuspec"]}, "Volo.Abp.Http.Abstractions/4.3.0": {"sha512": "Fy32oLk5amHZlObVzCz+FWDcjqd06IOhDSN10dg6Cueaefh/BRTd72tGSHPrEpKpjLLvnCU/L19ZIZd9DJAabQ==", "type": "package", "path": "volo.abp.http.abstractions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.xml", "volo.abp.http.abstractions.4.3.0.nupkg.sha512", "volo.abp.http.abstractions.nuspec"]}, "Volo.Abp.Identity.Application.Contracts/0.3.0": {"sha512": "WQJozlRR8KMsDN8fanJtdwQ8KY8hl+/oBzwLe1rOxnNXl5eHazMzjOrZcD5JBCpks2S9khoXXzj5DJ1v6mmNjg==", "type": "package", "path": "volo.abp.identity.application.contracts/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Identity.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.Identity.Application.Contracts.pdb", "volo.abp.identity.application.contracts.0.3.0.nupkg.sha512", "volo.abp.identity.application.contracts.nuspec"]}, "Volo.Abp.Identity.Domain.Shared/0.3.0": {"sha512": "VNXIl8HvkyaHxSqAicL3p1aR1VCv74ROvZ7mmfVdOjpX450R4KnQ/80eFSFUatHodhytYg7wO/gxUHTNxmNYew==", "type": "package", "path": "volo.abp.identity.domain.shared/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.pdb", "volo.abp.identity.domain.shared.0.3.0.nupkg.sha512", "volo.abp.identity.domain.shared.nuspec"]}, "Volo.Abp.IdentityServer.Domain.Shared/0.6.0": {"sha512": "6aBzeF2bcYBS8sOMP3BYdGJVNe6hoODLCfV/nyYsGVMOuwq3Zk2/NlPK2355EIHmhanx2NXsPsY3GaQrfdpQeA==", "type": "package", "path": "volo.abp.identityserver.domain.shared/0.6.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.Shared.pdb", "volo.abp.identityserver.domain.shared.0.6.0.nupkg.sha512", "volo.abp.identityserver.domain.shared.nuspec"]}, "Volo.Abp.Json/4.3.0": {"sha512": "yZJ/JjJeEO7vaQ/5vH1k0WUDPLjr7rkJ/JkhB6yjRDVirISPWL3y2CbTh6NPSyjTBShkaETCsOeAQBbjDvEbPQ==", "type": "package", "path": "volo.abp.json/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Json.dll", "lib/netstandard2.0/Volo.Abp.Json.pdb", "lib/netstandard2.0/Volo.Abp.Json.xml", "volo.abp.json.4.3.0.nupkg.sha512", "volo.abp.json.nuspec"]}, "Volo.Abp.Localization/0.3.0": {"sha512": "VGjyBKlfsB9no2E2q09LS/I7O/Vbqg3mJcLwtXSNibXwwZFEDTKnqo4k3pPJfIFJmG+n2ThtzUG8OpA69Cgswg==", "type": "package", "path": "volo.abp.localization/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Localization.dll", "lib/netstandard2.0/Volo.Abp.Localization.pdb", "volo.abp.localization.0.3.0.nupkg.sha512", "volo.abp.localization.nuspec"]}, "Volo.Abp.Localization.Abstractions/4.3.0": {"sha512": "8edOpld98PimnPODkrWtw/chW+K+1oAQUZccaO6F1DdnUsknQ03k6tXNvdVaXDQft74SsMcaf1tlHrvfrKPhZA==", "type": "package", "path": "volo.abp.localization.abstractions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.xml", "volo.abp.localization.abstractions.4.3.0.nupkg.sha512", "volo.abp.localization.abstractions.nuspec"]}, "Volo.Abp.MultiTenancy/4.3.0": {"sha512": "/DU+5xg+7v889I86EguH+s/2C8wleVaphlTAuRgSPlWIujeOh6O7Dj+ImBb+LOEFK+6PvnzDyDCca57k2lwRsw==", "type": "package", "path": "volo.abp.multitenancy/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.MultiTenancy.dll", "lib/netstandard2.0/Volo.Abp.MultiTenancy.pdb", "lib/netstandard2.0/Volo.Abp.MultiTenancy.xml", "volo.abp.multitenancy.4.3.0.nupkg.sha512", "volo.abp.multitenancy.nuspec"]}, "Volo.Abp.ObjectExtending/2.4.0": {"sha512": "K8K3ZjbX7k6rF7Q1fBizcI2cSaG/qauDdq9lFoldpCuQbkgBzNup9KpcZytMKgnufIWtnFBx1+Ysn2ScIQIlaQ==", "type": "package", "path": "volo.abp.objectextending/2.4.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.ObjectExtending.dll", "lib/netstandard2.0/Volo.Abp.ObjectExtending.pdb", "volo.abp.objectextending.2.4.0.nupkg.sha512", "volo.abp.objectextending.nuspec"]}, "Volo.Abp.ObjectMapping/4.3.0": {"sha512": "4QwdHZFbDiLMW4QNaQvDqjmHw5a3BEKhDmGUkWYwqNHVBTpwj/t7Fm+N0g1FXGjYE5muioko168pbsA8oPwDDw==", "type": "package", "path": "volo.abp.objectmapping/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.ObjectMapping.dll", "lib/netstandard2.0/Volo.Abp.ObjectMapping.pdb", "lib/netstandard2.0/Volo.Abp.ObjectMapping.xml", "volo.abp.objectmapping.4.3.0.nupkg.sha512", "volo.abp.objectmapping.nuspec"]}, "Volo.Abp.PermissionManagement.Application.Contracts/0.3.0": {"sha512": "KCWAh6INJm8fHJ7YqVVjweSwd8iqNJ7RM3n7KS8NSVsX9esK6trRslkiNEPyKD7/Z6ar6kXRJyVIeDwvNH1vmw==", "type": "package", "path": "volo.abp.permissionmanagement.application.contracts/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Application.Contracts.pdb", "volo.abp.permissionmanagement.application.contracts.0.3.0.nupkg.sha512", "volo.abp.permissionmanagement.application.contracts.nuspec"]}, "Volo.Abp.PermissionManagement.Domain.Shared/0.3.0": {"sha512": "HRpGxHawNqQKx6ySG1AjVn6F6m8QTFNphZ/Wx9HijengnbXGsz/JN85ahXrj/ftOTby2sy9R+VspL73DBzmTxg==", "type": "package", "path": "volo.abp.permissionmanagement.domain.shared/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Shared.pdb", "volo.abp.permissionmanagement.domain.shared.0.3.0.nupkg.sha512", "volo.abp.permissionmanagement.domain.shared.nuspec"]}, "Volo.Abp.Security/4.3.0": {"sha512": "HNShcU4urZUP4McuJFqv/X3m2tyJQkrruvuIbV+4MR9a817zo82BPqmzYG7RrZnG2Spyvqaa8GtXBfCGgFVUZA==", "type": "package", "path": "volo.abp.security/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Security.dll", "lib/netstandard2.0/Volo.Abp.Security.pdb", "lib/netstandard2.0/Volo.Abp.Security.xml", "volo.abp.security.4.3.0.nupkg.sha512", "volo.abp.security.nuspec"]}, "Volo.Abp.SettingManagement.Application.Contracts/4.3.0": {"sha512": "i9dCc5OGdynForx95Ypjmb37oPPLl+yrjmTqNwmy0xuCGBqmkMDnL7oZ111x89OfP8/ZTKx2LAsywT5qKa1Ijw==", "type": "package", "path": "volo.abp.settingmanagement.application.contracts/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.SettingManagement.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.SettingManagement.Application.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.SettingManagement.Application.Contracts.xml", "volo.abp.settingmanagement.application.contracts.4.3.0.nupkg.sha512", "volo.abp.settingmanagement.application.contracts.nuspec"]}, "Volo.Abp.Settings/4.3.0": {"sha512": "aCEhagDlTzoS+c/RI/geH7YlK+d3fYvQ/WRI75PLVJMMhMALXT25cZr71BsNwu00LLUnPf6xFfLkfwlsZfIaKA==", "type": "package", "path": "volo.abp.settings/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Settings.dll", "lib/netstandard2.0/Volo.Abp.Settings.pdb", "lib/netstandard2.0/Volo.Abp.Settings.xml", "volo.abp.settings.4.3.0.nupkg.sha512", "volo.abp.settings.nuspec"]}, "Volo.Abp.Specifications/4.3.0": {"sha512": "c/Yg1LiFm+JPX5sGTS/OPxTRvaaEaNlkvCAJAwjTC2CkjrdjPSKDEiB47tCWHYW0dIcIyEx/bV9L5inyLc2qAQ==", "type": "package", "path": "volo.abp.specifications/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Specifications.dll", "lib/netstandard2.0/Volo.Abp.Specifications.pdb", "lib/netstandard2.0/Volo.Abp.Specifications.xml", "volo.abp.specifications.4.3.0.nupkg.sha512", "volo.abp.specifications.nuspec"]}, "Volo.Abp.TenantManagement.Application.Contracts/0.3.0": {"sha512": "dkQvJmgFzu2ObvCvT5GQp4BY0U5btUbnk9Wa6y98yBt37FZckW+m9pTQePmMuu4L+onfQqiFiIViaVwmYi+Ekw==", "type": "package", "path": "volo.abp.tenantmanagement.application.contracts/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.TenantManagement.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.TenantManagement.Application.Contracts.pdb", "volo.abp.tenantmanagement.application.contracts.0.3.0.nupkg.sha512", "volo.abp.tenantmanagement.application.contracts.nuspec"]}, "Volo.Abp.TenantManagement.Domain.Shared/0.3.0": {"sha512": "e6rOKpbOX0e0l+0juEcKom9t/4tEL6mtzjkhthLWwCeXYwCkMt7CUMWNIMP+7j9j7EQBsXOnIqd4lcsh19pI1g==", "type": "package", "path": "volo.abp.tenantmanagement.domain.shared/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.TenantManagement.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.TenantManagement.Domain.Shared.pdb", "volo.abp.tenantmanagement.domain.shared.0.3.0.nupkg.sha512", "volo.abp.tenantmanagement.domain.shared.nuspec"]}, "Volo.Abp.TextTemplating/4.3.0": {"sha512": "H2CVzU5534ydCB91/2XFLyCVCNg0G6VWe3PhcDm1zIBhH+lQip/qRm06UY/hdRn0pC4EmY/BozD1LG6xYlvzFw==", "type": "package", "path": "volo.abp.texttemplating/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.TextTemplating.dll", "lib/netstandard2.0/Volo.Abp.TextTemplating.pdb", "lib/netstandard2.0/Volo.Abp.TextTemplating.xml", "volo.abp.texttemplating.4.3.0.nupkg.sha512", "volo.abp.texttemplating.nuspec"]}, "Volo.Abp.Threading/4.3.0": {"sha512": "HGZ7MNUNUamMKwhp9taCv8LTnsltA3WchiO0qIuf1BRAg3333muQqrtmraIB/k6T+1ecPoEIpxXzxwgMmTvvxg==", "type": "package", "path": "volo.abp.threading/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Threading.dll", "lib/netstandard2.0/Volo.Abp.Threading.pdb", "lib/netstandard2.0/Volo.Abp.Threading.xml", "volo.abp.threading.4.3.0.nupkg.sha512", "volo.abp.threading.nuspec"]}, "Volo.Abp.Timing/4.3.0": {"sha512": "ohLNz/1T1sKLFV0IXeqxrMlu2sf/qdM3Bj4hOzhse9MGB+jO2l8DKmzOOH70xpH6wWrRELjmRyOoR8U3jwc8Hg==", "type": "package", "path": "volo.abp.timing/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Timing.dll", "lib/netstandard2.0/Volo.Abp.Timing.pdb", "lib/netstandard2.0/Volo.Abp.Timing.xml", "volo.abp.timing.4.3.0.nupkg.sha512", "volo.abp.timing.nuspec"]}, "Volo.Abp.Uow/4.3.0": {"sha512": "CwmJseLIBUcFsAiIVSr9WiNBcn/cqzJDCZmAYqzTLy7etETO+nMXSqohvAt6LlbwmjXHfMO5EZb6mYy4nnTbKQ==", "type": "package", "path": "volo.abp.uow/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Uow.dll", "lib/netstandard2.0/Volo.Abp.Uow.pdb", "lib/netstandard2.0/Volo.Abp.Uow.xml", "volo.abp.uow.4.3.0.nupkg.sha512", "volo.abp.uow.nuspec"]}, "Volo.Abp.Users.Domain.Shared/0.3.0": {"sha512": "7C21JDHOg1BZ/hWQGjs2shLETJ8dn0bH/kIoWVLEQ18Pra4GIeT2MURaut/tnZoDibuXKj6k3JRiB0iUSGMtnw==", "type": "package", "path": "volo.abp.users.domain.shared/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.pdb", "volo.abp.users.domain.shared.0.3.0.nupkg.sha512", "volo.abp.users.domain.shared.nuspec"]}, "Volo.Abp.Validation/0.3.0": {"sha512": "qu/vW3VES3VjBu/2aj1P8MSkxfEo/ihD05i7Io9CQm5oKFFlx23q6cgxEpnhhMcQIb1Rw5MjvigWozrioFZlKA==", "type": "package", "path": "volo.abp.validation/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Validation.dll", "lib/netstandard2.0/Volo.Abp.Validation.pdb", "volo.abp.validation.0.3.0.nupkg.sha512", "volo.abp.validation.nuspec"]}, "Volo.Abp.VirtualFileSystem/4.3.0": {"sha512": "YL4rsAsqFzDOIOAGmCk78KskDcwLR8EsDxRdtgnztxGIEfok7c3/39nwbpwrkFBrwN5ZP0Qoo03VjFc+49cgqg==", "type": "package", "path": "volo.abp.virtualfilesystem/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.pdb", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.xml", "volo.abp.virtualfilesystem.4.3.0.nupkg.sha512", "volo.abp.virtualfilesystem.nuspec"]}, "TSZ.ServiceBase.FileStorageCenter.Domain.Shared/1.0.0": {"type": "project", "path": "../2_Domain.Shared/TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj", "msbuildProject": "../2_Domain.Shared/TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["AlibabaCloud.SDK.Sts20150401", "Microsoft.AspNetCore.Mvc.Core", "TSZ.ServiceBase.FileStorageCenter.Domain.Shared >= 1.0.0", "Volo.Abp.Account.Application.Contracts", "Volo.Abp.FeatureManagement.Application.Contracts", "Volo.Abp.Identity.Application.Contracts", "Volo.Abp.ObjectExtending", "Volo.Abp.PermissionManagement.Application.Contracts", "Volo.Abp.SettingManagement.Application.Contracts", "Volo.Abp.TenantManagement.Application.Contracts"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj", "projectName": "TSZ.ServiceBase.FileStorageCenter.Application.Contracts", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AlibabaCloud.SDK.Sts20150401": {"target": "Package", "version": "(, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "(, )"}, "Volo.Abp.Account.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.FeatureManagement.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.Identity.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.ObjectExtending": {"target": "Package", "version": "(, )"}, "Volo.Abp.PermissionManagement.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.SettingManagement.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.TenantManagement.Application.Contracts": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 AlibabaCloud.SDK.Sts20150401 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "AlibabaCloud.SDK.Sts20150401", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Microsoft.AspNetCore.Mvc.Core 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Microsoft.AspNetCore.Mvc.Core", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.Account.Application.Contracts 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.Account.Application.Contracts", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.FeatureManagement.Application.Contracts 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.FeatureManagement.Application.Contracts", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.Identity.Application.Contracts 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.Identity.Application.Contracts", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.ObjectExtending 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.ObjectExtending", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.PermissionManagement.Application.Contracts 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.PermissionManagement.Application.Contracts", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.SettingManagement.Application.Contracts 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.SettingManagement.Application.Contracts", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.TenantManagement.Application.Contracts 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.TenantManagement.Application.Contracts", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Microsoft.Extensions.FileProviders.Embedded 的下限(含)。已改为解析 Microsoft.Extensions.FileProviders.Embedded 1.0.0。", "libraryId": "Microsoft.Extensions.FileProviders.Embedded", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.AuditLogging.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.AuditLogging.Domain.Shared 0.4.1。", "libraryId": "Volo.Abp.AuditLogging.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.BackgroundJobs.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.BackgroundJobs.Domain.Shared 0.4.1。", "libraryId": "Volo.Abp.BackgroundJobs.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.FeatureManagement.Application.Contracts 0.15.0 不提供依赖项 Volo.Abp.FeatureManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.FeatureManagement.Domain.Shared 0.15.0。", "libraryId": "Volo.Abp.FeatureManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.Identity.Application.Contracts 0.3.0 不提供依赖项 Volo.Abp.Identity.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.Identity.Domain.Shared 0.3.0。", "libraryId": "Volo.Abp.Identity.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.IdentityServer.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.IdentityServer.Domain.Shared 0.6.0。", "libraryId": "Volo.Abp.IdentityServer.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.Localization 的下限(含)。已改为解析 Volo.Abp.Localization 0.3.0。", "libraryId": "Volo.Abp.Localization", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.PermissionManagement.Application.Contracts 0.3.0 不提供依赖项 Volo.Abp.PermissionManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.PermissionManagement.Domain.Shared 0.3.0。", "libraryId": "Volo.Abp.PermissionManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.TenantManagement.Application.Contracts 0.3.0 不提供依赖项 Volo.Abp.TenantManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.TenantManagement.Domain.Shared 0.3.0。", "libraryId": "Volo.Abp.TenantManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.Validation 的下限(含)。已改为解析 Volo.Abp.Validation 0.3.0。", "libraryId": "Volo.Abp.Validation", "targetGraphs": ["net9.0"]}, {"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "包 \"Microsoft.AspNetCore.Mvc.Core\" 1.0.0 具有已知的 高 严重性漏洞，https://github.com/advisories/GHSA-3rp6-rjw4-cq39", "libraryId": "Microsoft.AspNetCore.Mvc.Core", "targetGraphs": ["net9.0"]}, {"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "包 \"Microsoft.AspNetCore.Mvc.Core\" 1.0.0 具有已知的 高 严重性漏洞，https://github.com/advisories/GHSA-6xh7-4v2w-36q6", "libraryId": "Microsoft.AspNetCore.Mvc.Core", "targetGraphs": ["net9.0"]}, {"code": "NU1902", "level": "Warning", "warningLevel": 1, "message": "包 \"Microsoft.AspNetCore.Mvc.Core\" 1.0.0 具有已知的 中 严重性漏洞，https://github.com/advisories/GHSA-ch6p-4jcm-h8vh", "libraryId": "Microsoft.AspNetCore.Mvc.Core", "targetGraphs": ["net9.0"]}, {"code": "NU1902", "level": "Warning", "warningLevel": 1, "message": "包 \"Microsoft.AspNetCore.Mvc.Core\" 1.0.0 具有已知的 中 严重性漏洞，https://github.com/advisories/GHSA-j8f4-2w4p-mhjc", "libraryId": "Microsoft.AspNetCore.Mvc.Core", "targetGraphs": ["net9.0"]}, {"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "包 \"Microsoft.AspNetCore.Mvc.Core\" 1.0.0 具有已知的 高 严重性漏洞，https://github.com/advisories/GHSA-qhqf-ghgh-x2m4", "libraryId": "Microsoft.AspNetCore.Mvc.Core", "targetGraphs": ["net9.0"]}, {"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net9.0”还原包“BouncyCastle 1.8.5”。此包可能与项目不完全兼容。", "libraryId": "BouncyCastle", "targetGraphs": ["net9.0"]}, {"code": "NU1107", "level": "Error", "message": "Volo.Abp.SettingManagement.Domain.Shared 中检测到版本冲突。直接安装/引用 Volo.Abp.SettingManagement.Domain.Shared 0.3.0 到项目 TSZ.ServiceBase.FileStorageCenter.Application.Contracts 可解决此问题。 \r\n TSZ.ServiceBase.FileStorageCenter.Application.Contracts -> TSZ.ServiceBase.FileStorageCenter.Domain.Shared -> Volo.Abp.SettingManagement.Domain.Shared \r\n TSZ.ServiceBase.FileStorageCenter.Application.Contracts -> Volo.Abp.SettingManagement.Application.Contracts 4.3.0 -> Volo.Abp.SettingManagement.Domain.Shared (>= 4.3.0).", "libraryId": "Volo.Abp.SettingManagement.Domain.Shared", "targetGraphs": ["net9.0"]}]}