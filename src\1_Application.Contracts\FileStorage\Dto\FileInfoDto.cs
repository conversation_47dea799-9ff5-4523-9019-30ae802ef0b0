using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage.Dto
{
    /// <summary>
    /// 文件信息DTO
    /// </summary>
    public class FileInfoDto : FullAuditedEntityDto<Guid>
    {
        /// <summary>
        /// 租户ID
        /// </summary>
        public Guid? TenantId { get; set; }

        /// <summary>
        /// 文件ID（业务主键）
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 原始文件名
        /// </summary>
        public string OriginalFileName { get; set; }

        /// <summary>
        /// 相对路径
        /// </summary>
        public string RelativePath { get; set; }

        /// <summary>
        /// 绝对路径
        /// </summary>
        public string AbsolutePath { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// MD5哈希值
        /// </summary>
        public string MD5Hash { get; set; }

        /// <summary>
        /// SHA256哈希值
        /// </summary>
        public string SHA256Hash { get; set; }

        /// <summary>
        /// MIME类型
        /// </summary>
        public string MimeType { get; set; }

        /// <summary>
        /// 文件扩展名
        /// </summary>
        public string FileExtension { get; set; }

        /// <summary>
        /// 存储类型
        /// </summary>
        public StorageType StorageType { get; set; }

        /// <summary>
        /// 存储配置名称
        /// </summary>
        public string StorageConfig { get; set; }

        /// <summary>
        /// 文件状态
        /// </summary>
        public FileStatus Status { get; set; }

        /// <summary>
        /// 引用计数
        /// </summary>
        public int ReferenceCount { get; set; }

        /// <summary>
        /// 是否加密
        /// </summary>
        public bool IsEncrypted { get; set; }

        /// <summary>
        /// 是否公开
        /// </summary>
        public bool IsPublic { get; set; }

        /// <summary>
        /// 访问次数
        /// </summary>
        public long AccessCount { get; set; }

        /// <summary>
        /// 下载次数
        /// </summary>
        public long DownloadCount { get; set; }

        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime? LastAccessTime { get; set; }

        /// <summary>
        /// 最后下载时间
        /// </summary>
        public DateTime? LastDownloadTime { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public string Tags { get; set; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtraProperties { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; }
    }

    /// <summary>
    /// 文件上传请求DTO
    /// </summary>
    public class FileUploadRequestDto
    {
        /// <summary>
        /// 文件名
        /// </summary>
        [Required]
        [StringLength(255)]
        public string FileName { get; set; }

        /// <summary>
        /// 目标目录（相对路径）
        /// </summary>
        [StringLength(1000)]
        public string TargetDirectory { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// MD5哈希值（可选，用于快传）
        /// </summary>
        [StringLength(32)]
        public string MD5Hash { get; set; }

        /// <summary>
        /// MIME类型
        /// </summary>
        [StringLength(100)]
        public string MimeType { get; set; }

        /// <summary>
        /// 是否加密
        /// </summary>
        public bool IsEncrypted { get; set; }

        /// <summary>
        /// 是否公开
        /// </summary>
        public bool IsPublic { get; set; }

        /// <summary>
        /// 存储类型（可选，使用默认配置）
        /// </summary>
        public StorageType? StorageType { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        [StringLength(1000)]
        public string Tags { get; set; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtraProperties { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string Remarks { get; set; }

        /// <summary>
        /// 上传模式（1=覆盖，0=追加）
        /// </summary>
        public int UploadMode { get; set; } = 1;

        /// <summary>
        /// 是否检查快传
        /// </summary>
        public bool CheckQuickUpload { get; set; } = true;
    }

    /// <summary>
    /// 文件上传响应DTO
    /// </summary>
    public class FileUploadResponseDto
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        /// 实际文件名（可能与请求的文件名不同）
        /// </summary>
        public string ActualFileName { get; set; }

        /// <summary>
        /// 文件相对路径
        /// </summary>
        public string RelativePath { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// MD5哈希值
        /// </summary>
        public string MD5Hash { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        public DateTime UploadTime { get; set; }

        /// <summary>
        /// 是否为快传（秒传）
        /// </summary>
        public bool IsQuickUpload { get; set; }

        /// <summary>
        /// 存储类型
        /// </summary>
        public StorageType StorageType { get; set; }

        /// <summary>
        /// 上传状态消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 文件下载请求DTO
    /// </summary>
    public class FileDownloadRequestDto
    {
        /// <summary>
        /// 是否内联显示（预览）
        /// </summary>
        public bool Inline { get; set; } = false;

        /// <summary>
        /// 自定义下载文件名
        /// </summary>
        [StringLength(255)]
        public string DownloadFileName { get; set; }

        /// <summary>
        /// 是否记录下载统计
        /// </summary>
        public bool RecordDownload { get; set; } = true;

        /// <summary>
        /// 访问令牌（用于权限验证）
        /// </summary>
        [StringLength(500)]
        public string AccessToken { get; set; }
    }

    /// <summary>
    /// 文件下载响应DTO
    /// </summary>
    public class FileDownloadResponseDto
    {
        /// <summary>
        /// 文件内容
        /// </summary>
        public byte[] Content { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// MIME类型
        /// </summary>
        public string MimeType { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// ETag（用于缓存）
        /// </summary>
        public string ETag { get; set; }
    }

    /// <summary>
    /// 文件统计信息DTO
    /// </summary>
    public class FileStatisticsDto
    {
        /// <summary>
        /// 文件总数
        /// </summary>
        public long TotalCount { get; set; }

        /// <summary>
        /// 文件总大小（字节）
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// 活跃文件数量
        /// </summary>
        public long ActiveCount { get; set; }

        /// <summary>
        /// 已删除文件数量
        /// </summary>
        public long DeletedCount { get; set; }

        /// <summary>
        /// 已归档文件数量
        /// </summary>
        public long ArchivedCount { get; set; }

        /// <summary>
        /// 总访问次数
        /// </summary>
        public long TotalAccessCount { get; set; }

        /// <summary>
        /// 总下载次数
        /// </summary>
        public long TotalDownloadCount { get; set; }

        /// <summary>
        /// 平均文件大小
        /// </summary>
        public double AverageFileSize { get; set; }

        /// <summary>
        /// 最大文件大小
        /// </summary>
        public long MaxFileSize { get; set; }

        /// <summary>
        /// 最小文件大小
        /// </summary>
        public long MinFileSize { get; set; }

        /// <summary>
        /// 按存储类型分组的统计
        /// </summary>
        public Dictionary<StorageType, long> CountByStorageType { get; set; }

        /// <summary>
        /// 按文件状态分组的统计
        /// </summary>
        public Dictionary<FileStatus, long> CountByStatus { get; set; }

        /// <summary>
        /// 按文件扩展名分组的统计（Top 10）
        /// </summary>
        public Dictionary<string, long> CountByExtension { get; set; }
    }

    /// <summary>
    /// 文件搜索请求DTO
    /// </summary>
    public class FileSearchRequestDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// 搜索关键词（文件名）
        /// </summary>
        [StringLength(255)]
        public string Keyword { get; set; }

        /// <summary>
        /// 文件扩展名过滤
        /// </summary>
        [StringLength(20)]
        public string FileExtension { get; set; }

        /// <summary>
        /// 存储类型过滤
        /// </summary>
        public StorageType? StorageType { get; set; }

        /// <summary>
        /// 文件状态过滤
        /// </summary>
        public FileStatus? Status { get; set; }

        /// <summary>
        /// 最小文件大小
        /// </summary>
        public long? MinFileSize { get; set; }

        /// <summary>
        /// 最大文件大小
        /// </summary>
        public long? MaxFileSize { get; set; }

        /// <summary>
        /// 创建时间范围开始
        /// </summary>
        public DateTime? CreatedAfter { get; set; }

        /// <summary>
        /// 创建时间范围结束
        /// </summary>
        public DateTime? CreatedBefore { get; set; }

        /// <summary>
        /// 标签过滤
        /// </summary>
        [StringLength(100)]
        public string Tag { get; set; }

        /// <summary>
        /// 是否包含已删除文件
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;
    }

    /// <summary>
    /// 文件搜索响应DTO
    /// </summary>
    public class FileSearchResponseDto : PagedResultDto<FileInfoDto>
    {
        /// <summary>
        /// 搜索耗时（毫秒）
        /// </summary>
        public long SearchTimeMs { get; set; }

        /// <summary>
        /// 搜索建议（当结果较少时）
        /// </summary>
        public List<string> Suggestions { get; set; }
    }

    /// <summary>
    /// 文件统计请求DTO
    /// </summary>
    public class FileStatisticsRequestDto
    {
        /// <summary>
        /// 存储类型过滤
        /// </summary>
        public StorageType? StorageType { get; set; }

        /// <summary>
        /// 文件状态过滤
        /// </summary>
        public FileStatus? Status { get; set; }

        /// <summary>
        /// 统计时间范围开始
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 统计时间范围结束
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 是否包含详细统计
        /// </summary>
        public bool IncludeDetails { get; set; } = true;
    }
}
