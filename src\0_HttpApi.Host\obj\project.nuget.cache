{"version": 2, "dgSpecHash": "AKuOpU1DMqw=", "success": false, "projectFilePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.endpointutil\\0.1.1\\alibabacloud.endpointutil.0.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.openapiclient\\0.0.6\\alibabacloud.openapiclient.0.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.openapiutil\\1.0.3\\alibabacloud.openapiutil.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.sdk.sts20150401\\1.0.0\\alibabacloud.sdk.sts20150401.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.teautil\\0.1.9\\alibabacloud.teautil.0.1.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aliyun.credentials\\1.3.1\\aliyun.credentials.1.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aliyun.oss.sdk.netcore\\2.9.0\\aliyun.oss.sdk.netcore.2.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac\\4.0.1\\autofac.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac.extras.dynamicproxy\\4.4.0\\autofac.extras.dynamicproxy.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\automapper\\8.0.0\\automapper.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.core\\3.7.303.24\\awssdk.core.3.7.303.24.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.s3\\3.7.307.25\\awssdk.s3.3.7.307.25.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bouncycastle\\1.8.5\\bouncycastle.1.8.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\castle.core\\4.2.1\\castle.core.4.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.highperformance\\8.2.2\\communitytoolkit.highperformance.8.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\configureawait.fody\\3.3.1\\configureawait.fody.3.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentftp\\16.0.13\\fluentftp.16.0.13.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fody\\6.0.2\\fody.6.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.protobuf\\3.6.1\\google.protobuf.3.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc\\1.17.0\\grpc.1.17.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.core\\1.17.0\\grpc.core.1.17.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\identitymodel\\3.6.1\\identitymodel.3.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\identityserver4\\2.2.0\\identityserver4.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\identityserver4.aspnetidentity\\2.1.0\\identityserver4.aspnetidentity.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jetbrains.annotations\\2020.3.0\\jetbrains.annotations.2020.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication\\2.1.0\\microsoft.aspnetcore.authentication.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.abstractions\\2.1.0\\microsoft.aspnetcore.authentication.abstractions.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.cookies\\2.1.0\\microsoft.aspnetcore.authentication.cookies.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.core\\2.1.0\\microsoft.aspnetcore.authentication.core.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.jwtbearer\\1.0.0\\microsoft.aspnetcore.authentication.jwtbearer.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.oauth\\2.0.3\\microsoft.aspnetcore.authentication.oauth.2.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.openidconnect\\2.0.3\\microsoft.aspnetcore.authentication.openidconnect.2.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\5.0.5\\microsoft.aspnetcore.authorization.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cors\\2.0.2\\microsoft.aspnetcore.cors.2.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.internal\\2.2.0\\microsoft.aspnetcore.cryptography.internal.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.keyderivation\\2.1.0\\microsoft.aspnetcore.cryptography.keyderivation.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection\\2.2.0\\microsoft.aspnetcore.dataprotection.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.abstractions\\2.2.0\\microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.stackexchangeredis\\2.2.0\\microsoft.aspnetcore.dataprotection.stackexchangeredis.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.diagnostics\\2.1.0\\microsoft.aspnetcore.diagnostics.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.diagnostics.abstractions\\2.1.0\\microsoft.aspnetcore.diagnostics.abstractions.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.server.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http\\2.1.0\\microsoft.aspnetcore.http.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.extensions\\2.1.0\\microsoft.aspnetcore.http.extensions.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity\\2.1.0\\microsoft.aspnetcore.identity.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.jsonpatch\\5.0.5\\microsoft.aspnetcore.jsonpatch.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\5.0.5\\microsoft.aspnetcore.metadata.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.abstractions\\1.0.0\\microsoft.aspnetcore.mvc.abstractions.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.core\\1.0.0\\microsoft.aspnetcore.mvc.core.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.newtonsoftjson\\5.0.5\\microsoft.aspnetcore.mvc.newtonsoftjson.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor.extensions\\5.0.5\\microsoft.aspnetcore.mvc.razor.extensions.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor.runtimecompilation\\5.0.5\\microsoft.aspnetcore.mvc.razor.runtimecompilation.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.versioning\\5.0.0\\microsoft.aspnetcore.mvc.versioning.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.razor.language\\5.0.5\\microsoft.aspnetcore.razor.language.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.routing\\1.0.0\\microsoft.aspnetcore.routing.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.routing.abstractions\\1.0.0\\microsoft.aspnetcore.routing.abstractions.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.webutilities\\2.1.0\\microsoft.aspnetcore.webutilities.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\3.0.0\\microsoft.codeanalysis.analyzers.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\3.8.0\\microsoft.codeanalysis.common.3.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\3.8.0\\microsoft.codeanalysis.csharp.3.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.razor\\5.0.5\\microsoft.codeanalysis.razor.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\2.2.0\\microsoft.entityframeworkcore.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\2.2.0\\microsoft.entityframeworkcore.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.analyzers\\2.2.0\\microsoft.entityframeworkcore.analyzers.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.design\\1.0.3\\microsoft.entityframeworkcore.design.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\2.2.0\\microsoft.entityframeworkcore.relational.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational.design\\1.0.3\\microsoft.entityframeworkcore.relational.design.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.tools\\1.0.0\\microsoft.entityframeworkcore.tools.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\3.0.0\\microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\3.1.5\\microsoft.extensions.caching.abstractions.3.1.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\3.1.5\\microsoft.extensions.caching.memory.3.1.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.stackexchangeredis\\3.1.5\\microsoft.extensions.caching.stackexchangeredis.3.1.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\5.0.0\\microsoft.extensions.configuration.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\5.0.0\\microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\5.0.0\\microsoft.extensions.configuration.binder.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\5.0.0\\microsoft.extensions.configuration.commandline.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\5.0.0\\microsoft.extensions.configuration.environmentvariables.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\5.0.0\\microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\5.0.0\\microsoft.extensions.configuration.json.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\5.0.0\\microsoft.extensions.configuration.usersecrets.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.0\\microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.1\\microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\5.0.0\\microsoft.extensions.dependencymodel.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\5.0.0\\microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.composite\\5.0.0\\microsoft.extensions.fileproviders.composite.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\5.0.0\\microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\5.0.0\\microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\5.0.0\\microsoft.extensions.hosting.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.identity.core\\2.1.0\\microsoft.extensions.identity.core.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization\\5.0.5\\microsoft.extensions.localization.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization.abstractions\\5.0.5\\microsoft.extensions.localization.abstractions.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\8.0.0\\microsoft.extensions.logging.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.0\\microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\2.1.0\\microsoft.extensions.objectpool.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\8.0.0\\microsoft.extensions.options.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\5.0.0\\microsoft.extensions.options.configurationextensions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.platformabstractions\\1.0.0\\microsoft.extensions.platformabstractions.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.webencoders\\2.1.0\\microsoft.extensions.webencoders.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.icu.icu4c.runtime\\67.1.0.4\\microsoft.icu.icu4c.runtime.67.1.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.icu.icu4c.runtime.win-arm64\\67.1.0.4\\microsoft.icu.icu4c.runtime.win-arm64.67.1.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.icu.icu4c.runtime.win-x64\\67.1.0.4\\microsoft.icu.icu4c.runtime.win-x64.67.1.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.icu.icu4c.runtime.win-x86\\67.1.0.4\\microsoft.icu.icu4c.runtime.win-x86.67.1.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\5.2.1\\microsoft.identitymodel.logging.5.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\2.1.4\\microsoft.identitymodel.protocols.2.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\2.1.4\\microsoft.identitymodel.protocols.openidconnect.2.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\5.2.1\\microsoft.identitymodel.tokens.5.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.http.headers\\2.1.0\\microsoft.net.http.headers.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\2.1.2\\microsoft.netcore.platforms.2.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.openapi\\1.1.4\\microsoft.openapi.1.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.tpl.dataflow\\4.5.24\\microsoft.tpl.dataflow.4.5.24.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.5.0\\microsoft.win32.registry.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\minio\\6.0.3\\minio.6.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mysql.data\\8.0.11\\mysql.data.8.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mysql.data.entityframeworkcore\\8.0.11\\mysql.data.entityframeworkcore.8.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\1.6.1\\netstandard.library.1.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json.bson\\1.0.2\\newtonsoft.json.bson.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.context\\5.1.0\\nito.asyncex.context.5.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.coordination\\5.1.0\\nito.asyncex.coordination.5.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.tasks\\5.1.0\\nito.asyncex.tasks.5.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.collections.deque\\1.1.0\\nito.collections.deque.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.disposables\\2.2.0\\nito.disposables.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuglify\\1.13.8\\nuglify.1.13.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pipelines.sockets.unofficial\\2.0.17\\pipelines.sockets.unofficial.2.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\remotion.linq\\2.2.0\\remotion.linq.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog\\2.8.0\\serilog.2.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.aspnetcore\\2.0.0\\serilog.aspnetcore.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.logging\\2.0.2\\serilog.extensions.logging.2.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.async\\1.0.0\\serilog.sinks.async.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.file\\3.2.0\\serilog.sinks.file.3.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.rollingfile\\3.3.0\\serilog.sinks.rollingfile.3.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.abstractions\\0.8.0\\skyapm.abstractions.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.agent.aspnetcore\\0.8.0\\skyapm.agent.aspnetcore.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.core\\0.8.0\\skyapm.core.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.diagnostics.aspnetcore\\0.8.0\\skyapm.diagnostics.aspnetcore.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.diagnostics.entityframeworkcore\\0.8.0\\skyapm.diagnostics.entityframeworkcore.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.diagnostics.entityframeworkcore.npgsql\\0.8.0\\skyapm.diagnostics.entityframeworkcore.npgsql.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.diagnostics.entityframeworkcore.pomelo.mysql\\0.8.0\\skyapm.diagnostics.entityframeworkcore.pomelo.mysql.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.diagnostics.entityframeworkcore.sqlite\\0.8.0\\skyapm.diagnostics.entityframeworkcore.sqlite.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.diagnostics.httpclient\\0.8.0\\skyapm.diagnostics.httpclient.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.diagnostics.sqlclient\\0.8.0\\skyapm.diagnostics.sqlclient.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.transport.grpc\\0.8.0\\skyapm.transport.grpc.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.transport.grpc.protocol\\0.8.0\\skyapm.transport.grpc.protocol.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.utilities.configuration\\0.8.0\\skyapm.utilities.configuration.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.utilities.dependencyinjection\\0.8.0\\skyapm.utilities.dependencyinjection.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skyapm.utilities.logging\\0.8.0\\skyapm.utilities.logging.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stackexchange.redis\\2.0.593\\stackexchange.redis.2.0.593.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore\\5.5.1\\swashbuckle.aspnetcore.5.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swagger\\5.5.1\\swashbuckle.aspnetcore.swagger.5.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggergen\\5.5.1\\swashbuckle.aspnetcore.swaggergen.5.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggerui\\5.5.1\\swashbuckle.aspnetcore.swaggerui.5.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.appcontext\\4.3.0\\system.appcontext.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.0\\system.buffers.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\5.0.0\\system.collections.immutable.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.nongeneric\\4.3.0\\system.collections.nongeneric.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.specialized\\4.3.0\\system.collections.specialized.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.annotations\\5.0.0\\system.componentmodel.annotations.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.primitives\\4.3.0\\system.componentmodel.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.typeconverter\\4.3.0\\system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\4.5.0\\system.configuration.configurationmanager.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.contracts\\4.3.0\\system.diagnostics.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\4.5.1\\system.diagnostics.diagnosticsource.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\4.5.0\\system.diagnostics.performancecounter.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracesource\\4.3.0\\system.diagnostics.tracesource.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.dynamic.runtime\\4.3.0\\system.dynamic.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\5.2.1\\system.identitymodel.tokens.jwt.5.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.interactive.async\\3.2.0\\system.interactive.async.3.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.compression.zipfile\\4.3.0\\system.io.compression.zipfile.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.hashing\\8.0.0\\system.io.hashing.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\4.5.1\\system.io.pipelines.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.dynamic.core\\1.2.9\\system.linq.dynamic.core.1.2.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.queryable\\4.3.0\\system.linq.queryable.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http\\4.3.0\\system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.sockets\\4.3.0\\system.net.sockets.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.private.datacontractserialization\\4.3.0\\system.private.datacontractserialization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reactive\\6.0.1\\system.reactive.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\5.0.0\\system.reflection.metadata.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.7.1\\system.runtime.compilerservices.unsafe.4.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.loader\\4.3.0\\system.runtime.loader.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.serialization.primitives\\4.3.0\\system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.serialization.xml\\4.3.0\\system.runtime.serialization.xml.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\4.5.0\\system.security.accesscontrol.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.claims\\4.3.0\\system.security.claims.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\4.5.0\\system.security.cryptography.cng.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\4.5.0\\system.security.cryptography.pkcs.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\4.5.0\\system.security.cryptography.protecteddata.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\4.5.0\\system.security.cryptography.xml.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\4.5.0\\system.security.permissions.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal\\4.3.0\\system.security.principal.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.5.0\\system.security.principal.windows.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\4.5.1\\system.text.encoding.codepages.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\4.5.0\\system.text.encodings.web.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.regularexpressions\\4.3.0\\system.text.regularexpressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.channels\\4.5.0\\system.threading.channels.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.thread\\4.0.0\\system.threading.thread.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.threadpool\\4.0.10\\system.threading.threadpool.4.0.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.timer\\4.3.0\\system.threading.timer.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.valuetuple\\4.5.0\\system.valuetuple.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xmldocument\\4.3.0\\system.xml.xmldocument.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xmlserializer\\4.3.0\\system.xml.xmlserializer.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\tea\\1.0.4\\tea.1.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\timezoneconverter\\3.4.0\\timezoneconverter.3.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.account.application\\0.19.0\\volo.abp.account.application.0.19.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.account.application.contracts\\0.19.0\\volo.abp.account.application.contracts.0.19.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.account.httpapi\\0.19.0\\volo.abp.account.httpapi.0.19.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.apiversioning.abstractions\\4.3.0\\volo.abp.apiversioning.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore\\4.3.0\\volo.abp.aspnetcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.multitenancy\\0.5.0\\volo.abp.aspnetcore.multitenancy.0.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc\\4.3.0\\volo.abp.aspnetcore.mvc.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.contracts\\4.3.0\\volo.abp.aspnetcore.mvc.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui\\0.5.0\\volo.abp.aspnetcore.mvc.ui.0.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.bootstrap\\0.5.0\\volo.abp.aspnetcore.mvc.ui.bootstrap.0.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.bundling\\0.5.0\\volo.abp.aspnetcore.mvc.ui.bundling.0.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.multitenancy\\0.5.0\\volo.abp.aspnetcore.mvc.ui.multitenancy.0.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.packages\\0.5.0\\volo.abp.aspnetcore.mvc.ui.packages.0.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.theme.shared\\0.5.0\\volo.abp.aspnetcore.mvc.ui.theme.shared.0.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.serilog\\2.0.0\\volo.abp.aspnetcore.serilog.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditing\\4.3.0\\volo.abp.auditing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditlogging.domain\\0.4.1\\volo.abp.auditlogging.domain.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditlogging.domain.shared\\0.4.1\\volo.abp.auditlogging.domain.shared.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditlogging.entityframeworkcore\\0.4.1\\volo.abp.auditlogging.entityframeworkcore.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.authorization\\4.3.0\\volo.abp.authorization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.authorization.abstractions\\4.3.0\\volo.abp.authorization.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.autofac\\0.3.0\\volo.abp.autofac.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.automapper\\0.15.0\\volo.abp.automapper.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs\\0.4.1\\volo.abp.backgroundjobs.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs.abstractions\\0.4.1\\volo.abp.backgroundjobs.abstractions.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs.domain\\0.4.1\\volo.abp.backgroundjobs.domain.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs.domain.shared\\0.4.1\\volo.abp.backgroundjobs.domain.shared.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs.entityframeworkcore\\0.4.1\\volo.abp.backgroundjobs.entityframeworkcore.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundworkers\\0.4.1\\volo.abp.backgroundworkers.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.caching\\3.0.0\\volo.abp.caching.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.caching.stackexchangeredis\\3.0.0\\volo.abp.caching.stackexchangeredis.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.castle.core\\0.3.0\\volo.abp.castle.core.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.core\\4.3.0\\volo.abp.core.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.data\\4.3.0\\volo.abp.data.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.application\\4.3.0\\volo.abp.ddd.application.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.application.contracts\\4.3.0\\volo.abp.ddd.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.domain\\4.3.0\\volo.abp.ddd.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.entityframeworkcore\\0.15.0\\volo.abp.entityframeworkcore.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.entityframeworkcore.mysql\\0.3.6\\volo.abp.entityframeworkcore.mysql.0.3.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.eventbus\\4.3.0\\volo.abp.eventbus.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.eventbus.abstractions\\4.3.0\\volo.abp.eventbus.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.exceptionhandling\\4.3.0\\volo.abp.exceptionhandling.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.application\\0.15.0\\volo.abp.featuremanagement.application.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.application.contracts\\0.15.0\\volo.abp.featuremanagement.application.contracts.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.domain\\0.15.0\\volo.abp.featuremanagement.domain.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.domain.shared\\0.15.0\\volo.abp.featuremanagement.domain.shared.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.entityframeworkcore\\0.15.0\\volo.abp.featuremanagement.entityframeworkcore.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.httpapi\\0.15.0\\volo.abp.featuremanagement.httpapi.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.features\\4.3.0\\volo.abp.features.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.globalfeatures\\4.3.0\\volo.abp.globalfeatures.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.guids\\4.3.0\\volo.abp.guids.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.http\\4.3.0\\volo.abp.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.http.abstractions\\4.3.0\\volo.abp.http.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.application\\0.3.0\\volo.abp.identity.application.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.application.contracts\\0.3.0\\volo.abp.identity.application.contracts.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.domain\\0.3.0\\volo.abp.identity.domain.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.domain.shared\\0.3.0\\volo.abp.identity.domain.shared.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.entityframeworkcore\\0.3.0\\volo.abp.identity.entityframeworkcore.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.httpapi\\0.3.0\\volo.abp.identity.httpapi.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identityserver.domain\\0.6.0\\volo.abp.identityserver.domain.0.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identityserver.domain.shared\\0.6.0\\volo.abp.identityserver.domain.shared.0.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identityserver.entityframeworkcore\\0.6.0\\volo.abp.identityserver.entityframeworkcore.0.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.json\\4.3.0\\volo.abp.json.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.localization.abstractions\\4.3.0\\volo.abp.localization.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.minify\\4.3.0\\volo.abp.minify.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.multitenancy\\4.3.0\\volo.abp.multitenancy.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.multitenancy.abstractions\\0.5.0\\volo.abp.multitenancy.abstractions.0.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectextending\\2.4.0\\volo.abp.objectextending.2.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectmapping\\4.3.0\\volo.abp.objectmapping.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.application\\0.3.0\\volo.abp.permissionmanagement.application.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain\\0.14.0\\volo.abp.permissionmanagement.domain.0.14.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain.identity\\0.13.0\\volo.abp.permissionmanagement.domain.identity.0.13.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain.identityserver\\0.14.0\\volo.abp.permissionmanagement.domain.identityserver.0.14.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain.shared\\0.3.0\\volo.abp.permissionmanagement.domain.shared.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.entityframeworkcore\\0.3.0\\volo.abp.permissionmanagement.entityframeworkcore.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.httpapi\\0.13.0\\volo.abp.permissionmanagement.httpapi.0.13.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.security\\4.3.0\\volo.abp.security.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.serialization\\3.0.0\\volo.abp.serialization.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.application\\4.3.0\\volo.abp.settingmanagement.application.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.application.contracts\\4.3.0\\volo.abp.settingmanagement.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.entityframeworkcore\\0.3.0\\volo.abp.settingmanagement.entityframeworkcore.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.httpapi\\4.3.0\\volo.abp.settingmanagement.httpapi.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settings\\4.3.0\\volo.abp.settings.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.specifications\\4.3.0\\volo.abp.specifications.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.swashbuckle\\3.3.0\\volo.abp.swashbuckle.3.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.tenantmanagement.application\\0.3.0\\volo.abp.tenantmanagement.application.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.tenantmanagement.application.contracts\\0.3.0\\volo.abp.tenantmanagement.application.contracts.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.tenantmanagement.domain\\0.3.0\\volo.abp.tenantmanagement.domain.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.tenantmanagement.domain.shared\\0.3.0\\volo.abp.tenantmanagement.domain.shared.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.tenantmanagement.entityframeworkcore\\0.3.0\\volo.abp.tenantmanagement.entityframeworkcore.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.tenantmanagement.httpapi\\0.3.0\\volo.abp.tenantmanagement.httpapi.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.threading\\4.3.0\\volo.abp.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.timing\\4.3.0\\volo.abp.timing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ui\\4.3.0\\volo.abp.ui.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ui.navigation\\4.3.0\\volo.abp.ui.navigation.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.uow\\4.3.0\\volo.abp.uow.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.abstractions\\0.3.0\\volo.abp.users.abstractions.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.domain\\0.3.0\\volo.abp.users.domain.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.domain.shared\\0.3.0\\volo.abp.users.domain.shared.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.entityframeworkcore\\0.3.0\\volo.abp.users.entityframeworkcore.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.virtualfilesystem\\4.3.0\\volo.abp.virtualfilesystem.4.3.0.nupkg.sha512"], "logs": [{"code": "NU1604", "level": "Warning", "message": "项目依赖项 FluentFTP 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "FluentFTP", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Microsoft.AspNetCore.Authentication.JwtBearer 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Microsoft.AspNetCore.Authentication.JwtBearer", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Microsoft.AspNetCore.DataProtection.StackExchangeRedis 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Microsoft.AspNetCore.DataProtection.StackExchangeRedis", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Microsoft.ICU.ICU4C.Runtime 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Microsoft.ICU.ICU4C.Runtime", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Serilog.AspNetCore 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Serilog.AspNetCore", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Serilog.Sinks.Async 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Serilog.Sinks.Async", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 SkyAPM.Agent.AspNetCore 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "SkyAPM.Agent.AspNetCore", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 TSZ.Abp.Modulies 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.Abp.Modulies", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 TSZ.Common.Core 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.Common.Core", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 TSZ.Common.Core.Helper 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.Common.Core.Helper", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 TSZ.Common.OfficeExtends 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.Common.OfficeExtends", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 TSZ.Infrastructures.Consul 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.Infrastructures.Consul", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 TSZ.ServiceProxy.Caching 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.ServiceProxy.Caching", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 TSZ.ServiceProxy.Config 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.ServiceProxy.Config", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 TSZ.ServiceProxy.SeriLog 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.ServiceProxy.SeriLog", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.AspNetCore.Serilog 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.AspNetCore.Serilog", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.Autofac 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Autofac", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.Caching.CSRedis 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Caching.CSRedis", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.Caching.StackExchangeRedis 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Caching.StackExchangeRedis", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.Swashbuckle 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Swashbuckle", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Application 不提供依赖项 AlibabaCloud.SDK.Sts20150401 的下限(含)。已改为解析 AlibabaCloud.SDK.Sts20150401 1.0.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "AlibabaCloud.SDK.Sts20150401", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Application 不提供依赖项 Aliyun.OSS.SDK.NetCore 的下限(含)。已改为解析 Aliyun.OSS.SDK.NetCore 2.9.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Aliyun.OSS.SDK.NetCore", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Application.Contracts 不提供依赖项 Microsoft.AspNetCore.Mvc.Core 的下限(含)。已改为解析 Microsoft.AspNetCore.Mvc.Core 1.0.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Microsoft.AspNetCore.Mvc.Core", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.DbMigrations 不提供依赖项 Microsoft.EntityFrameworkCore.Tools 的下限(含)。已改为解析 Microsoft.EntityFrameworkCore.Tools 1.0.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Microsoft.EntityFrameworkCore.Tools", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Application 不提供依赖项 Volo.Abp.Account.Application 的下限(含)。已改为解析 Volo.Abp.Account.Application 0.19.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Account.Application", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.Account.HttpApi 0.19.0 不提供依赖项 Volo.Abp.Account.Application.Contracts 的下限(含)。已改为解析 Volo.Abp.Account.Application.Contracts 0.19.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Account.Application.Contracts", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.HttpApi 不提供依赖项 Volo.Abp.Account.HttpApi 的下限(含)。已改为解析 Volo.Abp.Account.HttpApi 0.19.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Account.HttpApi", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain 不提供依赖项 Volo.Abp.AuditLogging.Domain 的下限(含)。已改为解析 Volo.Abp.AuditLogging.Domain 0.4.1。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.AuditLogging.Domain", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.AuditLogging.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.AuditLogging.Domain.Shared 0.4.1。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.AuditLogging.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore 不提供依赖项 Volo.Abp.AuditLogging.EntityFrameworkCore 的下限(含)。已改为解析 Volo.Abp.AuditLogging.EntityFrameworkCore 0.4.1。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.AuditLogging.EntityFrameworkCore", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain 不提供依赖项 Volo.Abp.BackgroundJobs.Domain 的下限(含)。已改为解析 Volo.Abp.BackgroundJobs.Domain 0.4.1。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.BackgroundJobs.Domain", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.BackgroundJobs.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.BackgroundJobs.Domain.Shared 0.4.1。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.BackgroundJobs.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore 不提供依赖项 Volo.Abp.BackgroundJobs.EntityFrameworkCore 的下限(含)。已改为解析 Volo.Abp.BackgroundJobs.EntityFrameworkCore 0.4.1。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.BackgroundJobs.EntityFrameworkCore", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore 不提供依赖项 Volo.Abp.EntityFrameworkCore.MySQL 的下限(含)。已改为解析 Volo.Abp.EntityFrameworkCore.MySQL 0.3.6。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.EntityFrameworkCore.MySQL", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Application 不提供依赖项 Volo.Abp.FeatureManagement.Application 的下限(含)。已改为解析 Volo.Abp.FeatureManagement.Application 0.15.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.FeatureManagement.Application", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.FeatureManagement.HttpApi 0.15.0 不提供依赖项 Volo.Abp.FeatureManagement.Application.Contracts 的下限(含)。已改为解析 Volo.Abp.FeatureManagement.Application.Contracts 0.15.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.FeatureManagement.Application.Contracts", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.FeatureManagement.Application 0.15.0 不提供依赖项 Volo.Abp.FeatureManagement.Domain 的下限(含)。已改为解析 Volo.Abp.FeatureManagement.Domain 0.15.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.FeatureManagement.Domain", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.FeatureManagement.Application.Contracts 0.15.0 不提供依赖项 Volo.Abp.FeatureManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.FeatureManagement.Domain.Shared 0.15.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.FeatureManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore 不提供依赖项 Volo.Abp.FeatureManagement.EntityFrameworkCore 的下限(含)。已改为解析 Volo.Abp.FeatureManagement.EntityFrameworkCore 0.15.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.FeatureManagement.EntityFrameworkCore", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.HttpApi 不提供依赖项 Volo.Abp.FeatureManagement.HttpApi 的下限(含)。已改为解析 Volo.Abp.FeatureManagement.HttpApi 0.15.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.FeatureManagement.HttpApi", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Application 不提供依赖项 Volo.Abp.Identity.Application 的下限(含)。已改为解析 Volo.Abp.Identity.Application 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Identity.Application", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.Identity.HttpApi 0.3.0 不提供依赖项 Volo.Abp.Identity.Application.Contracts 的下限(含)。已改为解析 Volo.Abp.Identity.Application.Contracts 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Identity.Application.Contracts", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.Identity.Application 0.3.0 不提供依赖项 Volo.Abp.Identity.Domain 的下限(含)。已改为解析 Volo.Abp.Identity.Domain 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Identity.Domain", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.Identity.Application.Contracts 0.3.0 不提供依赖项 Volo.Abp.Identity.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.Identity.Domain.Shared 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Identity.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore 不提供依赖项 Volo.Abp.Identity.EntityFrameworkCore 的下限(含)。已改为解析 Volo.Abp.Identity.EntityFrameworkCore 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Identity.EntityFrameworkCore", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.HttpApi 不提供依赖项 Volo.Abp.Identity.HttpApi 的下限(含)。已改为解析 Volo.Abp.Identity.HttpApi 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Identity.HttpApi", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain 不提供依赖项 Volo.Abp.IdentityServer.Domain 的下限(含)。已改为解析 Volo.Abp.IdentityServer.Domain 0.6.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.IdentityServer.Domain", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.IdentityServer.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.IdentityServer.Domain.Shared 0.6.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.IdentityServer.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore 不提供依赖项 Volo.Abp.IdentityServer.EntityFrameworkCore 的下限(含)。已改为解析 Volo.Abp.IdentityServer.EntityFrameworkCore 0.6.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.IdentityServer.EntityFrameworkCore", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Application.Contracts 不提供依赖项 Volo.Abp.ObjectExtending 的下限(含)。已改为解析 Volo.Abp.ObjectExtending 2.4.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.ObjectExtending", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Application 不提供依赖项 Volo.Abp.PermissionManagement.Application 的下限(含)。已改为解析 Volo.Abp.PermissionManagement.Application 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.PermissionManagement.Application", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain 不提供依赖项 Volo.Abp.PermissionManagement.Domain.Identity 的下限(含)。已改为解析 Volo.Abp.PermissionManagement.Domain.Identity 0.13.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.PermissionManagement.Domain.Identity", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain 不提供依赖项 Volo.Abp.PermissionManagement.Domain.IdentityServer 的下限(含)。已改为解析 Volo.Abp.PermissionManagement.Domain.IdentityServer 0.14.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.PermissionManagement.Domain.IdentityServer", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.PermissionManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.PermissionManagement.Domain.Shared 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.PermissionManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore 不提供依赖项 Volo.Abp.PermissionManagement.EntityFrameworkCore 的下限(含)。已改为解析 Volo.Abp.PermissionManagement.EntityFrameworkCore 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.PermissionManagement.EntityFrameworkCore", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.HttpApi 不提供依赖项 Volo.Abp.PermissionManagement.HttpApi 的下限(含)。已改为解析 Volo.Abp.PermissionManagement.HttpApi 0.13.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.PermissionManagement.HttpApi", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Application 不提供依赖项 Volo.Abp.SettingManagement.Application 的下限(含)。已改为解析 Volo.Abp.SettingManagement.Application 4.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.SettingManagement.Application", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.SettingManagement.HttpApi 4.3.0 不提供依赖项 Volo.Abp.SettingManagement.Application.Contracts 的下限(含)。已改为解析 Volo.Abp.SettingManagement.Application.Contracts 4.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.SettingManagement.Application.Contracts", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore 不提供依赖项 Volo.Abp.SettingManagement.EntityFrameworkCore 的下限(含)。已改为解析 Volo.Abp.SettingManagement.EntityFrameworkCore 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.SettingManagement.EntityFrameworkCore", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.HttpApi 不提供依赖项 Volo.Abp.SettingManagement.HttpApi 的下限(含)。已改为解析 Volo.Abp.SettingManagement.HttpApi 4.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.SettingManagement.HttpApi", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Application 不提供依赖项 Volo.Abp.TenantManagement.Application 的下限(含)。已改为解析 Volo.Abp.TenantManagement.Application 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.TenantManagement.Application", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.TenantManagement.HttpApi 0.3.0 不提供依赖项 Volo.Abp.TenantManagement.Application.Contracts 的下限(含)。已改为解析 Volo.Abp.TenantManagement.Application.Contracts 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.TenantManagement.Application.Contracts", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.TenantManagement.Application 0.3.0 不提供依赖项 Volo.Abp.TenantManagement.Domain 的下限(含)。已改为解析 Volo.Abp.TenantManagement.Domain 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.TenantManagement.Domain", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.TenantManagement.Application.Contracts 0.3.0 不提供依赖项 Volo.Abp.TenantManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.TenantManagement.Domain.Shared 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.TenantManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore 不提供依赖项 Volo.Abp.TenantManagement.EntityFrameworkCore 的下限(含)。已改为解析 Volo.Abp.TenantManagement.EntityFrameworkCore 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.TenantManagement.EntityFrameworkCore", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.HttpApi 不提供依赖项 Volo.Abp.TenantManagement.HttpApi 的下限(含)。已改为解析 Volo.Abp.TenantManagement.HttpApi 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.TenantManagement.HttpApi", "targetGraphs": ["net9.0"]}, {"code": "NU1101", "level": "Error", "message": "找不到包 TSZ.Common.Core。源 Microsoft Visual Studio Offline Packages, nuget.org, 国内 中不存在具有此 ID 的包", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.Common.Core", "targetGraphs": ["net9.0"]}, {"code": "NU1101", "level": "Error", "message": "找不到包 TSZ.Common.Core.Helper。源 Microsoft Visual Studio Offline Packages, nuget.org, 国内 中不存在具有此 ID 的包", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.Common.Core.Helper", "targetGraphs": ["net9.0"]}, {"code": "NU1101", "level": "Error", "message": "找不到包 TSZ.Common.OfficeExtends。源 Microsoft Visual Studio Offline Packages, nuget.org, 国内 中不存在具有此 ID 的包", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.Common.OfficeExtends", "targetGraphs": ["net9.0"]}, {"code": "NU1101", "level": "Error", "message": "找不到包 TSZ.Abp.Modulies。源 Microsoft Visual Studio Offline Packages, nuget.org, 国内 中不存在具有此 ID 的包", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.Abp.Modulies", "targetGraphs": ["net9.0"]}, {"code": "NU1101", "level": "Error", "message": "找不到包 TSZ.Infrastructures.Consul。源 Microsoft Visual Studio Offline Packages, nuget.org, 国内 中不存在具有此 ID 的包", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.Infrastructures.Consul", "targetGraphs": ["net9.0"]}, {"code": "NU1101", "level": "Error", "message": "找不到包 Volo.Abp.Caching.CSRedis。源 Microsoft Visual Studio Offline Packages, nuget.org, 国内 中不存在具有此 ID 的包", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Caching.CSRedis", "targetGraphs": ["net9.0"]}, {"code": "NU1101", "level": "Error", "message": "找不到包 TSZ.ServiceProxy.Caching。源 Microsoft Visual Studio Offline Packages, nuget.org, 国内 中不存在具有此 ID 的包", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.ServiceProxy.Caching", "targetGraphs": ["net9.0"]}, {"code": "NU1101", "level": "Error", "message": "找不到包 TSZ.ServiceProxy.Config。源 Microsoft Visual Studio Offline Packages, nuget.org, 国内 中不存在具有此 ID 的包", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.ServiceProxy.Config", "targetGraphs": ["net9.0"]}, {"code": "NU1101", "level": "Error", "message": "找不到包 TSZ.ServiceProxy.SeriLog。源 Microsoft Visual Studio Offline Packages, nuget.org, 国内 中不存在具有此 ID 的包", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "TSZ.ServiceProxy.SeriLog", "targetGraphs": ["net9.0"]}, {"code": "NU1701", "level": "Warning", "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net9.0”还原包“BouncyCastle 1.8.5”。此包可能与项目不完全兼容。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "BouncyCastle", "targetGraphs": ["net9.0"]}, {"code": "NU1701", "level": "Warning", "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net9.0”还原包“FluentFTP 16.0.13”。此包可能与项目不完全兼容。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "FluentFTP", "targetGraphs": ["net9.0"]}, {"code": "NU1701", "level": "Warning", "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net9.0”还原包“Microsoft.Tpl.Dataflow 4.5.24”。此包可能与项目不完全兼容。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Microsoft.Tpl.Dataflow", "targetGraphs": ["net9.0"]}, {"code": "NU1701", "level": "Warning", "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net9.0”还原包“Serilog.Sinks.Async 1.0.0”。此包可能与项目不完全兼容。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Serilog.Sinks.Async", "targetGraphs": ["net9.0"]}, {"code": "NU1107", "level": "Error", "message": "Volo.Abp.Validation 中检测到版本冲突。直接安装/引用 Volo.Abp.Validation 0.3.0 到项目 TSZ.ServiceBase.FileStorageCenter.HttpApi.Host 可解决此问题。 \r\n TSZ.ServiceBase.FileStorageCenter.HttpApi.Host -> TSZ.ServiceBase.FileStorageCenter.HttpApi -> TSZ.ServiceBase.FileStorageCenter.Application.Contracts -> TSZ.ServiceBase.FileStorageCenter.Domain.Shared -> Volo.Abp.Validation \r\n TSZ.ServiceBase.FileStorageCenter.HttpApi.Host -> Volo.Abp.AspNetCore.Serilog 2.0.0 -> Volo.Abp.AspNetCore 4.3.0 -> Volo.Abp.Validation (>= 4.3.0).", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "libraryId": "Volo.Abp.Validation", "targetGraphs": ["net9.0"]}]}