{"version": 2, "dgSpecHash": "E3A+vvf4ffM=", "success": true, "projectFilePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\automapper\\6.2.2\\automapper.6.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\configureawait.fody\\3.3.1\\configureawait.fody.3.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fody\\6.0.2\\fody.6.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\identitymodel\\3.6.1\\identitymodel.3.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\identityserver4\\2.2.0\\identityserver4.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\identityserver4.aspnetidentity\\2.1.0\\identityserver4.aspnetidentity.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jetbrains.annotations\\2019.1.3\\jetbrains.annotations.2019.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication\\2.1.0\\microsoft.aspnetcore.authentication.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.abstractions\\2.1.0\\microsoft.aspnetcore.authentication.abstractions.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.cookies\\2.1.0\\microsoft.aspnetcore.authentication.cookies.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.core\\2.1.0\\microsoft.aspnetcore.authentication.core.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.oauth\\2.0.3\\microsoft.aspnetcore.authentication.oauth.2.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.openidconnect\\2.0.3\\microsoft.aspnetcore.authentication.openidconnect.2.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\2.2.0\\microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cors\\2.0.2\\microsoft.aspnetcore.cors.2.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.internal\\2.1.0\\microsoft.aspnetcore.cryptography.internal.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.keyderivation\\2.1.0\\microsoft.aspnetcore.cryptography.keyderivation.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection\\2.1.0\\microsoft.aspnetcore.dataprotection.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.abstractions\\2.1.0\\microsoft.aspnetcore.dataprotection.abstractions.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.abstractions\\2.1.0\\microsoft.aspnetcore.hosting.abstractions.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.server.abstractions\\2.1.0\\microsoft.aspnetcore.hosting.server.abstractions.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http\\2.1.0\\microsoft.aspnetcore.http.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.abstractions\\2.1.0\\microsoft.aspnetcore.http.abstractions.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.extensions\\2.1.0\\microsoft.aspnetcore.http.extensions.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.features\\2.1.0\\microsoft.aspnetcore.http.features.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity\\2.1.0\\microsoft.aspnetcore.identity.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.webutilities\\2.1.0\\microsoft.aspnetcore.webutilities.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.3.0\\microsoft.csharp.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\2.2.0\\microsoft.extensions.caching.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\2.2.0\\microsoft.extensions.caching.memory.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\3.1.2\\microsoft.extensions.configuration.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\3.1.2\\microsoft.extensions.configuration.abstractions.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\3.1.2\\microsoft.extensions.configuration.binder.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\3.1.2\\microsoft.extensions.configuration.commandline.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\3.1.2\\microsoft.extensions.configuration.environmentvariables.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\3.1.2\\microsoft.extensions.configuration.fileextensions.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\3.1.2\\microsoft.extensions.configuration.json.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\3.1.2\\microsoft.extensions.configuration.usersecrets.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\3.1.2\\microsoft.extensions.dependencyinjection.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\3.1.2\\microsoft.extensions.dependencyinjection.abstractions.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\3.1.2\\microsoft.extensions.fileproviders.abstractions.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.composite\\2.1.0\\microsoft.extensions.fileproviders.composite.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.embedded\\1.0.0\\microsoft.extensions.fileproviders.embedded.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\3.1.2\\microsoft.extensions.fileproviders.physical.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\3.1.2\\microsoft.extensions.filesystemglobbing.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\3.1.2\\microsoft.extensions.hosting.abstractions.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.identity.core\\2.1.0\\microsoft.extensions.identity.core.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization\\3.1.2\\microsoft.extensions.localization.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization.abstractions\\3.1.2\\microsoft.extensions.localization.abstractions.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\3.1.2\\microsoft.extensions.logging.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\3.1.2\\microsoft.extensions.logging.abstractions.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\2.1.0\\microsoft.extensions.objectpool.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\3.1.2\\microsoft.extensions.options.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\3.1.2\\microsoft.extensions.options.configurationextensions.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\3.1.2\\microsoft.extensions.primitives.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.webencoders\\2.1.0\\microsoft.extensions.webencoders.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\5.2.1\\microsoft.identitymodel.logging.5.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\2.1.4\\microsoft.identitymodel.protocols.2.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\2.1.4\\microsoft.identitymodel.protocols.openidconnect.2.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\5.2.1\\microsoft.identitymodel.tokens.5.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.http.headers\\2.1.0\\microsoft.net.http.headers.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\2.0.0\\microsoft.netcore.platforms.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.5.0\\microsoft.win32.registry.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\1.6.1\\netstandard.library.1.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\12.0.1\\newtonsoft.json.12.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.context\\5.0.0\\nito.asyncex.context.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.coordination\\5.0.0\\nito.asyncex.coordination.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.tasks\\5.0.0\\nito.asyncex.tasks.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.collections.deque\\1.0.4\\nito.collections.deque.1.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.disposables\\2.0.0\\nito.disposables.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.appcontext\\4.3.0\\system.appcontext.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.0\\system.buffers.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\1.7.0\\system.collections.immutable.1.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.nongeneric\\4.3.0\\system.collections.nongeneric.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.specialized\\4.3.0\\system.collections.specialized.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.annotations\\4.7.0\\system.componentmodel.annotations.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.primitives\\4.3.0\\system.componentmodel.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.typeconverter\\4.3.0\\system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.contracts\\4.3.0\\system.diagnostics.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\4.3.0\\system.diagnostics.diagnosticsource.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.dynamic.runtime\\4.3.0\\system.dynamic.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\5.2.1\\system.identitymodel.tokens.jwt.5.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.compression.zipfile\\4.3.0\\system.io.compression.zipfile.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.dynamic.core\\1.0.19\\system.linq.dynamic.core.1.0.19.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.queryable\\4.3.0\\system.linq.queryable.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http\\4.3.0\\system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.sockets\\4.3.0\\system.net.sockets.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.private.datacontractserialization\\4.3.0\\system.private.datacontractserialization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.loader\\4.3.0\\system.runtime.loader.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.serialization.primitives\\4.3.0\\system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.serialization.xml\\4.3.0\\system.runtime.serialization.xml.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\4.5.0\\system.security.accesscontrol.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.claims\\4.3.0\\system.security.claims.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\4.5.0\\system.security.cryptography.cng.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\4.5.0\\system.security.cryptography.pkcs.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\4.5.0\\system.security.cryptography.xml.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\4.5.0\\system.security.permissions.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal\\4.3.0\\system.security.principal.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.5.0\\system.security.principal.windows.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\4.5.0\\system.text.encodings.web.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.regularexpressions\\4.3.0\\system.text.regularexpressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.3.0\\system.threading.tasks.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.timer\\4.3.0\\system.threading.timer.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xmldocument\\4.3.0\\system.xml.xmldocument.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xmlserializer\\4.3.0\\system.xml.xmlserializer.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditing\\0.15.0\\volo.abp.auditing.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditlogging.domain\\0.4.1\\volo.abp.auditlogging.domain.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditlogging.domain.shared\\0.4.1\\volo.abp.auditlogging.domain.shared.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.authorization\\0.14.0\\volo.abp.authorization.0.14.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.automapper\\0.6.0\\volo.abp.automapper.0.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs\\0.4.1\\volo.abp.backgroundjobs.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs.abstractions\\0.4.1\\volo.abp.backgroundjobs.abstractions.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs.domain\\0.4.1\\volo.abp.backgroundjobs.domain.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs.domain.shared\\0.4.1\\volo.abp.backgroundjobs.domain.shared.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundworkers\\0.4.1\\volo.abp.backgroundworkers.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.caching\\0.15.0\\volo.abp.caching.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.core\\2.4.0\\volo.abp.core.2.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.data\\0.15.0\\volo.abp.data.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.domain\\0.15.0\\volo.abp.ddd.domain.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.emailing\\0.3.0\\volo.abp.emailing.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.eventbus\\0.15.0\\volo.abp.eventbus.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.domain\\0.15.0\\volo.abp.featuremanagement.domain.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.domain.shared\\0.15.0\\volo.abp.featuremanagement.domain.shared.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.features\\0.15.0\\volo.abp.features.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.guids\\0.15.0\\volo.abp.guids.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.domain\\0.3.0\\volo.abp.identity.domain.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.domain.shared\\0.3.0\\volo.abp.identity.domain.shared.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identityserver.domain\\0.6.0\\volo.abp.identityserver.domain.0.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identityserver.domain.shared\\0.6.0\\volo.abp.identityserver.domain.shared.0.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.json\\0.15.0\\volo.abp.json.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.localization\\0.3.0\\volo.abp.localization.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.localization.abstractions\\0.15.0\\volo.abp.localization.abstractions.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.multitenancy.abstractions\\0.15.0\\volo.abp.multitenancy.abstractions.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectextending\\2.4.0\\volo.abp.objectextending.2.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectmapping\\0.15.0\\volo.abp.objectmapping.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain\\0.14.0\\volo.abp.permissionmanagement.domain.0.14.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain.identity\\0.13.0\\volo.abp.permissionmanagement.domain.identity.0.13.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain.identityserver\\0.14.0\\volo.abp.permissionmanagement.domain.identityserver.0.14.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain.shared\\0.3.0\\volo.abp.permissionmanagement.domain.shared.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.security\\0.15.0\\volo.abp.security.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.serialization\\0.15.0\\volo.abp.serialization.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.domain\\0.3.0\\volo.abp.settingmanagement.domain.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.domain.shared\\0.3.0\\volo.abp.settingmanagement.domain.shared.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settings\\0.3.0\\volo.abp.settings.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.tenantmanagement.domain\\0.3.0\\volo.abp.tenantmanagement.domain.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.tenantmanagement.domain.shared\\0.3.0\\volo.abp.tenantmanagement.domain.shared.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.threading\\0.15.0\\volo.abp.threading.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.timing\\0.15.0\\volo.abp.timing.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ui\\0.3.0\\volo.abp.ui.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.uow\\0.15.0\\volo.abp.uow.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.abstractions\\0.3.0\\volo.abp.users.abstractions.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.domain\\0.3.0\\volo.abp.users.domain.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.domain.shared\\0.3.0\\volo.abp.users.domain.shared.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.validation\\0.3.0\\volo.abp.validation.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.virtualfilesystem\\0.3.0\\volo.abp.virtualfilesystem.0.3.0.nupkg.sha512"], "logs": [{"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.AuditLogging.Domain 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.AuditLogging.Domain", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.BackgroundJobs.Domain 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.BackgroundJobs.Domain", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.Emailing 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.Emailing", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.FeatureManagement.Domain 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.FeatureManagement.Domain", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.Identity.Domain 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.Identity.Domain", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.IdentityServer.Domain 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.IdentityServer.Domain", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.PermissionManagement.Domain.Identity 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.PermissionManagement.Domain.Identity", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.PermissionManagement.Domain.IdentityServer 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.PermissionManagement.Domain.IdentityServer", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.SettingManagement.Domain 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.SettingManagement.Domain", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.TenantManagement.Domain 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.TenantManagement.Domain", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Microsoft.Extensions.FileProviders.Embedded 的下限(含)。已改为解析 Microsoft.Extensions.FileProviders.Embedded 1.0.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Microsoft.Extensions.FileProviders.Embedded", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.AuditLogging.Domain 0.4.1 不提供依赖项 Volo.Abp.AuditLogging.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.AuditLogging.Domain.Shared 0.4.1。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.AuditLogging.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.BackgroundJobs.Domain 0.4.1 不提供依赖项 Volo.Abp.BackgroundJobs.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.BackgroundJobs.Domain.Shared 0.4.1。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.BackgroundJobs.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.FeatureManagement.Domain 0.15.0 不提供依赖项 Volo.Abp.FeatureManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.FeatureManagement.Domain.Shared 0.15.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.FeatureManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.Identity.Domain 0.3.0 不提供依赖项 Volo.Abp.Identity.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.Identity.Domain.Shared 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.Identity.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.IdentityServer.Domain 0.6.0 不提供依赖项 Volo.Abp.IdentityServer.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.IdentityServer.Domain.Shared 0.6.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.IdentityServer.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.Localization 的下限(含)。已改为解析 Volo.Abp.Localization 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.Localization", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.ObjectExtending 的下限(含)。已改为解析 Volo.Abp.ObjectExtending 2.4.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.ObjectExtending", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.PermissionManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.PermissionManagement.Domain.Shared 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.PermissionManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.SettingManagement.Domain 0.3.0 不提供依赖项 Volo.Abp.SettingManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.SettingManagement.Domain.Shared 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.SettingManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.TenantManagement.Domain 0.3.0 不提供依赖项 Volo.Abp.TenantManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.TenantManagement.Domain.Shared 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.TenantManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.Validation 的下限(含)。已改为解析 Volo.Abp.Validation 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "libraryId": "Volo.Abp.Validation", "targetGraphs": ["net9.0"]}]}