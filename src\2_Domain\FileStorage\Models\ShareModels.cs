using System;
using System.Collections.Generic;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    #region 分享创建相关

    /// <summary>
    /// 分享创建请求
    /// </summary>
    public class ShareCreationRequest
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        /// 分享标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 分享描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 分享类型
        /// </summary>
        public ShareType ShareType { get; set; } = ShareType.Public;

        /// <summary>
        /// 访问密码（可选）
        /// </summary>
        public string AccessPassword { get; set; }

        /// <summary>
        /// 过期时间（可选）
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 最大访问次数（可选）
        /// </summary>
        public int? MaxAccessCount { get; set; }

        /// <summary>
        /// 最大下载次数（可选）
        /// </summary>
        public int? MaxDownloadCount { get; set; }

        /// <summary>
        /// 是否允许下载
        /// </summary>
        public bool AllowDownload { get; set; } = true;

        /// <summary>
        /// 是否允许预览
        /// </summary>
        public bool AllowPreview { get; set; } = true;

        /// <summary>
        /// 是否需要登录
        /// </summary>
        public bool RequireLogin { get; set; } = false;

        /// <summary>
        /// 分享码长度
        /// </summary>
        public int ShareCodeLength { get; set; } = 8;

        /// <summary>
        /// 自定义分享码（可选）
        /// </summary>
        public string CustomShareCode { get; set; }

        /// <summary>
        /// 高级访问控制设置
        /// </summary>
        public ShareAccessControl AccessControl { get; set; }
    }

    /// <summary>
    /// 分享创建结果
    /// </summary>
    public class ShareCreationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 分享ID
        /// </summary>
        public Guid ShareId { get; set; }

        /// <summary>
        /// 分享码
        /// </summary>
        public string ShareCode { get; set; }

        /// <summary>
        /// 分享链接
        /// </summary>
        public string ShareUrl { get; set; }

        /// <summary>
        /// 二维码链接
        /// </summary>
        public string QrCodeUrl { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 创建成功的结果
        /// </summary>
        public static ShareCreationResult Success(Guid shareId, string shareCode, string shareUrl)
        {
            return new ShareCreationResult
            {
                Success = true,
                ShareId = shareId,
                ShareCode = shareCode,
                ShareUrl = shareUrl,
                CreationTime = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 创建失败的结果
        /// </summary>
        public static ShareCreationResult Failure(string errorMessage)
        {
            return new ShareCreationResult
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
    }

    #endregion

    #region 分享信息相关

    /// <summary>
    /// 分享信息结果
    /// </summary>
    public class ShareInfoResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 分享信息
        /// </summary>
        public ShareInfo ShareInfo { get; set; }

        /// <summary>
        /// 文件信息
        /// </summary>
        public FileInfo FileInfo { get; set; }

        /// <summary>
        /// 创建成功的结果
        /// </summary>
        public static ShareInfoResult Success(ShareInfo shareInfo, FileInfo fileInfo = null)
        {
            return new ShareInfoResult
            {
                Success = true,
                ShareInfo = shareInfo,
                FileInfo = fileInfo
            };
        }

        /// <summary>
        /// 创建失败的结果
        /// </summary>
        public static ShareInfoResult Failure(string errorMessage)
        {
            return new ShareInfoResult
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// 分享信息
    /// </summary>
    public class ShareInfo
    {
        /// <summary>
        /// 分享ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 分享码
        /// </summary>
        public string ShareCode { get; set; }

        /// <summary>
        /// 分享标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 分享描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 分享类型
        /// </summary>
        public ShareType ShareType { get; set; }

        /// <summary>
        /// 分享状态
        /// </summary>
        public ShareStatus Status { get; set; }

        /// <summary>
        /// 是否需要密码
        /// </summary>
        public bool RequirePassword { get; set; }

        /// <summary>
        /// 是否需要登录
        /// </summary>
        public bool RequireLogin { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 访问次数
        /// </summary>
        public int AccessCount { get; set; }

        /// <summary>
        /// 下载次数
        /// </summary>
        public int DownloadCount { get; set; }

        /// <summary>
        /// 最大访问次数
        /// </summary>
        public int? MaxAccessCount { get; set; }

        /// <summary>
        /// 最大下载次数
        /// </summary>
        public int? MaxDownloadCount { get; set; }

        /// <summary>
        /// 是否允许下载
        /// </summary>
        public bool AllowDownload { get; set; }

        /// <summary>
        /// 是否允许预览
        /// </summary>
        public bool AllowPreview { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime? LastAccessTime { get; set; }

        /// <summary>
        /// 是否已过期
        /// </summary>
        public bool IsExpired => ExpirationTime.HasValue && ExpirationTime.Value <= DateTime.UtcNow;

        /// <summary>
        /// 是否可以访问
        /// </summary>
        public bool CanAccess => Status == ShareStatus.Active && !IsExpired && 
                                 (!MaxAccessCount.HasValue || AccessCount < MaxAccessCount.Value);

        /// <summary>
        /// 是否可以下载
        /// </summary>
        public bool CanDownload => CanAccess && AllowDownload && 
                                   (!MaxDownloadCount.HasValue || DownloadCount < MaxDownloadCount.Value);
    }

    #endregion

    #region 分享访问相关

    /// <summary>
    /// 分享访问请求
    /// </summary>
    public class ShareAccessRequest
    {
        /// <summary>
        /// 分享码
        /// </summary>
        public string ShareCode { get; set; }

        /// <summary>
        /// 访问密码（如果需要）
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 访问者IP地址
        /// </summary>
        public string IPAddress { get; set; }

        /// <summary>
        /// 用户ID（如果已登录）
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 用户代理字符串
        /// </summary>
        public string UserAgent { get; set; }

        /// <summary>
        /// 引用页面
        /// </summary>
        public string Referrer { get; set; }

        /// <summary>
        /// 验证码（如果需要）
        /// </summary>
        public string Captcha { get; set; }

        /// <summary>
        /// 验证码会话ID
        /// </summary>
        public string CaptchaSessionId { get; set; }
    }

    /// <summary>
    /// 分享访问结果
    /// </summary>
    public class ShareAccessResult
    {
        /// <summary>
        /// 是否允许访问
        /// </summary>
        public bool IsAllowed { get; set; }

        /// <summary>
        /// 拒绝原因
        /// </summary>
        public string DenialReason { get; set; }

        /// <summary>
        /// 分享信息
        /// </summary>
        public ShareInfo ShareInfo { get; set; }

        /// <summary>
        /// 文件信息
        /// </summary>
        public FileInfo FileInfo { get; set; }

        /// <summary>
        /// 访问令牌（用于后续下载）
        /// </summary>
        public string AccessToken { get; set; }

        /// <summary>
        /// 令牌过期时间
        /// </summary>
        public DateTime? TokenExpirationTime { get; set; }

        /// <summary>
        /// 创建允许访问的结果
        /// </summary>
        public static ShareAccessResult Allow(ShareInfo shareInfo, FileInfo fileInfo, string accessToken = null)
        {
            return new ShareAccessResult
            {
                IsAllowed = true,
                ShareInfo = shareInfo,
                FileInfo = fileInfo,
                AccessToken = accessToken,
                TokenExpirationTime = accessToken != null ? DateTime.UtcNow.AddHours(1) : null
            };
        }

        /// <summary>
        /// 创建拒绝访问的结果
        /// </summary>
        public static ShareAccessResult Deny(string reason)
        {
            return new ShareAccessResult
            {
                IsAllowed = false,
                DenialReason = reason
            };
        }
    }

    #endregion

    #region 分享下载相关

    /// <summary>
    /// 分享下载请求
    /// </summary>
    public class ShareDownloadRequest
    {
        /// <summary>
        /// 分享码
        /// </summary>
        public string ShareCode { get; set; }

        /// <summary>
        /// 访问令牌
        /// </summary>
        public string AccessToken { get; set; }

        /// <summary>
        /// 访问者IP地址
        /// </summary>
        public string IPAddress { get; set; }

        /// <summary>
        /// 用户ID（如果已登录）
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 用户代理字符串
        /// </summary>
        public string UserAgent { get; set; }

        /// <summary>
        /// 是否为预览下载
        /// </summary>
        public bool IsPreview { get; set; } = false;
    }

    /// <summary>
    /// 分享下载结果
    /// </summary>
    public class ShareDownloadResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 下载URL
        /// </summary>
        public string DownloadUrl { get; set; }

        /// <summary>
        /// 文件流
        /// </summary>
        public System.IO.Stream FileStream { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// MIME类型
        /// </summary>
        public string MimeType { get; set; }

        /// <summary>
        /// 下载令牌过期时间
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 创建成功的结果
        /// </summary>
        public static ShareDownloadResult Success(string downloadUrl, string fileName, long fileSize, string mimeType)
        {
            return new ShareDownloadResult
            {
                Success = true,
                DownloadUrl = downloadUrl,
                FileName = fileName,
                FileSize = fileSize,
                MimeType = mimeType,
                ExpirationTime = DateTime.UtcNow.AddHours(1)
            };
        }

        /// <summary>
        /// 创建失败的结果
        /// </summary>
        public static ShareDownloadResult Failure(string errorMessage)
        {
            return new ShareDownloadResult
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
    }

    #endregion

    #region 分享管理相关

    /// <summary>
    /// 分享更新请求
    /// </summary>
    public class ShareUpdateRequest
    {
        /// <summary>
        /// 分享码
        /// </summary>
        public string ShareCode { get; set; }

        /// <summary>
        /// 分享标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 分享描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 访问密码
        /// </summary>
        public string AccessPassword { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 最大访问次数
        /// </summary>
        public int? MaxAccessCount { get; set; }

        /// <summary>
        /// 最大下载次数
        /// </summary>
        public int? MaxDownloadCount { get; set; }

        /// <summary>
        /// 是否允许下载
        /// </summary>
        public bool? AllowDownload { get; set; }

        /// <summary>
        /// 是否允许预览
        /// </summary>
        public bool? AllowPreview { get; set; }

        /// <summary>
        /// 是否需要登录
        /// </summary>
        public bool? RequireLogin { get; set; }

        /// <summary>
        /// 分享状态
        /// </summary>
        public ShareStatus? Status { get; set; }
    }

    /// <summary>
    /// 分享更新结果
    /// </summary>
    public class ShareUpdateResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 更新的分享信息
        /// </summary>
        public ShareInfo ShareInfo { get; set; }

        /// <summary>
        /// 创建成功的结果
        /// </summary>
        public static ShareUpdateResult Success(ShareInfo shareInfo)
        {
            return new ShareUpdateResult
            {
                Success = true,
                ShareInfo = shareInfo
            };
        }

        /// <summary>
        /// 创建失败的结果
        /// </summary>
        public static ShareUpdateResult Failure(string errorMessage)
        {
            return new ShareUpdateResult
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// 分享删除结果
    /// </summary>
    public class ShareDeleteResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 删除的分享码
        /// </summary>
        public string ShareCode { get; set; }

        /// <summary>
        /// 创建成功的结果
        /// </summary>
        public static ShareDeleteResult Success(string shareCode)
        {
            return new ShareDeleteResult
            {
                Success = true,
                ShareCode = shareCode
            };
        }

        /// <summary>
        /// 创建失败的结果
        /// </summary>
        public static ShareDeleteResult Failure(string errorMessage)
        {
            return new ShareDeleteResult
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// 分享列表结果
    /// </summary>
    public class ShareListResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 分享列表
        /// </summary>
        public List<ShareInfo> Shares { get; set; }

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ShareListResult()
        {
            Shares = new List<ShareInfo>();
        }

        /// <summary>
        /// 创建成功的结果
        /// </summary>
        public static ShareListResult Success(List<ShareInfo> shares, int totalCount)
        {
            return new ShareListResult
            {
                Success = true,
                Shares = shares,
                TotalCount = totalCount
            };
        }

        /// <summary>
        /// 创建失败的结果
        /// </summary>
        public static ShareListResult Failure(string errorMessage)
        {
            return new ShareListResult
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// 批量分享管理请求
    /// </summary>
    public class BatchShareManagementRequest
    {
        /// <summary>
        /// 分享码列表
        /// </summary>
        public List<string> ShareCodes { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public BatchShareOperation Operation { get; set; }

        /// <summary>
        /// 操作参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public BatchShareManagementRequest()
        {
            ShareCodes = new List<string>();
            Parameters = new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// 批量分享管理结果
    /// </summary>
    public class BatchShareManagementResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 成功处理的数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败的数量
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 失败详情
        /// </summary>
        public List<BatchShareOperationFailure> Failures { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public BatchShareManagementResult()
        {
            Failures = new List<BatchShareOperationFailure>();
        }
    }

    /// <summary>
    /// 批量分享操作类型
    /// </summary>
    public enum BatchShareOperation
    {
        /// <summary>
        /// 删除
        /// </summary>
        Delete = 0,

        /// <summary>
        /// 禁用
        /// </summary>
        Disable = 1,

        /// <summary>
        /// 启用
        /// </summary>
        Enable = 2,

        /// <summary>
        /// 延长有效期
        /// </summary>
        ExtendExpiration = 3,

        /// <summary>
        /// 重置访问次数
        /// </summary>
        ResetAccessCount = 4,

        /// <summary>
        /// 更新密码
        /// </summary>
        UpdatePassword = 5
    }

    /// <summary>
    /// 批量分享操作失败详情
    /// </summary>
    public class BatchShareOperationFailure
    {
        /// <summary>
        /// 分享码
        /// </summary>
        public string ShareCode { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; set; }
    }

    #endregion
}
