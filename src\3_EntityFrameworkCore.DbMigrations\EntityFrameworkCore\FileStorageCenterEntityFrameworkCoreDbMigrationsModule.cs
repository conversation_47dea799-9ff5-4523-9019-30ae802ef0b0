﻿using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Modularity;

namespace TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore
{
    [DependsOn(
        typeof(FileStorageCenterEntityFrameworkCoreModule)
        )]
    public class FileStorageCenterEntityFrameworkCoreDbMigrationsModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            context.Services.AddAbpDbContext<FileStorageCenterMigrationsDbContext>();
        }
    }
}
