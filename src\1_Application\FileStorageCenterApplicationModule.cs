﻿using Microsoft.Extensions.DependencyInjection;
using TSZ.ServiceBase.FileStorageCenter.FileStorage.Providers;
using Volo.Abp.AutoMapper;
using Volo.Abp.Modularity;

namespace TSZ.ServiceBase.FileStorageCenter
{
    /// <summary>
    /// 
    /// </summary>
    [DependsOn(
        typeof(FileStorageCenterDomainModule),
        //typeof(AbpAccountApplicationModule),
        typeof(AbpAutoMapperModule),
        typeof(FileStorageCenterApplicationContractsModule)
        //typeof(AbpIdentityApplicationModule)//update by lijie 2025-05-16
        //,
                                            //typeof(AbpPermissionManagementApplicationModule),
                                            //typeof(AbpTenantManagementApplicationModule),
                                            //typeof(AbpFeatureManagementApplicationModule),
                                            //typeof(AbpSettingManagementApplicationModule)
        )]
    public class FileStorageCenterApplicationModule : AbpModule
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            var services = context.Services;

            Configure<AbpAutoMapperOptions>(options =>
            {
                // 配置AutoMapper映射
            });

            // 注册存储提供者工厂
            services.AddSingleton<FileStorageProviderFactory>();

            // 注册存储提供者
            services.AddTransient<S3FileStorageProvider>();
        }
    }
}
