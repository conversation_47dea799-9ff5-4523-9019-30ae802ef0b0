using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage.Dto
{
    /// <summary>
    /// 文件预览请求DTO
    /// </summary>
    public class FilePreviewRequestDto
    {
        /// <summary>
        /// 预览类型
        /// </summary>
        public FilePreviewType PreviewType { get; set; } = FilePreviewType.Auto;

        /// <summary>
        /// 预览质量（1-100）
        /// </summary>
        public int Quality { get; set; } = 80;

        /// <summary>
        /// 最大宽度（像素）
        /// </summary>
        public int? MaxWidth { get; set; }

        /// <summary>
        /// 最大高度（像素）
        /// </summary>
        public int? MaxHeight { get; set; }

        /// <summary>
        /// 页码（用于PDF等多页文档）
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 是否生成缓存
        /// </summary>
        public bool EnableCache { get; set; } = true;
    }

    /// <summary>
    /// 文件预览响应DTO
    /// </summary>
    public class FilePreviewResponseDto
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        /// 预览类型
        /// </summary>
        public FilePreviewType PreviewType { get; set; }

        /// <summary>
        /// 预览内容（Base64编码或URL）
        /// </summary>
        public string PreviewContent { get; set; }

        /// <summary>
        /// 预览内容类型
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// 预览内容大小
        /// </summary>
        public long ContentSize { get; set; }

        /// <summary>
        /// 是否支持预览
        /// </summary>
        public bool IsSupported { get; set; }

        /// <summary>
        /// 预览URL（如果是URL类型）
        /// </summary>
        public string PreviewUrl { get; set; }

        /// <summary>
        /// 缩略图URL
        /// </summary>
        public string ThumbnailUrl { get; set; }

        /// <summary>
        /// 总页数（用于多页文档）
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// 预览生成时间
        /// </summary>
        public DateTime GeneratedAt { get; set; }

        /// <summary>
        /// 预览有效期
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 文件缩略图请求DTO
    /// </summary>
    public class FileThumbnailRequestDto
    {
        /// <summary>
        /// 缩略图宽度
        /// </summary>
        public int Width { get; set; } = 200;

        /// <summary>
        /// 缩略图高度
        /// </summary>
        public int Height { get; set; } = 200;

        /// <summary>
        /// 缩略图质量（1-100）
        /// </summary>
        public int Quality { get; set; } = 80;

        /// <summary>
        /// 缩放模式
        /// </summary>
        public ThumbnailScaleMode ScaleMode { get; set; } = ThumbnailScaleMode.Fit;

        /// <summary>
        /// 背景颜色（十六进制，如：#FFFFFF）
        /// </summary>
        [StringLength(7)]
        public string BackgroundColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// 是否生成缓存
        /// </summary>
        public bool EnableCache { get; set; } = true;

        /// <summary>
        /// 输出格式
        /// </summary>
        public ThumbnailFormat Format { get; set; } = ThumbnailFormat.JPEG;
    }

    /// <summary>
    /// 文件缩略图响应DTO
    /// </summary>
    public class FileThumbnailResponseDto
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        /// 缩略图内容（Base64编码）
        /// </summary>
        public string ThumbnailContent { get; set; }

        /// <summary>
        /// 缩略图URL
        /// </summary>
        public string ThumbnailUrl { get; set; }

        /// <summary>
        /// 内容类型
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// 缩略图大小
        /// </summary>
        public long ThumbnailSize { get; set; }

        /// <summary>
        /// 实际宽度
        /// </summary>
        public int ActualWidth { get; set; }

        /// <summary>
        /// 实际高度
        /// </summary>
        public int ActualHeight { get; set; }

        /// <summary>
        /// 是否生成成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedAt { get; set; }

        /// <summary>
        /// 缓存有效期
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 文件预览类型枚举
    /// </summary>
    public enum FilePreviewType
    {
        /// <summary>
        /// 自动检测
        /// </summary>
        Auto = 0,

        /// <summary>
        /// 图片预览
        /// </summary>
        Image = 1,

        /// <summary>
        /// 文本预览
        /// </summary>
        Text = 2,

        /// <summary>
        /// PDF预览
        /// </summary>
        PDF = 3,

        /// <summary>
        /// 视频预览（缩略图）
        /// </summary>
        Video = 4,

        /// <summary>
        /// 音频预览（波形图）
        /// </summary>
        Audio = 5,

        /// <summary>
        /// Office文档预览
        /// </summary>
        Office = 6,

        /// <summary>
        /// 代码预览
        /// </summary>
        Code = 7,

        /// <summary>
        /// 不支持预览
        /// </summary>
        NotSupported = 99
    }

    /// <summary>
    /// 缩略图缩放模式枚举
    /// </summary>
    public enum ThumbnailScaleMode
    {
        /// <summary>
        /// 适应（保持比例，可能有空白）
        /// </summary>
        Fit = 0,

        /// <summary>
        /// 填充（可能裁剪）
        /// </summary>
        Fill = 1,

        /// <summary>
        /// 拉伸（可能变形）
        /// </summary>
        Stretch = 2,

        /// <summary>
        /// 居中裁剪
        /// </summary>
        CenterCrop = 3
    }

    /// <summary>
    /// 缩略图格式枚举
    /// </summary>
    public enum ThumbnailFormat
    {
        /// <summary>
        /// JPEG格式
        /// </summary>
        JPEG = 0,

        /// <summary>
        /// PNG格式
        /// </summary>
        PNG = 1,

        /// <summary>
        /// WebP格式
        /// </summary>
        WebP = 2,

        /// <summary>
        /// GIF格式
        /// </summary>
        GIF = 3
    }
}
