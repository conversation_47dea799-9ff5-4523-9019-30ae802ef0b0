using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage.Dto
{
    /// <summary>
    /// 文件分享DTO
    /// </summary>
    public class FileShareDto : FullAuditedEntityDto<Guid>
    {
        /// <summary>
        /// 租户ID
        /// </summary>
        public Guid? TenantId { get; set; }

        /// <summary>
        /// 关联的文件信息ID
        /// </summary>
        public Guid FileInfoId { get; set; }

        /// <summary>
        /// 分享码
        /// </summary>
        public string ShareCode { get; set; }

        /// <summary>
        /// 分享标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 分享描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 分享类型
        /// </summary>
        public ShareType ShareType { get; set; }

        /// <summary>
        /// 分享状态
        /// </summary>
        public ShareStatus Status { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 最大访问次数
        /// </summary>
        public int? MaxAccessCount { get; set; }

        /// <summary>
        /// 当前访问次数
        /// </summary>
        public int AccessCount { get; set; }

        /// <summary>
        /// 最大下载次数
        /// </summary>
        public int? MaxDownloadCount { get; set; }

        /// <summary>
        /// 当前下载次数
        /// </summary>
        public int DownloadCount { get; set; }

        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime? LastAccessTime { get; set; }

        /// <summary>
        /// 最后访问IP地址
        /// </summary>
        public string LastAccessIP { get; set; }

        /// <summary>
        /// 是否允许下载
        /// </summary>
        public bool AllowDownload { get; set; }

        /// <summary>
        /// 是否允许预览
        /// </summary>
        public bool AllowPreview { get; set; }

        /// <summary>
        /// 是否需要登录访问
        /// </summary>
        public bool RequireLogin { get; set; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtraProperties { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// 关联的文件信息
        /// </summary>
        public FileInfoDto FileInfo { get; set; }
    }

    /// <summary>
    /// 分享统计信息DTO
    /// </summary>
    public class ShareStatisticsDto
    {
        /// <summary>
        /// 分享总数
        /// </summary>
        public long TotalCount { get; set; }

        /// <summary>
        /// 活跃分享数量
        /// </summary>
        public long ActiveCount { get; set; }

        /// <summary>
        /// 已禁用分享数量
        /// </summary>
        public long DisabledCount { get; set; }

        /// <summary>
        /// 已过期分享数量
        /// </summary>
        public long ExpiredCount { get; set; }

        /// <summary>
        /// 总访问次数
        /// </summary>
        public long TotalAccessCount { get; set; }

        /// <summary>
        /// 总下载次数
        /// </summary>
        public long TotalDownloadCount { get; set; }

        /// <summary>
        /// 平均访问次数
        /// </summary>
        public double AverageAccessCount { get; set; }

        /// <summary>
        /// 最受欢迎的分享ID
        /// </summary>
        public Guid? MostPopularShareId { get; set; }

        /// <summary>
        /// 最受欢迎的分享访问次数
        /// </summary>
        public int MostPopularAccessCount { get; set; }
    }

    /// <summary>
    /// 创建文件分享请求DTO
    /// </summary>
    public class CreateFileShareRequestDto
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        [Required]
        [StringLength(32)]
        public string FileId { get; set; }

        /// <summary>
        /// 分享标题
        /// </summary>
        [StringLength(200)]
        public string Title { get; set; }

        /// <summary>
        /// 分享描述
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// 访问密码（可选）
        /// </summary>
        [StringLength(50)]
        public string AccessPassword { get; set; }

        /// <summary>
        /// 分享类型
        /// </summary>
        public ShareType ShareType { get; set; } = ShareType.Public;

        /// <summary>
        /// 过期时间（可选）
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 最大访问次数（可选）
        /// </summary>
        public int? MaxAccessCount { get; set; }

        /// <summary>
        /// 最大下载次数（可选）
        /// </summary>
        public int? MaxDownloadCount { get; set; }

        /// <summary>
        /// 是否允许下载
        /// </summary>
        public bool AllowDownload { get; set; } = true;

        /// <summary>
        /// 是否允许预览
        /// </summary>
        public bool AllowPreview { get; set; } = true;

        /// <summary>
        /// 是否需要登录访问
        /// </summary>
        public bool RequireLogin { get; set; } = false;

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(500)]
        public string Remarks { get; set; }
    }

    /// <summary>
    /// 创建文件分享响应DTO
    /// </summary>
    public class CreateFileShareResponseDto
    {
        /// <summary>
        /// 分享ID
        /// </summary>
        public Guid ShareId { get; set; }

        /// <summary>
        /// 分享码
        /// </summary>
        public string ShareCode { get; set; }

        /// <summary>
        /// 分享URL
        /// </summary>
        public string ShareUrl { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 访问文件分享请求DTO
    /// </summary>
    public class AccessFileShareRequestDto
    {
        /// <summary>
        /// 分享码
        /// </summary>
        [Required]
        [StringLength(12)]
        public string ShareCode { get; set; }

        /// <summary>
        /// 访问密码（如果需要）
        /// </summary>
        [StringLength(50)]
        public string AccessPassword { get; set; }

        /// <summary>
        /// 访问IP地址
        /// </summary>
        [StringLength(45)]
        public string AccessIP { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        [StringLength(500)]
        public string UserAgent { get; set; }
    }

    /// <summary>
    /// 访问文件分享响应DTO
    /// </summary>
    public class AccessFileShareResponseDto
    {
        /// <summary>
        /// 是否可以访问
        /// </summary>
        public bool CanAccess { get; set; }

        /// <summary>
        /// 文件信息
        /// </summary>
        public FileInfoDto FileInfo { get; set; }

        /// <summary>
        /// 分享信息
        /// </summary>
        public FileShareDto ShareInfo { get; set; }

        /// <summary>
        /// 下载URL（如果允许下载）
        /// </summary>
        public string DownloadUrl { get; set; }

        /// <summary>
        /// 预览URL（如果允许预览）
        /// </summary>
        public string PreviewUrl { get; set; }

        /// <summary>
        /// 访问令牌
        /// </summary>
        public string AccessToken { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
    }
}
