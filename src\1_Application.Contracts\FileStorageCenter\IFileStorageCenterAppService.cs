﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using TSZ.ServiceBase.FileStorageCenter.FileStorageCenter;
using TSZ.Common.Core.Helper;
using Volo.Abp.Application.Services;
using TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto;

namespace TSZ.ServiceBase.FileStorageCenter
{
    /// <summary>
    /// 
    /// </summary>
    public interface IFileStorageCenterAppService : IApplicationService
    {
        #region 常规文件上传下载复制移动
        /// <summary>
        /// 上传文件，分割上传（也可以理解为分片上传，但是不需要合并，每次上传都是直接追加到文件的末尾，顺序一直上传，直到上传全部完成）
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名）</param>
        /// <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>    
        Task<ServiceResult<string>> UploadFile(string strFullFileName, int uploadMode, bool isEncryptDecrypt);

        /// <summary>
        /// 分片上传文件，不管分片数量多少，后续都必须调用FileMerge合并文件
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <param name="identifier">分片标识符</param>
        /// <param name="chunkNumber">分片号</param>
        /// <param name="chunkSize">分片大小</param>
        /// <param name="totalChunks">总分片数量，不管分片数量多少，后续都必须调用FileMerge合并文件</param>
        /// <param name="totalSize">文件总大小</param>
        /// <returns></returns>     
        Task<ServiceResult<string>> UploadFileChunk(string strFullFileName, bool isEncryptDecrypt, string identifier, int chunkNumber, long chunkSize, int totalChunks, long totalSize);

        /// <summary>
        /// 合并分片
        /// </summary>
        /// <param name="strFullFileName">完成服务器文件名</param>
        /// <param name="fileUploadBehaviour"></param>
        /// <param name="identifier"></param>
        /// <param name="totalChunks"></param>
        /// <returns></returns>      
        Task<ServiceResult<string>> FileMerge(string strFullFileName, FileUploadBehaviours fileUploadBehaviour, string identifier, int totalChunks);

        /// <summary>
        /// 下载文件，分割下载，一次只下载 FileServiceConfig.MAXFILESIZE（默认为1MB）大小，顺序一直下载，直到下载全部完成
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名）</param>
        /// <param name="intReadCount">文件读取的次数（第几次，从0开始计数）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>     
        Task<FileContentResult> DownloadFile(string strFullFileName, int intReadCount, bool isEncryptDecrypt);

        /// <summary>
        /// 下载文件，从指定节点
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名）</param>
        /// <param name="lPosition">读取文件的位置（文件从0开始的偏移量）</param>
        /// <param name="intReadSize">读取文件大小（一次读取的文件大小）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>      
        Task<FileContentResult> DownloadFilePosition(string strFullFileName, long lPosition, int intReadSize, bool isEncryptDecrypt);

        /// <summary>
        /// 覆盖文件
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名）</param>
        /// <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>       
        Task<ServiceResult<string>> OverrideFile(string strFullFileName, int uploadMode, bool isEncryptDecrypt);

        #endregion

        #region 文件分块上传

        /// <summary>
        /// 创建签名文件【分块】
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="intChunkSize">分块数量</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>       
        Task<ServiceResult<string>> CreateSignatureFile(string strFullFileName, short intChunkSize, bool isEncryptDecrypt = true);

        /// <summary>
        /// 创建Delta文件【分块】
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strSignatureFile">Delta完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>       
        Task<ServiceResult<string>> CreateDeltaFile(string strFullFileName, string strSignatureFile, bool isEncryptDecrypt = false);

        /// <summary>
        /// 从Delta文件创建新文件
        /// </summary>
        /// <param name="strReferenceServerFullFileName">参考的完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strDeltaFile">Delta完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>       
        Task<ServiceResult<string>> ApplyDeltaFileToCreateNewFile(string strReferenceServerFullFileName, string strFullFileName, string strDeltaFile, bool isEncryptDecrypt = true);

        #endregion

        #region  一次性上传下载API

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否是加密文件</param>
        /// <returns></returns>       
        Task<FileStreamResult> DownloadFileWhole(string strFullFileName, bool isEncryptDecrypt);

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="intReadCount">文件读取的次数（第几次，从0开始计数）</param>
        /// <param name="isEncryptDecrypt">是否是加密文件</param>
        /// <returns></returns>      
        Task<FileStreamResult> DownloadFileWholePart(string strFullFileName, int intReadCount, bool isEncryptDecrypt);

        /// <summary>
        /// 下载文件(发布目录下的模型等文件);（通用），在特定目录下（发布目录）去下载
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否是加密文件</param>
        /// <returns></returns>     
        Task<FileContentResult> DownloadModelFileWhole(string strFullFileName, bool isEncryptDecrypt);

        /// <summary>
        /// 上传文件（整个一起上传）
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否加密文件</param>
        /// <returns></returns>      
        Task<ServiceResult<string>> UploadFileWhole(string strFullFileName, bool isEncryptDecrypt);

        /// <summary>
        /// 上传文件，切割上传
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
        /// <param name="isEncryptDecrypt">是否加密文件</param>
        /// <returns></returns>     
        Task<ServiceResult<string>> UploadFileWholePart(string strFullFileName, int uploadMode, bool isEncryptDecrypt);

        #endregion

        #region 文件压测接口，不实际读写文件，避免磁盘爆炸和大量垃圾

        /// <summary>
        /// 上传文件（整个一起上传）,文件压测接口，不实际读写文件，避免磁盘爆炸和大量垃圾
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否加密文件</param>
        /// <returns></returns>      
        Task<ServiceResult<string>> UploadFileWholeEx(string strFullFileName, bool isEncryptDecrypt);

        /// <summary>
        /// 上传文件，切割上传，文件压测接口，不实际读写文件，避免磁盘爆炸和大量垃圾
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
        /// <param name="isEncryptDecrypt">是否加密文件</param>
        /// <returns></returns>     
        Task<ServiceResult<string>> UploadFileWholePartEx(string strFullFileName, int uploadMode, bool isEncryptDecrypt);
        #endregion

        #region  vue-simple-uploader 服务 https://www.cnblogs.com/xiahj/p/vue-simple-uploader.html
        /// <summary>
        /// GET请求，获取已经上传的分片信息（分片上传前返回已经上传的分片给前端）【vue前端专用】
        /// </summary>
        /// <returns></returns>
        Task<ServiceResult<FileResultDto>> UploadFileChunks();

        /// <summary>
        /// 文件分片上传，后续需要调用FileMergeChunks合并分片（除非totalChunks=1则直接处理为直接上传，不需要FileMergeChunks合并）【vue前端专用】
        /// 临时分片文件存储目录为：根目录+oneceGuid+identifier
        /// </summary>
        /// <param name="chunkNumber">分片编号</param>
        /// <param name="chunkSize">分片大小</param>
        /// <param name="totalSize">文件总大小</param>
        /// <param name="identifier">文件标识符，临时分片文件存储目录为：根目录+oneceGuid+identifier</param>      
        /// <param name="totalChunks">总分片数，大于1的总分片，后续需要调用FileMergeChunks合并分片，如果等于1，则变成直接上传</param>
        /// <param name="bucketName">模拟对象存储的bucketName，必填，可以为租户Id，可以是任意字符串，如tszobs、Guid等</param>
        /// <param name="objectName">模拟对象存储的objectName，可以是文件对象名称而且可以带子目录，如exampledir/exampleobject.txt</param>
        /// <returns></returns>       
        Task<ServiceResult<FileResultDto>> UploadFileChunks(
            [FromForm] int chunkNumber, [FromForm] long chunkSize, [FromForm] long totalSize,
            [FromForm] string identifier, [FromForm] int totalChunks, [FromForm] string bucketName, [FromForm] string objectName);

        /// <summary>
        /// 合并分片文件，完成上传（UploadFileChunks的后续方法）【vue前端专用】
        /// 临时分片文件存储目录为：根目录+oneceGuid+identifier
        /// </summary>
        /// <param name="identifier">文件标识符，临时分片文件存储目录为：根目录+oneceGuid+identifier</param>       
        /// <param name="totalChunks">总分片数量</param>
        /// <param name="bucketName">模拟对象存储的bucketName，必填，可以为租户Id，可以是任意字符串，如tszobs、Guid等</param>
        /// <param name="objectName">模拟对象存储的objectName，可以是文件对象名称而且可以带子目录，如exampledir/exampleobject.txt</param>
        /// <returns></returns>      
        Task<ServiceResult<FileResultDto>> FileMergeChunks(string identifier, int totalChunks, string bucketName, string objectName);

        #endregion

        #region 其它API

        /// <summary>
        /// 测试API，检查文件服务是否正常
        /// </summary>
        /// <returns></returns>      
        Task<ServiceResult<bool>> CanOpened();

        /// <summary>
        /// 获取服务器时间，返回标准时间
        /// </summary>
        /// <returns></returns>       
        Task<ServiceResult<DateTime>> GetServerDateTime();

        /// <summary>
        /// 判断文件是否存在
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>       
        Task<ServiceResult<bool>> ExistsFile(string strFullFileName);

        /// <summary>
        /// 获取文件大小
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>      
        Task<ServiceResult<long>> GetFileLength(string strFullFileName);

        /// <summary>
        /// 获取文件信息
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>       
        Task<ServiceResult<List<string>>> GetFileInfo(string strFullFileName, bool isEncryptDecrypt);

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>      
        Task<ServiceResult<string>> DeleteFile(string strFullFileName);

        /// <summary>
        /// 删除文件夹
        /// </summary>
        /// <param name="strDirectoryName"></param>
        /// <returns></returns>       
        Task<ServiceResult<bool>> DeleteDirectory(string strDirectoryName);

        /// <summary>
        /// 复制文件
        /// </summary>
        /// <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>      
        Task<ServiceResult<string>> CopyFile(string strSrcFullFileName, string strTargetFullFileName);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="strSrcFullFileName"></param>
        /// <param name="strTargetFullFileName"></param>
        /// <returns></returns>
        Task<ServiceResult<string>> CopyFileOverwrite(string strSrcFullFileName, string strTargetFullFileName);

        /// <summary>
        /// 解密文件
        /// </summary>
        /// <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>      
        Task<ServiceResult<string>> CopyFileEncryptToDecrypt(string strSrcFullFileName, string strTargetFullFileName);

        /// <summary>
        /// 加密文件
        /// </summary>
        /// <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>       
        Task<ServiceResult<string>> CopyFileDecryptToEncrypt(string strSrcFullFileName, string strTargetFullFileName);

        /// <summary>
        /// 获取文件MD5值
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="lFileLength">文件长度</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>       
        Task<ServiceResult<string>> ComputeFileMD5(string strFullFileName, long lFileLength, bool isEncryptDecrypt);

        /// <summary>
        /// 重命名文件
        /// </summary>
        /// <param name="strFullFileName">文件原始路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strNewFileName">文件新路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>      
        Task<ServiceResult<bool>> ReNameFile(string strFullFileName, string strNewFileName);

        /// <summary>
        /// 移动文件
        /// </summary>
        /// <param name="strSrcFullFileName">文件原始路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetFullFileName">文件目的路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>      
        Task<ServiceResult<bool>> MoveFile(string strSrcFullFileName, string strTargetFullFileName);

        #endregion

        #region  迁移文件添加的接口
        /// <summary>
        /// 获取文件夹文件
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>

        Task<ServiceResult<List<string>>> GetDirectoyChild(string path);

        /// <summary>
        /// 获取文件下面目录
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        Task<ServiceResult<List<string>>> GetDirectoryFiles(string path);


        /// <summary>
        /// 移动文件夹下面的文件
        /// </summary>
        /// <param name="sourcePath"></param>
        /// <param name="targetPath"></param>
        /// <returns></returns>
        Task<ServiceResult<bool>> MoveDirectoryFile(string sourcePath, string targetPath);

        #endregion
        /// <summary>
        /// 解压指定文件到指定目录
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatas，则API会自动加上，只能在 /tszappdatas 根目录下解压到指定的子目录</param>
        /// <param name="isEncryptDecrypt">原始文件是否是加密的</param>
        /// <returns></returns>       
        Task<ServiceResult<string>> UnzipFile(string strFullFileName, string strTargetDirectory, bool isEncryptDecrypt);

        /// <summary>
        /// 生成压缩包的方法(TSV5专用);-改为通用方法
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>       
        Task<ServiceResult<string>> GetZipFolderArchive(ZipFolderArchivesDto dto);

        /// <summary>
        /// 解压指定文件到指定目录并生成2d图纸信息json文件（BimBang专属接口，其它应用不能使用）
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下解压到指定的子目录</param>
        /// <param name="isEncryptDecrypt">原始文件是否是加密的</param>
        /// <param name="jsonFileName">生成json的文件名</param>
        /// <returns>true：有2d图纸，false：无2d图纸</returns>      
        Task<ServiceResult<bool>> UnzipFileWithJsonInfo(string strFullFileName, string strTargetDirectory, bool isEncryptDecrypt, string jsonFileName);

        /// <summary>
        /// 删除指定文件并删除模型发布服务的指定目录（通用）
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下解压到指定的子目录</param>
        /// <returns></returns>      
        Task<ServiceResult<bool>> DeleteFileAndFolder(string strFullFileName, string strTargetDirectory);

        /// <summary>
        /// 解压模型文件到模型web服务（通用）
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下解压到指定的子目录</param>
        /// <param name="isEncryptDecrypt">原始文件是否是加密的</param>       
        Task<ServiceResult<bool>> UnzipModelFile(string strFullFileName, string strTargetDirectory, bool isEncryptDecrypt);

        /// <summary>
        /// 复制模型文件到模型web服务（通用）
        /// </summary>
        /// <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下复制到指定的子目录</param>
        /// <returns></returns>
        Task<ServiceResult<string>> CopyModelFile(string strSrcFullFileName, string strTargetDirectory);

        #region Ftp文件服务
        /// <summary>
        /// 单个传文件
        /// </summary>
        /// <param name="clientFullName">客户端的全路径</param>
        /// <param name="serverFullName">服务端的全路径</param>
        /// <returns></returns>
        Task<ServiceResult> UpLoadFileWholeByFtp(string clientFullName, string serverFullName);


        /// <summary>
        /// 批量上传文件
        /// </summary>
        /// <param name="clientFullNames">上传的文件客户端全路径</param>
        /// <param name="serverDirec">上传的目录</param>
        /// <returns></returns>

        Task<ServiceResult> UpLoadFilesWholeByFtp(List<string> clientFullNames, string serverDirec);

        /// <summary>
        /// ftp下载文件
        /// </summary>
        /// <param name="clientFullName">客户端全路径</param>
        /// <param name="serverFullName">服务器全路径</param>
        /// <returns></returns>
        Task<ServiceResult> DownLoadFileByFtp(string clientFullName, string serverFullName);


        /// <summary>
        /// ftp创建目录
        /// </summary>
        /// <param name="drecName">目录名称</param>
        /// <returns></returns>
        Task<ServiceResult> CreateDirectoryByFtp(string drecName);
        #endregion

    }
}
