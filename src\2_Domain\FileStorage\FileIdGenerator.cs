using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件ID生成器实现
    /// 提供安全可靠的文件ID和分享码生成算法
    /// </summary>
    public class FileIdGenerator : IFileIdGenerator, ITransientDependency
    {
        private static readonly char[] ShareCodeChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".ToCharArray();
        private static readonly Random Random = new Random();
        private static readonly Regex FileIdRegex = new Regex("^[A-Fa-f0-9]{32}$", RegexOptions.Compiled);
        private static readonly Regex ShareCodeRegex = new Regex("^[A-Z0-9]{6,12}$", RegexOptions.Compiled);

        /// <summary>
        /// 生成文件ID
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="fileContent">文件内容</param>
        /// <param name="md5Hash">MD5哈希值</param>
        /// <returns></returns>
        public async Task<string> GenerateFileIdAsync(string filePath, byte[] fileContent = null, string md5Hash = null)
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            // 如果提供了MD5哈希值，直接使用
            if (!string.IsNullOrEmpty(md5Hash))
            {
                return md5Hash.ToUpperInvariant();
            }

            // 如果提供了文件内容，基于内容生成MD5
            if (fileContent != null && fileContent.Length > 0)
            {
                return GenerateMD5Hash(fileContent).ToUpperInvariant();
            }

            // 如果文件存在，读取文件内容生成MD5
            if (File.Exists(filePath))
            {
                return await GenerateMD5HashFromFileAsync(filePath);
            }

            // 如果文件不存在，基于路径和时间戳生成唯一ID
            var pathBytes = Encoding.UTF8.GetBytes(filePath + DateTime.UtcNow.Ticks);
            return GenerateMD5Hash(pathBytes).ToUpperInvariant();
        }

        /// <summary>
        /// 生成分享码
        /// </summary>
        /// <param name="length">长度</param>
        /// <returns></returns>
        public async Task<string> GenerateShareCodeAsync(int length = 8)
        {
            if (length < 6 || length > 12)
                throw new ArgumentException("Share code length must be between 6 and 12", nameof(length));

            await Task.CompletedTask;

            return GenerateSecureShareCode(length);
        }

        /// <summary>
        /// 生成高级分享码（带前缀和校验）
        /// </summary>
        /// <param name="prefix">前缀（可选）</param>
        /// <param name="length">长度</param>
        /// <param name="includeChecksum">是否包含校验码</param>
        /// <returns></returns>
        public async Task<string> GenerateAdvancedShareCodeAsync(string prefix = null, int length = 8, bool includeChecksum = false)
        {
            if (length < 6 || length > 12)
                throw new ArgumentException("Share code length must be between 6 and 12", nameof(length));

            await Task.CompletedTask;

            var actualLength = length;
            if (!string.IsNullOrEmpty(prefix))
            {
                actualLength -= prefix.Length;
                if (actualLength < 4)
                    throw new ArgumentException("Length too short for the given prefix", nameof(length));
            }

            if (includeChecksum)
            {
                actualLength -= 1; // 为校验码预留1位
                if (actualLength < 3)
                    throw new ArgumentException("Length too short for checksum", nameof(length));
            }

            var baseCode = GenerateSecureShareCode(actualLength);
            var result = new StringBuilder();

            if (!string.IsNullOrEmpty(prefix))
            {
                result.Append(prefix.ToUpperInvariant());
            }

            result.Append(baseCode);

            if (includeChecksum)
            {
                var checksum = CalculateChecksum(result.ToString());
                result.Append(checksum);
            }

            return result.ToString();
        }

        /// <summary>
        /// 生成基于时间戳的分享码（确保唯一性）
        /// </summary>
        /// <param name="length">长度</param>
        /// <returns></returns>
        public async Task<string> GenerateTimestampBasedShareCodeAsync(int length = 8)
        {
            if (length < 6 || length > 12)
                throw new ArgumentException("Share code length must be between 6 and 12", nameof(length));

            await Task.CompletedTask;

            // 使用时间戳确保唯一性
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var timestampBytes = BitConverter.GetBytes(timestamp);

            // 结合随机数增加安全性
            var randomBytes = new byte[4];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(randomBytes);
            }

            // 合并时间戳和随机数
            var combinedBytes = new byte[timestampBytes.Length + randomBytes.Length];
            Array.Copy(timestampBytes, 0, combinedBytes, 0, timestampBytes.Length);
            Array.Copy(randomBytes, 0, combinedBytes, timestampBytes.Length, randomBytes.Length);

            // 生成哈希并转换为分享码格式
            var hash = GenerateMD5Hash(combinedBytes);
            return ConvertHashToShareCode(hash, length);
        }

        /// <summary>
        /// 验证文件ID格式
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns></returns>
        public bool ValidateFileId(string fileId)
        {
            if (string.IsNullOrEmpty(fileId))
                return false;

            return FileIdRegex.IsMatch(fileId);
        }

        /// <summary>
        /// 验证分享码格式
        /// </summary>
        /// <param name="shareCode">分享码</param>
        /// <returns></returns>
        public bool ValidateShareCode(string shareCode)
        {
            if (string.IsNullOrEmpty(shareCode))
                return false;

            return ShareCodeRegex.IsMatch(shareCode);
        }

        /// <summary>
        /// 基于文件内容生成MD5哈希值
        /// </summary>
        /// <param name="fileContent">文件内容</param>
        /// <returns></returns>
        public string GenerateMD5Hash(byte[] fileContent)
        {
            if (fileContent == null || fileContent.Length == 0)
                throw new ArgumentException("File content cannot be null or empty", nameof(fileContent));

            using (var md5 = MD5.Create())
            {
                var hash = md5.ComputeHash(fileContent);
                return BitConverter.ToString(hash).Replace("-", "");
            }
        }

        /// <summary>
        /// 基于文件内容生成SHA256哈希值
        /// </summary>
        /// <param name="fileContent">文件内容</param>
        /// <returns></returns>
        public string GenerateSHA256Hash(byte[] fileContent)
        {
            if (fileContent == null || fileContent.Length == 0)
                throw new ArgumentException("File content cannot be null or empty", nameof(fileContent));

            using (var sha256 = SHA256.Create())
            {
                var hash = sha256.ComputeHash(fileContent);
                return BitConverter.ToString(hash).Replace("-", "");
            }
        }

        /// <summary>
        /// 基于文件路径生成MD5哈希值
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns></returns>
        public async Task<string> GenerateMD5HashFromFileAsync(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException($"File not found: {filePath}");

            using (var stream = File.OpenRead(filePath))
            using (var md5 = MD5.Create())
            {
                var hash = await Task.Run(() => md5.ComputeHash(stream));
                return BitConverter.ToString(hash).Replace("-", "");
            }
        }

        /// <summary>
        /// 基于文件路径生成SHA256哈希值
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns></returns>
        public async Task<string> GenerateSHA256HashFromFileAsync(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException($"File not found: {filePath}");

            using (var stream = File.OpenRead(filePath))
            using (var sha256 = SHA256.Create())
            {
                var hash = await Task.Run(() => sha256.ComputeHash(stream));
                return BitConverter.ToString(hash).Replace("-", "");
            }
        }

        #region 私有辅助方法 - REQ-005

        /// <summary>
        /// 生成安全的分享码
        /// </summary>
        /// <param name="length">长度</param>
        /// <returns>分享码</returns>
        private static string GenerateSecureShareCode(int length)
        {
            // 使用加密安全的随机数生成器
            using (var rng = RandomNumberGenerator.Create())
            {
                var bytes = new byte[length];
                rng.GetBytes(bytes);

                var result = new StringBuilder(length);
                for (int i = 0; i < length; i++)
                {
                    result.Append(ShareCodeChars[bytes[i] % ShareCodeChars.Length]);
                }

                return result.ToString();
            }
        }

        /// <summary>
        /// 计算校验码
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>校验码字符</returns>
        private static char CalculateChecksum(string input)
        {
            var sum = 0;
            foreach (char c in input)
            {
                sum += c;
            }
            return ShareCodeChars[sum % ShareCodeChars.Length];
        }

        /// <summary>
        /// 将哈希转换为分享码格式
        /// </summary>
        /// <param name="hash">哈希字符串</param>
        /// <param name="length">目标长度</param>
        /// <returns>分享码</returns>
        private static string ConvertHashToShareCode(string hash, int length)
        {
            var result = new StringBuilder(length);
            var hashBytes = Encoding.UTF8.GetBytes(hash);

            for (int i = 0; i < length; i++)
            {
                var index = hashBytes[i % hashBytes.Length] % ShareCodeChars.Length;
                result.Append(ShareCodeChars[index]);
            }

            return result.ToString();
        }



        #endregion
    }
}
