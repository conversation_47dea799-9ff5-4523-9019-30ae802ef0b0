using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore;
using TSZ.ServiceBase.FileStorageCenter.FileStorage;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件分享仓储EF Core实现
    /// </summary>
    public class EfCoreFileShareRepository : EfCoreRepository<FileStorageCenterDbContext, FileShare, Guid>, IFileShareRepository
    {
        public EfCoreFileShareRepository(IDbContextProvider<FileStorageCenterDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        /// <summary>
        /// 根据分享码查找分享信息
        /// </summary>
        public async Task<FileShare> FindByShareCodeAsync(string shareCode, bool includeDetails = true, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            if (includeDetails)
            {
                query = query.Include(x => x.FileInfo);
            }

            return await query.FirstOrDefaultAsync(x => x.ShareCode == shareCode, cancellationToken);
        }

        /// <summary>
        /// 根据文件信息ID获取分享列表
        /// </summary>
        public async Task<List<FileShare>> GetListByFileInfoIdAsync(Guid fileInfoId, int skipCount = 0, int maxResultCount = 10, string sorting = null, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            query = query.Where(x => x.FileInfoId == fileInfoId);
            
            if (includeDetails)
            {
                query = query.Include(x => x.FileInfo);
            }
            
            if (!string.IsNullOrEmpty(sorting))
            {
                query = query.OrderBy(sorting);
            }
            else
            {
                query = query.OrderByDescending(x => x.CreationTime);
            }

            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 根据分享状态获取分享列表
        /// </summary>
        public async Task<List<FileShare>> GetListByStatusAsync(ShareStatus status, int skipCount = 0, int maxResultCount = 10, string sorting = null, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            query = query.Where(x => x.Status == status);
            
            if (includeDetails)
            {
                query = query.Include(x => x.FileInfo);
            }
            
            if (!string.IsNullOrEmpty(sorting))
            {
                query = query.OrderBy(sorting);
            }
            else
            {
                query = query.OrderByDescending(x => x.CreationTime);
            }

            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 根据分享类型获取分享列表
        /// </summary>
        public async Task<List<FileShare>> GetListByShareTypeAsync(ShareType shareType, int skipCount = 0, int maxResultCount = 10, string sorting = null, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            query = query.Where(x => x.ShareType == shareType);
            
            if (includeDetails)
            {
                query = query.Include(x => x.FileInfo);
            }
            
            if (!string.IsNullOrEmpty(sorting))
            {
                query = query.OrderBy(sorting);
            }
            else
            {
                query = query.OrderByDescending(x => x.CreationTime);
            }

            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 获取过期的分享列表
        /// </summary>
        public async Task<List<FileShare>> GetExpiredSharesAsync(int skipCount = 0, int maxResultCount = 100, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            var now = DateTime.UtcNow;
            
            query = query.Where(x => x.ExpirationTime.HasValue && x.ExpirationTime.Value <= now);
            
            if (includeDetails)
            {
                query = query.Include(x => x.FileInfo);
            }
            
            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 获取访问次数超限的分享列表
        /// </summary>
        public async Task<List<FileShare>> GetAccessLimitExceededSharesAsync(int skipCount = 0, int maxResultCount = 100, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            query = query.Where(x => x.MaxAccessCount.HasValue && x.AccessCount >= x.MaxAccessCount.Value);
            
            if (includeDetails)
            {
                query = query.Include(x => x.FileInfo);
            }
            
            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 获取分享统计信息
        /// </summary>
        public async Task<ShareStatistics> GetStatisticsAsync(ShareType? shareType = null, ShareStatus? status = null, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            if (shareType.HasValue)
            {
                query = query.Where(x => x.ShareType == shareType.Value);
            }
            
            if (status.HasValue)
            {
                query = query.Where(x => x.Status == status.Value);
            }

            var statistics = new ShareStatistics
            {
                TotalCount = await query.CountAsync(cancellationToken),
                ActiveCount = await query.CountAsync(x => x.Status == ShareStatus.Active, cancellationToken),
                DisabledCount = await query.CountAsync(x => x.Status == ShareStatus.Disabled, cancellationToken),
                ExpiredCount = await query.CountAsync(x => x.Status == ShareStatus.Expired, cancellationToken),
                TotalAccessCount = await query.SumAsync(x => x.AccessCount, cancellationToken),
                TotalDownloadCount = await query.SumAsync(x => x.DownloadCount, cancellationToken)
            };

            if (statistics.TotalCount > 0)
            {
                statistics.AverageAccessCount = (double)statistics.TotalAccessCount / statistics.TotalCount;
                
                var mostPopular = await query.OrderByDescending(x => x.AccessCount).FirstOrDefaultAsync(cancellationToken);
                if (mostPopular != null)
                {
                    statistics.MostPopularShareId = mostPopular.Id;
                    statistics.MostPopularAccessCount = mostPopular.AccessCount;
                }
            }

            return statistics;
        }

        /// <summary>
        /// 批量更新分享状态
        /// </summary>
        public async Task<int> BatchUpdateStatusAsync(List<Guid> shareIds, ShareStatus status, CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            
            return await dbContext.Database.ExecuteSqlRawAsync(
                "UPDATE FileShares SET Status = {0}, LastModificationTime = {1} WHERE Id IN ({2})",
                status,
                DateTime.UtcNow,
                string.Join(",", shareIds.Select(id => $"'{id}'")),
                cancellationToken);
        }

        /// <summary>
        /// 批量删除过期分享
        /// </summary>
        public async Task<int> DeleteExpiredSharesAsync(CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var now = DateTime.UtcNow;
            
            return await dbContext.Database.ExecuteSqlRawAsync(
                "DELETE FROM FileShares WHERE ExpirationTime IS NOT NULL AND ExpirationTime <= {0}",
                now,
                cancellationToken);
        }

        /// <summary>
        /// 检查分享码是否存在
        /// </summary>
        public async Task<bool> ExistsByShareCodeAsync(string shareCode, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            return await query.AnyAsync(x => x.ShareCode == shareCode, cancellationToken);
        }

        /// <summary>
        /// 获取分享总数
        /// </summary>
        public async Task<long> GetCountAsync(ShareType? shareType = null, ShareStatus? status = null, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            if (shareType.HasValue)
            {
                query = query.Where(x => x.ShareType == shareType.Value);
            }
            
            if (status.HasValue)
            {
                query = query.Where(x => x.Status == status.Value);
            }

            return await query.CountAsync(cancellationToken);
        }

        /// <summary>
        /// 获取最近访问的分享列表
        /// </summary>
        public async Task<List<FileShare>> GetRecentlyAccessedSharesAsync(int days = 7, int skipCount = 0, int maxResultCount = 10, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            var cutoffDate = DateTime.UtcNow.AddDays(-days);
            
            query = query.Where(x => x.LastAccessTime.HasValue && x.LastAccessTime.Value >= cutoffDate);
            
            if (includeDetails)
            {
                query = query.Include(x => x.FileInfo);
            }
            
            query = query.OrderByDescending(x => x.LastAccessTime);

            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 获取热门分享列表（按访问次数排序）
        /// </summary>
        public async Task<List<FileShare>> GetPopularSharesAsync(int skipCount = 0, int maxResultCount = 10, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            if (includeDetails)
            {
                query = query.Include(x => x.FileInfo);
            }
            
            query = query.OrderByDescending(x => x.AccessCount);

            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 根据创建者获取分享列表
        /// </summary>
        public async Task<List<FileShare>> GetListByCreatorAsync(Guid creatorId, int skipCount = 0, int maxResultCount = 10, string sorting = null, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            query = query.Where(x => x.CreatorId == creatorId);
            
            if (includeDetails)
            {
                query = query.Include(x => x.FileInfo);
            }
            
            if (!string.IsNullOrEmpty(sorting))
            {
                query = query.OrderBy(sorting);
            }
            else
            {
                query = query.OrderByDescending(x => x.CreationTime);
            }

            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 搜索分享（根据标题和描述）
        /// </summary>
        public async Task<List<FileShare>> SearchAsync(string keyword, int skipCount = 0, int maxResultCount = 10, string sorting = null, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            if (!string.IsNullOrEmpty(keyword))
            {
                query = query.Where(x => x.Title.Contains(keyword) || x.Description.Contains(keyword));
            }
            
            if (includeDetails)
            {
                query = query.Include(x => x.FileInfo);
            }
            
            if (!string.IsNullOrEmpty(sorting))
            {
                query = query.OrderBy(sorting);
            }
            else
            {
                query = query.OrderByDescending(x => x.CreationTime);
            }

            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }
    }
}
