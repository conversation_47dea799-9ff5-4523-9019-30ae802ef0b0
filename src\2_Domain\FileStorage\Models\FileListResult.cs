using System.Collections.Generic;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件列表结果
    /// </summary>
    public class FileListResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; } = true;

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        /// <summary>
        /// 文件列表
        /// </summary>
        public List<FileMetadata> Files { get; set; } = new List<FileMetadata>();

        /// <summary>
        /// 目录列表
        /// </summary>
        public List<string> Directories { get; set; } = new List<string>();

        /// <summary>
        /// 是否截断
        /// </summary>
        public bool IsTruncated { get; set; }

        /// <summary>
        /// 下一个继续标记
        /// </summary>
        public string NextContinuationToken { get; set; }

        /// <summary>
        /// 前缀
        /// </summary>
        public string Prefix { get; set; }

        /// <summary>
        /// 分隔符
        /// </summary>
        public string Delimiter { get; set; }

        /// <summary>
        /// 最大键数
        /// </summary>
        public int MaxKeys { get; set; }

        /// <summary>
        /// 键数量
        /// </summary>
        public int KeyCount { get; set; }

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 是否有更多数据
        /// </summary>
        public bool HasMore { get; set; }

        /// <summary>
        /// 内容类型
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// 创建成功的结果
        /// </summary>
        /// <param name="files">文件列表</param>
        /// <param name="totalCount">总数量</param>
        /// <returns>文件列表结果</returns>
        public static FileListResult Success(List<FileMetadata> files, int totalCount)
        {
            return new FileListResult
            {
                Success = true,
                Files = files ?? new List<FileMetadata>(),
                TotalCount = totalCount,
                KeyCount = files?.Count ?? 0
            };
        }

        /// <summary>
        /// 创建失败的结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>文件列表结果</returns>
        public static FileListResult Failure(string errorMessage)
        {
            return new FileListResult
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
    }
}
