﻿using TSZ.ServiceBase.FileStorageCenter.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;

namespace TSZ.ServiceBase.FileStorageCenter.Permissions
{
    /// <summary>
    /// 
    /// </summary>
    public class FileStorageCenterPermissionDefinitionProvider : PermissionDefinitionProvider
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        public override void Define(IPermissionDefinitionContext context)
        {
            var myGroup = context.AddGroup(FileStorageCenterPermissions.GroupName);

            //Define your own permissions here. Example:
            //myGroup.AddPermission(FileStorageCenterPermissions.MyPermission1, L("Permission:MyPermission1"));
        }

        private static LocalizableString L(string name)
        {
            return LocalizableString.Create<FileStorageCenterResource>(name);
        }
    }
}
