﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TSZ.Common.Core.Helper;
using Volo.Abp.Application.Services;

namespace TSZ.ServiceBase.FileStorageCenter
{
    /// <summary>
    /// 文件同步服务
    /// </summary>
    public interface IFileSynchronizerAppService : IApplicationService
    {
        /// <summary>
        /// 从相同架构文件服务同步下载文件
        /// </summary>
        /// <param name="strFullFileName"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        Task<ServiceResult<string>> DownloadAsync(string strFullFileName, string tenantId);
    }
}