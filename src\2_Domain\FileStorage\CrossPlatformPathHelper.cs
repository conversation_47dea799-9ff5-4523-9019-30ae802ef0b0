using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using Volo.Abp.DependencyInjection;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 跨平台路径处理工具实现
    /// 提供Linux和Windows混合部署环境下的路径标准化和转换功能
    /// </summary>
    public class CrossPlatformPathHelper : ICrossPlatformPathHelper, ISingletonDependency
    {
        private static readonly char[] InvalidFileNameChars = Path.GetInvalidFileNameChars();
        private static readonly char[] InvalidPathChars = Path.GetInvalidPathChars();
        private static readonly Regex WindowsDriveRegex = new Regex(@"^[A-Za-z]:", RegexOptions.Compiled);

        /// <summary>
        /// 标准化路径（统一使用正斜杠作为分隔符）
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>标准化后的路径</returns>
        public string NormalizePath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return string.Empty;

            // 替换所有反斜杠为正斜杠
            var normalized = path.Replace('\\', '/');

            // 移除重复的斜杠
            normalized = Regex.Replace(normalized, @"/+", "/");

            // 移除末尾的斜杠（除非是根路径）
            if (normalized.Length > 1 && normalized.EndsWith("/"))
            {
                normalized = normalized.TrimEnd('/');
            }

            return normalized;
        }

        /// <summary>
        /// 转换为平台特定路径
        /// </summary>
        /// <param name="standardPath">标准化路径</param>
        /// <returns>平台特定路径</returns>
        public string ToPlatformPath(string standardPath)
        {
            if (string.IsNullOrEmpty(standardPath))
                return string.Empty;

            if (IsWindows())
            {
                return ToWindowsPath(standardPath);
            }
            else
            {
                return ToUnixPath(standardPath);
            }
        }

        /// <summary>
        /// 转换为Unix风格路径（使用正斜杠）
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>Unix风格路径</returns>
        public string ToUnixPath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return string.Empty;

            return path.Replace('\\', '/');
        }

        /// <summary>
        /// 转换为Windows风格路径（使用反斜杠）
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>Windows风格路径</returns>
        public string ToWindowsPath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return string.Empty;

            return path.Replace('/', '\\');
        }

        /// <summary>
        /// 组合路径（使用标准化分隔符）
        /// </summary>
        /// <param name="paths">路径片段</param>
        /// <returns>组合后的标准化路径</returns>
        public string CombinePaths(params string[] paths)
        {
            if (paths == null || paths.Length == 0)
                return string.Empty;

            var validPaths = paths.Where(p => !string.IsNullOrEmpty(p)).ToArray();
            if (validPaths.Length == 0)
                return string.Empty;

            var combined = string.Join("/", validPaths.Select(p => p.Trim('/', '\\')));
            return NormalizePath(combined);
        }

        /// <summary>
        /// 获取相对路径
        /// </summary>
        /// <param name="basePath">基础路径</param>
        /// <param name="fullPath">完整路径</param>
        /// <returns>相对路径</returns>
        public string GetRelativePath(string basePath, string fullPath)
        {
            if (string.IsNullOrEmpty(basePath) || string.IsNullOrEmpty(fullPath))
                return fullPath;

            var normalizedBase = NormalizePath(basePath);
            var normalizedFull = NormalizePath(fullPath);

            if (!normalizedFull.StartsWith(normalizedBase, StringComparison.OrdinalIgnoreCase))
                return normalizedFull;

            var relativePath = normalizedFull.Substring(normalizedBase.Length);
            return relativePath.TrimStart('/');
        }

        /// <summary>
        /// 获取绝对路径
        /// </summary>
        /// <param name="basePath">基础路径</param>
        /// <param name="relativePath">相对路径</param>
        /// <returns>绝对路径</returns>
        public string GetAbsolutePath(string basePath, string relativePath)
        {
            if (string.IsNullOrEmpty(basePath))
                return NormalizePath(relativePath);

            if (string.IsNullOrEmpty(relativePath))
                return NormalizePath(basePath);

            if (IsAbsolutePath(relativePath))
                return NormalizePath(relativePath);

            return CombinePaths(basePath, relativePath);
        }

        /// <summary>
        /// 验证路径是否有效
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>是否有效</returns>
        public bool IsValidPath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return false;

            try
            {
                // 检查是否包含非法字符
                if (ContainsInvalidChars(path))
                    return false;

                // 尝试创建路径对象
                var fullPath = Path.GetFullPath(path);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查路径是否为绝对路径
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>是否为绝对路径</returns>
        public bool IsAbsolutePath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return false;

            // Unix绝对路径以/开头
            if (path.StartsWith("/"))
                return true;

            // Windows绝对路径以盘符开头（如C:）
            if (WindowsDriveRegex.IsMatch(path))
                return true;

            // UNC路径（如\\server\share）
            if (path.StartsWith("\\\\") || path.StartsWith("//"))
                return true;

            return false;
        }

        /// <summary>
        /// 检查路径是否为相对路径
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>是否为相对路径</returns>
        public bool IsRelativePath(string path)
        {
            return !IsAbsolutePath(path);
        }

        /// <summary>
        /// 获取文件名（不包含路径）
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>文件名</returns>
        public string GetFileName(string path)
        {
            if (string.IsNullOrEmpty(path))
                return string.Empty;

            var normalized = NormalizePath(path);
            var lastSlash = normalized.LastIndexOf('/');
            return lastSlash >= 0 ? normalized.Substring(lastSlash + 1) : normalized;
        }

        /// <summary>
        /// 获取文件名（不包含扩展名）
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>文件名（无扩展名）</returns>
        public string GetFileNameWithoutExtension(string path)
        {
            var fileName = GetFileName(path);
            var lastDot = fileName.LastIndexOf('.');
            return lastDot > 0 ? fileName.Substring(0, lastDot) : fileName;
        }

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>文件扩展名（包含点号）</returns>
        public string GetFileExtension(string path)
        {
            var fileName = GetFileName(path);
            var lastDot = fileName.LastIndexOf('.');
            return lastDot >= 0 ? fileName.Substring(lastDot) : string.Empty;
        }

        /// <summary>
        /// 获取目录路径
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>目录路径</returns>
        public string GetDirectoryPath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return string.Empty;

            var normalized = NormalizePath(path);
            var lastSlash = normalized.LastIndexOf('/');
            return lastSlash > 0 ? normalized.Substring(0, lastSlash) : "/";
        }

        /// <summary>
        /// 清理路径中的非法字符
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <param name="replacement">替换字符</param>
        /// <returns>清理后的路径</returns>
        public string CleanPath(string path, char replacement = '_')
        {
            if (string.IsNullOrEmpty(path))
                return string.Empty;

            var cleaned = path;
            foreach (var invalidChar in InvalidPathChars)
            {
                cleaned = cleaned.Replace(invalidChar, replacement);
            }

            return cleaned;
        }

        /// <summary>
        /// 生成安全的文件名
        /// </summary>
        /// <param name="fileName">原始文件名</param>
        /// <param name="replacement">替换字符</param>
        /// <returns>安全的文件名</returns>
        public string GenerateSafeFileName(string fileName, char replacement = '_')
        {
            if (string.IsNullOrEmpty(fileName))
                return string.Empty;

            var safe = fileName;
            foreach (var invalidChar in InvalidFileNameChars)
            {
                safe = safe.Replace(invalidChar, replacement);
            }

            // 移除前后空格和点号
            safe = safe.Trim(' ', '.');

            // 确保不为空
            if (string.IsNullOrEmpty(safe))
                safe = "file";

            return safe;
        }

        /// <summary>
        /// 检查路径是否包含非法字符
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>是否包含非法字符</returns>
        public bool ContainsInvalidChars(string path)
        {
            if (string.IsNullOrEmpty(path))
                return false;

            return path.IndexOfAny(InvalidPathChars) >= 0;
        }

        /// <summary>
        /// 获取路径深度（目录层级数）
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>路径深度</returns>
        public int GetPathDepth(string path)
        {
            if (string.IsNullOrEmpty(path))
                return 0;

            var normalized = NormalizePath(path);
            return normalized.Split('/', StringSplitOptions.RemoveEmptyEntries).Length;
        }

        /// <summary>
        /// 分割路径为片段
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>路径片段列表</returns>
        public List<string> SplitPath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return new List<string>();

            var normalized = NormalizePath(path);
            return normalized.Split('/', StringSplitOptions.RemoveEmptyEntries).ToList();
        }

        /// <summary>
        /// 检查路径是否在指定的基础路径下
        /// </summary>
        /// <param name="basePath">基础路径</param>
        /// <param name="targetPath">目标路径</param>
        /// <returns>是否在基础路径下</returns>
        public bool IsUnderBasePath(string basePath, string targetPath)
        {
            if (string.IsNullOrEmpty(basePath) || string.IsNullOrEmpty(targetPath))
                return false;

            var normalizedBase = NormalizePath(basePath);
            var normalizedTarget = NormalizePath(targetPath);

            return normalizedTarget.StartsWith(normalizedBase + "/", StringComparison.OrdinalIgnoreCase) ||
                   normalizedTarget.Equals(normalizedBase, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 确保路径以指定字符结尾
        /// </summary>
        /// <param name="path">路径</param>
        /// <param name="endChar">结尾字符</param>
        /// <returns>处理后的路径</returns>
        public string EnsureEndsWith(string path, char endChar = '/')
        {
            if (string.IsNullOrEmpty(path))
                return endChar.ToString();

            return path.EndsWith(endChar) ? path : path + endChar;
        }

        /// <summary>
        /// 确保路径不以指定字符结尾
        /// </summary>
        /// <param name="path">路径</param>
        /// <param name="endChar">结尾字符</param>
        /// <returns>处理后的路径</returns>
        public string EnsureNotEndsWith(string path, char endChar = '/')
        {
            if (string.IsNullOrEmpty(path))
                return string.Empty;

            return path.TrimEnd(endChar);
        }

        /// <summary>
        /// 获取当前操作系统的路径分隔符
        /// </summary>
        /// <returns>路径分隔符</returns>
        public char GetPlatformPathSeparator()
        {
            return Path.DirectorySeparatorChar;
        }

        /// <summary>
        /// 检查当前是否为Windows平台
        /// </summary>
        /// <returns>是否为Windows平台</returns>
        public bool IsWindows()
        {
            return RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
        }

        /// <summary>
        /// 检查当前是否为Unix平台（Linux/macOS）
        /// </summary>
        /// <returns>是否为Unix平台</returns>
        public bool IsUnix()
        {
            return RuntimeInformation.IsOSPlatform(OSPlatform.Linux) ||
                   RuntimeInformation.IsOSPlatform(OSPlatform.OSX);
        }
    }
}
