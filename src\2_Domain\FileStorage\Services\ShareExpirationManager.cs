using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage.Services
{
    /// <summary>
    /// 分享过期管理服务实现
    /// </summary>
    public class ShareExpirationManager : IShareExpirationManager, ITransientDependency
    {
        private readonly IFileShareRepository _fileShareRepository;
        private readonly ILogger<ShareExpirationManager> _logger;

        public ShareExpirationManager(
            IFileShareRepository fileShareRepository,
            ILogger<ShareExpirationManager> logger)
        {
            _fileShareRepository = fileShareRepository;
            _logger = logger;
        }

        /// <summary>
        /// 检查并处理过期的分享
        /// </summary>
        public async Task<ShareExpirationProcessResult> ProcessExpiredSharesAsync(int batchSize = 100, CancellationToken cancellationToken = default)
        {
            var result = new ShareExpirationProcessResult();
            result.AddLog("开始处理过期分享");

            try
            {
                // 1. 获取过期的分享
                var expiredShares = await _fileShareRepository.GetExpiredSharesAsync(
                    skipCount: 0, 
                    maxResultCount: batchSize, 
                    includeDetails: false, 
                    cancellationToken: cancellationToken);

                result.ScannedCount = expiredShares.Count;
                result.ExpiredCount = expiredShares.Count;
                result.AddLog($"找到 {expiredShares.Count} 个过期分享");

                // 2. 处理每个过期分享
                foreach (var share in expiredShares)
                {
                    try
                    {
                        await ProcessExpiredShareAsync(share, cancellationToken);
                        result.ProcessedCount++;
                        result.AddLog($"已处理过期分享: {share.ShareCode}");
                    }
                    catch (Exception ex)
                    {
                        result.FailedCount++;
                        result.AddLog($"处理过期分享失败: {share.ShareCode}, 错误: {ex.Message}");
                        _logger.LogError(ex, "处理过期分享失败: ShareId={ShareId}", share.Id);
                    }
                }

                // 3. 发送过期通知
                if (expiredShares.Any())
                {
                    await SendExpirationNotificationsAsync(expiredShares, ShareExpirationNotificationType.ExpiredNotification, cancellationToken);
                    result.NotificationsSent = expiredShares.Count;
                }

                result.Success = true;
                result.AddLog($"过期分享处理完成: 成功={result.ProcessedCount}, 失败={result.FailedCount}");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.AddLog($"处理过期分享时发生错误: {ex.Message}");
                _logger.LogError(ex, "处理过期分享时发生错误");
            }
            finally
            {
                result.Complete();
            }

            return result;
        }

        /// <summary>
        /// 获取即将过期的分享列表
        /// </summary>
        public async Task<List<FileShare>> GetExpiringSharesAsync(int warningDays = 7, int maxCount = 1000, CancellationToken cancellationToken = default)
        {
            try
            {
                var cutoffDate = DateTime.UtcNow.AddDays(warningDays);
                
                // 简化实现 - 实际项目中需要在仓储中实现专门的查询方法
                var allShares = await _fileShareRepository.GetListAsync(cancellationToken: cancellationToken);
                
                return allShares
                    .Where(s => s.Status == ShareStatus.Active && 
                               s.ExpirationTime.HasValue && 
                               s.ExpirationTime.Value <= cutoffDate &&
                               s.ExpirationTime.Value > DateTime.UtcNow)
                    .Take(maxCount)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取即将过期分享列表时发生错误");
                return new List<FileShare>();
            }
        }

        /// <summary>
        /// 发送过期通知
        /// </summary>
        public async Task SendExpirationNotificationsAsync(List<FileShare> shares, ShareExpirationNotificationType notificationType, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("发送过期通知: 类型={NotificationType}, 数量={Count}", notificationType, shares.Count);

                foreach (var share in shares)
                {
                    await SendNotificationForShareAsync(share, notificationType, cancellationToken);
                }

                _logger.LogInformation("过期通知发送完成: 数量={Count}", shares.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送过期通知时发生错误");
            }
        }

        /// <summary>
        /// 延长分享有效期
        /// </summary>
        public async Task<bool> ExtendShareExpirationAsync(Guid shareId, int extensionDays, CancellationToken cancellationToken = default)
        {
            try
            {
                var share = await _fileShareRepository.GetAsync(shareId, cancellationToken: cancellationToken);
                if (share == null)
                {
                    _logger.LogWarning("分享不存在: ShareId={ShareId}", shareId);
                    return false;
                }

                var originalExpiration = share.ExpirationTime;
                var baseDate = share.ExpirationTime ?? DateTime.UtcNow;
                share.ExpirationTime = baseDate.AddDays(extensionDays);

                await _fileShareRepository.UpdateAsync(share, cancellationToken: cancellationToken);

                _logger.LogInformation("分享有效期已延长: ShareId={ShareId}, 原过期时间={OriginalExpiration}, 新过期时间={NewExpiration}", 
                    shareId, originalExpiration, share.ExpirationTime);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "延长分享有效期时发生错误: ShareId={ShareId}", shareId);
                return false;
            }
        }

        /// <summary>
        /// 批量延长分享有效期
        /// </summary>
        public async Task<ShareExpirationExtensionResult> BatchExtendShareExpirationAsync(List<Guid> shareIds, int extensionDays, CancellationToken cancellationToken = default)
        {
            var result = new ShareExpirationExtensionResult();

            try
            {
                foreach (var shareId in shareIds)
                {
                    try
                    {
                        var success = await ExtendShareExpirationAsync(shareId, extensionDays, cancellationToken);
                        if (success)
                        {
                            result.SuccessCount++;
                        }
                        else
                        {
                            result.FailedCount++;
                            result.Failures.Add(new ShareExtensionFailure
                            {
                                ShareId = shareId,
                                Reason = "分享不存在或更新失败",
                                ErrorCode = "SHARE_NOT_FOUND_OR_UPDATE_FAILED"
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        result.FailedCount++;
                        result.Failures.Add(new ShareExtensionFailure
                        {
                            ShareId = shareId,
                            Reason = ex.Message,
                            ErrorCode = ex.GetType().Name
                        });
                    }
                }

                result.Success = result.FailedCount == 0;
                if (result.FailedCount > 0)
                {
                    result.ErrorMessage = $"批量延期部分失败: 成功={result.SuccessCount}, 失败={result.FailedCount}";
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "批量延长分享有效期时发生错误");
            }

            return result;
        }

        /// <summary>
        /// 设置分享自动延期规则
        /// </summary>
        public async Task<bool> SetAutoExtensionRuleAsync(Guid shareId, ShareAutoExtensionRule rule, CancellationToken cancellationToken = default)
        {
            try
            {
                // 简化实现 - 实际项目中需要将规则存储到数据库
                _logger.LogInformation("设置分享自动延期规则: ShareId={ShareId}, Enabled={IsEnabled}, Days={ExtensionDays}", 
                    shareId, rule.IsEnabled, rule.ExtensionDays);

                // TODO: 实现自动延期规则存储
                // 1. 创建或更新ShareAutoExtensionRule表
                // 2. 存储规则配置
                // 3. 设置定时任务检查规则

                await Task.CompletedTask;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置分享自动延期规则时发生错误: ShareId={ShareId}", shareId);
                return false;
            }
        }

        /// <summary>
        /// 获取分享过期统计信息
        /// </summary>
        public async Task<ShareExpirationStatistics> GetExpirationStatisticsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var statistics = new ShareExpirationStatistics();

                // 简化实现 - 实际项目中需要优化查询性能
                var allShares = await _fileShareRepository.GetListAsync(cancellationToken: cancellationToken);

                statistics.TotalShares = allShares.Count;
                statistics.ActiveShares = allShares.Count(s => s.Status == ShareStatus.Active);
                statistics.ExpiredShares = allShares.Count(s => s.IsExpired());
                statistics.NeverExpireShares = allShares.Count(s => !s.ExpirationTime.HasValue);

                var expiringDate = DateTime.UtcNow.AddDays(7);
                statistics.ExpiringShares = allShares.Count(s => 
                    s.Status == ShareStatus.Active && 
                    s.ExpirationTime.HasValue && 
                    s.ExpirationTime.Value <= expiringDate &&
                    s.ExpirationTime.Value > DateTime.UtcNow);

                // 计算平均剩余天数
                var sharesWithExpiration = allShares.Where(s => s.ExpirationTime.HasValue && s.ExpirationTime.Value > DateTime.UtcNow).ToList();
                if (sharesWithExpiration.Any())
                {
                    statistics.AverageRemainingDays = sharesWithExpiration
                        .Average(s => (s.ExpirationTime.Value - DateTime.UtcNow).TotalDays);
                }

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分享过期统计信息时发生错误");
                return new ShareExpirationStatistics();
            }
        }

        #region 私有方法

        /// <summary>
        /// 处理单个过期分享
        /// </summary>
        private async Task ProcessExpiredShareAsync(FileShare share, CancellationToken cancellationToken)
        {
            // 更新分享状态为过期
            share.Status = ShareStatus.Expired;
            await _fileShareRepository.UpdateAsync(share, cancellationToken: cancellationToken);

            _logger.LogInformation("分享已标记为过期: ShareId={ShareId}, ShareCode={ShareCode}", share.Id, share.ShareCode);
        }

        /// <summary>
        /// 为单个分享发送通知
        /// </summary>
        private async Task SendNotificationForShareAsync(FileShare share, ShareExpirationNotificationType notificationType, CancellationToken cancellationToken)
        {
            // 简化实现 - 实际项目中需要集成通知服务
            await Task.CompletedTask;

            _logger.LogInformation("发送分享通知: ShareId={ShareId}, Type={NotificationType}", share.Id, notificationType);

            // TODO: 实现通知发送
            // 1. 根据通知类型生成通知内容
            // 2. 获取分享创建者信息
            // 3. 发送邮件/短信/站内信通知
        }

        #endregion
    }
}
