﻿@using Swashbuckle.AspNetCore.Swagger;
<!DOCTYPE html>
<html>
<head>
    <title>Swagger API文档代码文件</title>
    <style type='text/css'>

        table, table td, table th {
            border: 1px solid #000000;
            border-collapse: collapse;
        }

        table {
            table-layout: fixed;
            word-break: break-all;
        }

        tr {
            height: 20px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div style='width:1000px; margin: 0 auto'>
        <span><i>Word接口文档</i></span>
        <h1 align="center">@Model.Info.Title</h1>
        <h1 align="center">接口文档 @Model.Info.Version</h1>
        <br>
        @*<a href="@Model.Info.Contact.Url" target="_blank" rel="noopener noreferrer" class="link">@Model.Info.Contact.Name - Website</a>*@
        <br>
        <h3>接口描述</h3>
        <span>@Model.Info.Description</span>
        <br>
        <table border='1' cellspacing='0' cellpadding='0' style="table-layout: fixed; word-break: break-all;border: 1px solid #000000;border-collapse: collapse;" width='100%'>
            @foreach (var item in Model.Paths)
            {
                if (item.Value.Operations != null)
                {
                    foreach (var operation in item.Value.Operations)
                    {
                        if (item.Key.Contains("apis"))
                        {
                            <h3>@operation.Value.Summary</h3>
                            <table class="tsz_table" style="width:100%;">
                                <tr class="tr_bg1" style="background-color: #62c1e4; line-height:30px">
                                    <td colspan='5' style=" line-height:45px;padding:0 10px;border:1px solid #000;font-size:15px;font-weight:500;">@operation.Value.Summary</td>
                                </tr>

                                <tr>
                                    <td style=" line-height:30px;padding:0 10px;border:1px solid #000">URL</td>
                                    <td colspan='4' style=" line-height:30px;padding:0 10px;border:1px solid #000">@item.Key</td>
                                </tr>
                                <tr style=" line-height:30px;padding:0 10px;border:1px solid #000">
                                    <td style=" line-height:30px;padding:0 10px;border:1px solid #000">请求方式</td>
                                    <td colspan='4' style=" line-height:30px;padding:0 10px;border:1px solid #000">
                                        @operation.Key
                                    </td>
                                </tr>

                                @if (operation.Value.Parameters != null && operation.Value.Parameters.Count > 0)
                                {
                                    <tr class="tr_bg2" style="background-color:#c2efff" align='center'>
                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000">参数名</td>
                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000">参数类型</td>
                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000">是否必填</td>
                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='2'>说明</td>
                                    </tr>
                                    @foreach (var param in operation.Value.Parameters)
                                    {
                                        <tr align='center'>
                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@param.Name</td>
                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@param.In</td>
                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@param.Required</td>
                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px;" colspan='2'>@param.Description</td>
                                        </tr>
                                    }
                                }

                                @if (operation.Value.RequestBody != null && operation.Value.RequestBody.Content != null && operation.Value.RequestBody.Content.Count > 0)
                                {
                                    @foreach (var content in operation.Value.RequestBody.Content)
                                    {
                                        if (content.Key == "application/json")
                                        {
                                            if (content.Value.Schema != null && content.Value.Schema.Reference != null && content.Value.Schema.Reference.Id != null)
                                            {
                                                @foreach (var dto in Model.Components.Schemas)
                                                {
                                                    @if (dto.Key == content.Value.Schema.Reference.Id)
                                                    {
                                                        <tr class="tr_bg4" style="background-color:#deffe4" align='center'>
                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000" colspan='2'>Post提交参数</td>
                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000">参数类型</td>
                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='2'>说明</td>
                                                        </tr>
                                                        <tr align='center'>
                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000" colspan='2'>@dto.Key</td>
                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@dto.Value.Type</td>
                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='2'>@dto.Value.Description</td>
                                                        </tr>
                                                        if (dto.Value.Properties != null)
                                                        {
                                                            <tr class="tr_bg3" style="background-color:#c4fdcf" align='center'>
                                                                <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;">参数字段</td>
                                                                <td style=" line-height:30px;padding:0 10px;border:1px solid #000">参数类型</td>
                                                                <td style=" line-height:30px;padding:0 10px;border:1px solid #000">最大长度</td>
                                                                <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='2'>说明</td>
                                                            </tr>
                                                            @foreach (var column in dto.Value.Properties)
                                                            {
                                                                <tr align='center'>
                                                                    <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;">0 ：@column.Key</td>
                                                                    <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@column.Value.Type</td>
                                                                    <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@column.Value.MaxLength</td>
                                                                    <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='2'>@column.Value.Description</td>
                                                                </tr>
                                                                @if (column.Value.Items != null && column.Value.Items.Reference != null)
                                                                {
                                                                    @foreach (var childDto in Model.Components.Schemas)
                                                                    {
                                                                        @if (childDto.Key == column.Value.Items.Reference.Id)
                                                                        {
                                                                            if (childDto.Value.Properties != null)
                                                                            {
                                                                                <tr class="tr_bg5" style="background-color:#e6ffeb" align='center'>
                                                                                    <td style=" line-height:30px;padding:0 20px;border:1px solid #000;text-align:left;">1 @column.Key 字段</td>
                                                                                    <td style=" line-height:30px;padding:0 10px;border:1px solid #000">参数类型</td>
                                                                                    @*<td style=" line-height:30px;padding:0 10px;border:1px solid #000">最大长度</td>*@
                                                                                    <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='3'>说明</td>
                                                                                </tr>
                                                                                @foreach (var childColumn in childDto.Value.Properties)
                                                                                {
                                                                                    <tr align='center'>
                                                                                        <td style=" line-height:30px;padding:0 20px;border:1px solid #000;text-align:left;">1 ：@childColumn.Key</td>
                                                                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@childColumn.Value.Type</td>
                                                                                        @*<td style=" line-height:30px;padding:0 10px;border:1px solid #000">@childColumn.Value.MaxLength</td>*@
                                                                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='3'>@childColumn.Value.Description</td>
                                                                                    </tr>
                                                                                    @if (childColumn.Value.Items != null && childColumn.Value.Items.Reference != null)
                                                                                    {
                                                                                        @foreach (var child2Dto in Model.Components.Schemas)
                                                                                        {
                                                                                            @if (child2Dto.Key == childColumn.Value.Items.Reference.Id)
                                                                                            {
                                                                                                if (child2Dto.Value.Properties != null)
                                                                                                {
                                                                                                    <tr style="background-color: #e6ffeb" align='center'>
                                                                                                        <td style=" line-height:30px;padding:0 30px;border:1px solid #000;text-align:left;">2 @childColumn.Key 字段</td>
                                                                                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000">参数类型</td>
                                                                                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='3'>说明</td>
                                                                                                    </tr>
                                                                                                    @foreach (var child2Column in child2Dto.Value.Properties)
                                                                                                    {
                                                                                                        <tr align='center' style="border: 1px solid #4cff00;border-collapse: collapse;">

                                                                                                            <td style=" line-height:30px;padding:0 30px;border:1px solid #000;text-align:left;">2 ：@child2Column.Key</td>
                                                                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@child2Column.Value.Type</td>
                                                                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='3'>@child2Column.Value.Description</td>
                                                                                                        </tr>
                                                                                                        @if (child2Column.Value.Items != null && child2Column.Value.Items.Reference != null)
                                                                                                        {
                                                                                                            @foreach (var child3Dto in Model.Components.Schemas)
                                                                                                            {
                                                                                                                @if (child3Dto.Key == child2Column.Value.Items.Reference.Id)
                                                                                                                {
                                                                                                                    if (child3Dto.Value.Properties != null)
                                                                                                                    {
                                                                                                                        <tr style="background-color: #e6ffeb" align='center'>
                                                                                                                            <td style=" line-height:30px;padding:0 40px;border:1px solid #000;text-align:left;">3 @child2Column.Key 字段</td>
                                                                                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000">参数类型</td>
                                                                                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='3'>说明</td>
                                                                                                                        </tr>
                                                                                                                        @foreach (var child3Column in child2Dto.Value.Properties)
                                                                                                                        {
                                                                                                                            <tr align='center' style="border: 1px solid #4cff00;border-collapse: collapse;">

                                                                                                                                <td style=" line-height:30px;padding:0 40px;border:1px solid #000;text-align:left;">3 ：@child3Column.Key</td>
                                                                                                                                <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@child3Column.Value.Type</td>
                                                                                                                                <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='3'>@child3Column.Value.Description</td>
                                                                                                                            </tr>
                                                                                                                        }
                                                                                                                    }
                                                                                                                }
                                                                                                            }
                                                                                                        }
                                                                                                    }
                                                                                                }
                                                                                            }
                                                                                        }
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                @if (operation.Value.Responses != null && operation.Value.Responses.Count > 0)
                                {
                                    @foreach (var response in operation.Value.Responses)
                                    {
                                        if (response.Key == "200")
                                        {
                                            if (response.Value != null && response.Value.Content != null && response.Value.Content.Count > 0)
                                            {
                                                @foreach (var content in response.Value.Content)
                                                {
                                                    if (content.Key == "application/json")
                                                    {
                                                        if (content.Value.Schema != null && content.Value.Schema.Reference != null && content.Value.Schema.Reference.Id != null)
                                                        {
                                                            @foreach (var dto in Model.Components.Schemas)
                                                            {
                                                                @if (dto.Key == content.Value.Schema.Reference.Id)
                                                                {
                                                                    <tr style="background-color: #c7f32f;" align='center'>
                                                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000" colspan='2'>返回值</td>
                                                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000">类型</td>
                                                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='2'>说明</td>
                                                                    </tr>
                                                                    <tr align='center' style=" line-height:30px;padding:0 10px;border:1px solid #000">
                                                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000" colspan='2'>@dto.Key</td>
                                                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@dto.Value.Type</td>
                                                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='2'>@dto.Value.Description</td>
                                                                    </tr>
                                                                    if (dto.Value.Properties != null)
                                                                    {
                                                                        <tr style="        background-color: #c7f32f
" align='center'>
                                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000" colspan='2'>返回值字段</td>
                                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000">参数类型</td>
                                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='2'>说明</td>
                                                                        </tr>
                                                                        @foreach (var column in dto.Value.Properties)
                                                                        {
                                                                            <tr align='center' style=" line-height:30px;padding:0 10px;border:1px solid #000">
                                                                                <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;" colspan='2'>0 ：@column.Key</td>
                                                                                <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@column.Value.Type</td>
                                                                                <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='2'>@column.Value.Description</td>
                                                                            </tr>
                                                                            @if (column.Value.Items != null && column.Value.Items.Reference != null)
                                                                            {
                                                                                @foreach (var childDto in Model.Components.Schemas)
                                                                                {
                                                                                    @if (childDto.Key == column.Value.Items.Reference.Id)
                                                                                    {
                                                                                        if (childDto.Value.Properties != null)
                                                                                        {
                                                                                            <tr style="background-color: #c1de5d" align='center'>
                                                                                                <td style=" line-height:30px;padding:0 20px;border:1px solid #000;text-align:left;">1 @column.Key 字段</td>
                                                                                                <td style=" line-height:30px;padding:0 10px;border:1px solid #000">参数类型</td>
                                                                                                @*<td style=" line-height:30px;padding:0 10px;border:1px solid #000">最大长度</td>*@
                                                                                                <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='3'>说明</td>
                                                                                            </tr>
                                                                                            @foreach (var childColumn in childDto.Value.Properties)
                                                                                            {
                                                                                                <tr align='center' style="border: 1px solid #4cff00;border-collapse: collapse;">

                                                                                                    <td style=" line-height:30px;padding:0 20px;border:1px solid #000;text-align:left;">1 ：@childColumn.Key</td>
                                                                                                    <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@childColumn.Value.Type</td>
                                                                                                    @*<td style=" line-height:30px;padding:0 10px;border:1px solid #000">@childColumn.Value.MaxLength</td>*@
                                                                                                    <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='3'>@childColumn.Value.Description</td>
                                                                                                </tr>
                                                                                                @if (childColumn.Value.Items != null && childColumn.Value.Items.Reference != null)
                                                                                                {
                                                                                                    @foreach (var child2Dto in Model.Components.Schemas)
                                                                                                    {
                                                                                                        @if (child2Dto.Key == childColumn.Value.Items.Reference.Id)
                                                                                                        {
                                                                                                            if (child2Dto.Value.Properties != null)
                                                                                                            {
                                                                                                                <tr style="background-color: #b9d45b" align='center'>
                                                                                                                    <td style=" line-height:30px;padding:0 30px;border:1px solid #000;text-align:left;">2 @childColumn.Key 字段</td>
                                                                                                                    <td style=" line-height:30px;padding:0 20px;border:1px solid #000">参数类型</td>
                                                                                                                    <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='3'>说明</td>
                                                                                                                </tr>
                                                                                                                @foreach (var child2Column in child2Dto.Value.Properties)
                                                                                                                {
                                                                                                                    <tr align='center' style="border: 1px solid #4cff00;border-collapse: collapse;">

                                                                                                                        <td style=" line-height:30px;padding:0 30px;border:1px solid #000;text-align:left;">2 ：@child2Column.Key</td>
                                                                                                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@child2Column.Value.Type</td>
                                                                                                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='3'>@child2Column.Value.Description</td>
                                                                                                                    </tr>
                                                                                                                    @if (child2Column.Value.Items != null && child2Column.Value.Items.Reference != null)
                                                                                                                    {
                                                                                                                        @foreach (var child3Dto in Model.Components.Schemas)
                                                                                                                        {
                                                                                                                            @if (child3Dto.Key == child2Column.Value.Items.Reference.Id)
                                                                                                                            {
                                                                                                                                if (child3Dto.Value.Properties != null)
                                                                                                                                {
                                                                                                                                    <tr style="background-color: #aac256" align='center'>
                                                                                                                                        <td style=" line-height:30px;padding:0 40px;border:1px solid #000;text-align:left;">3 @child2Column.Key 字段</td>
                                                                                                                                        <td style=" line-height:30px;padding:0 30px;border:1px solid #000">参数类型</td>
                                                                                                                                        <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='3'>说明</td>
                                                                                                                                    </tr>
                                                                                                                                    @foreach (var child3Column in child2Dto.Value.Properties)
                                                                                                                                    {
                                                                                                                                        <tr align='center' style="border: 1px solid #4cff00;border-collapse: collapse;">

                                                                                                                                            <td style=" line-height:30px;padding:0 40px;border:1px solid #000;text-align:left;">3 ：@child3Column.Key</td>
                                                                                                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000">@child3Column.Value.Type</td>
                                                                                                                                            <td style=" line-height:30px;padding:0 10px;border:1px solid #000;text-align:left;word-break:break-all;word-wrap:break-word;letter-spacing:2px" colspan='3'>@child3Column.Value.Description</td>
                                                                                                                                        </tr>
                                                                                                                                    }
                                                                                                                                }
                                                                                                                            }
                                                                                                                        }
                                                                                                                    }
                                                                                                                }
                                                                                                            }
                                                                                                        }
                                                                                                    }
                                                                                                }
                                                                                            }
                                                                                        }
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }

                                                }

                                            }
                                        }
                                    }
                                }
                                <br>
                            </table>
                        }
                    }

                }

            }
        </table>
    </div>
</body>
</html>
