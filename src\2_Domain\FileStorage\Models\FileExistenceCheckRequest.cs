namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件存在性检查请求
    /// 用于检查文件是否已存在，支持文件快传功能
    /// </summary>
    public class FileExistenceCheckRequest
    {
        /// <summary>
        /// 文件MD5哈希值
        /// </summary>
        public string MD5Hash { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件名（可选，用于更精确的匹配）
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// MIME类型（可选）
        /// </summary>
        public string MimeType { get; set; }

        /// <summary>
        /// 是否只检查活跃状态的文件
        /// </summary>
        public bool OnlyActiveFiles { get; set; } = true;

        /// <summary>
        /// 是否包含详细信息
        /// </summary>
        public bool IncludeDetails { get; set; } = false;
    }
}
