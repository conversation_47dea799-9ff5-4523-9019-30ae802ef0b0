namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// S3存储配置
    /// </summary>
    public class S3StorageConfiguration : StorageConfiguration
    {
        /// <summary>
        /// 访问密钥ID
        /// </summary>
        public string AccessKeyId { get; set; }

        /// <summary>
        /// 访问密钥
        /// </summary>
        public string SecretAccessKey { get; set; }

        /// <summary>
        /// 区域
        /// </summary>
        public string Region { get; set; }

        /// <summary>
        /// 服务端点
        /// </summary>
        public string ServiceUrl { get; set; }

        /// <summary>
        /// 存储桶名称
        /// </summary>
        public string BucketName { get; set; }

        /// <summary>
        /// 是否使用HTTPS
        /// </summary>
        public bool UseHttps { get; set; } = true;

        /// <summary>
        /// 是否强制路径样式
        /// </summary>
        public bool ForcePathStyle { get; set; }

        /// <summary>
        /// 会话令牌
        /// </summary>
        public string SessionToken { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public S3StorageConfiguration()
        {
            StorageType = StorageType.S3;
        }
    }
}
