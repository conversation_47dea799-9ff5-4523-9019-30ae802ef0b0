using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TSZ.ServiceBase.FileStorageCenter.FileStorage;
using Volo.Abp.DependencyInjection;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage.Providers
{
    /// <summary>
    /// 文件存储提供者基类
    /// 提供通用的存储操作实现和模板方法
    /// </summary>
    public abstract class BaseFileStorageProvider : IFileStorageProvider, ITransientDependency
    {
        protected readonly ILogger Logger;

        protected BaseFileStorageProvider(ILogger logger)
        {
            Logger = logger;
        }

        /// <summary>
        /// 存储类型
        /// </summary>
        public abstract StorageType StorageType { get; }

        /// <summary>
        /// 存储配置名称
        /// </summary>
        public abstract string ConfigurationName { get; }

        /// <summary>
        /// 是否支持分片上传
        /// </summary>
        public virtual bool SupportsChunkedUpload => true;

        /// <summary>
        /// 是否支持断点续传
        /// </summary>
        public virtual bool SupportsResumableUpload => true;

        /// <summary>
        /// 是否支持预签名URL
        /// </summary>
        public virtual bool SupportsPresignedUrl => false;

        /// <summary>
        /// 上传文件
        /// </summary>
        public virtual async Task<FileUploadResult> UploadFileAsync(FileUploadRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始上传文件: {FilePath}, 大小: {FileSize}", request.FilePath, request.FileSize);

                var result = await DoUploadFileAsync(request, cancellationToken);

                Logger.LogInformation("文件上传完成: {FilePath}, 结果: {Success}", request.FilePath, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "文件上传失败: {FilePath}", request.FilePath);
                return new FileUploadResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    FilePath = request.FilePath
                };
            }
        }

        /// <summary>
        /// 分片上传文件
        /// </summary>
        public virtual async Task<ChunkedUploadResult> UploadChunkAsync(ChunkedUploadRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogDebug("开始上传分片: {FilePath}, 分片号: {ChunkNumber}", request.FilePath, request.ChunkNumber);

                var result = await DoUploadChunkAsync(request, cancellationToken);

                Logger.LogDebug("分片上传完成: {FilePath}, 分片号: {ChunkNumber}, 结果: {Success}", 
                    request.FilePath, request.ChunkNumber, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "分片上传失败: {FilePath}, 分片号: {ChunkNumber}", request.FilePath, request.ChunkNumber);
                return new ChunkedUploadResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    UploadId = request.UploadId,
                    ChunkNumber = request.ChunkNumber
                };
            }
        }

        /// <summary>
        /// 完成分片上传
        /// </summary>
        public virtual async Task<FileUploadResult> CompleteChunkedUploadAsync(CompleteChunkedUploadRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始完成分片上传: {FilePath}, 上传ID: {UploadId}, 分片数: {ChunkCount}", 
                    request.FilePath, request.UploadId, request.Chunks.Count);

                var result = await DoCompleteChunkedUploadAsync(request, cancellationToken);

                Logger.LogInformation("分片上传完成: {FilePath}, 结果: {Success}", request.FilePath, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "完成分片上传失败: {FilePath}, 上传ID: {UploadId}", request.FilePath, request.UploadId);
                return new FileUploadResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    FilePath = request.FilePath
                };
            }
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        public virtual async Task<FileDownloadResult> DownloadFileAsync(FileDownloadRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始下载文件: {FilePath}", request.FilePath);

                var result = await DoDownloadFileAsync(request, cancellationToken);

                Logger.LogInformation("文件下载完成: {FilePath}", request.FilePath);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "文件下载失败: {FilePath}", request.FilePath);
                return new FileDownloadResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    FilePath = request.FilePath
                };
            }
        }

        /// <summary>
        /// 分片下载文件
        /// </summary>
        public virtual async Task<Stream> DownloadFileRangeAsync(FileRangeDownloadRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始分片下载文件: {FilePath}, 范围: {RangeStart}-{RangeEnd}", 
                    request.FilePath, request.RangeStart, request.RangeEnd);

                var stream = await DoDownloadFileRangeAsync(request, cancellationToken);

                Logger.LogInformation("分片下载完成: {FilePath}", request.FilePath);

                return stream;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "分片下载失败: {FilePath}", request.FilePath);
                throw;
            }
        }

        /// <summary>
        /// 删除文件
        /// </summary>
        public virtual async Task<FileDeleteResult> DeleteFileAsync(FileDeleteRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始删除文件: {FilePath}", request.FilePath);

                var result = await DoDeleteFileAsync(request, cancellationToken);

                Logger.LogInformation("文件删除完成: {FilePath}, 结果: {Success}", request.FilePath, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "文件删除失败: {FilePath}", request.FilePath);
                return new FileDeleteResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    FilePath = request.FilePath
                };
            }
        }

        /// <summary>
        /// 批量删除文件
        /// </summary>
        public virtual async Task<BatchDeleteResult> BatchDeleteFilesAsync(BatchDeleteRequest request, CancellationToken cancellationToken = default)
        {
            var result = new BatchDeleteResult();

            foreach (var filePath in request.FilePaths)
            {
                try
                {
                    var deleteResult = await DeleteFileAsync(new FileDeleteRequest
                    {
                        FilePath = filePath,
                        PermanentDelete = request.PermanentDelete
                    }, cancellationToken);

                    result.SuccessfulDeletes.Add(filePath);
                }
                catch (Exception ex)
                {
                    result.FailedDeletes.Add(new DeleteError
                    {
                        FilePath = filePath,
                        ErrorMessage = ex.Message,
                        ErrorCode = ex.GetType().Name
                    });
                }
            }

            return result;
        }

        /// <summary>
        /// 复制文件
        /// </summary>
        public virtual async Task<FileCopyResult> CopyFileAsync(FileCopyRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始复制文件: {SourceFilePath} -> {DestinationFilePath}", 
                    request.SourceFilePath, request.DestinationFilePath);

                var result = await DoCopyFileAsync(request, cancellationToken);

                Logger.LogInformation("文件复制完成: {DestinationFilePath}, 结果: {Success}", 
                    request.DestinationFilePath, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "文件复制失败: {SourceFilePath} -> {DestinationFilePath}", 
                    request.SourceFilePath, request.DestinationFilePath);
                return new FileCopyResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DestinationFilePath = request.DestinationFilePath
                };
            }
        }

        /// <summary>
        /// 移动文件
        /// </summary>
        public virtual async Task<FileMoveResult> MoveFileAsync(FileMoveRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始移动文件: {SourceFilePath} -> {DestinationFilePath}", 
                    request.SourceFilePath, request.DestinationFilePath);

                var result = await DoMoveFileAsync(request, cancellationToken);

                Logger.LogInformation("文件移动完成: {DestinationFilePath}, 结果: {Success}", 
                    request.DestinationFilePath, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "文件移动失败: {SourceFilePath} -> {DestinationFilePath}", 
                    request.SourceFilePath, request.DestinationFilePath);
                return new FileMoveResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DestinationFilePath = request.DestinationFilePath
                };
            }
        }

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        public abstract Task<FileExistsResult> FileExistsAsync(FileExistsRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取文件信息
        /// </summary>
        public abstract Task<FileInfoResult> GetFileInfoAsync(FileInfoRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成预签名URL
        /// </summary>
        public virtual Task<PresignedUrlResult> GeneratePresignedUrlAsync(PresignedUrlRequest request, CancellationToken cancellationToken = default)
        {
            throw new NotSupportedException($"存储类型 {StorageType} 不支持预签名URL");
        }

        /// <summary>
        /// 列出文件
        /// </summary>
        public abstract Task<FileListResult> ListFilesAsync(FileListRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取存储统计信息
        /// </summary>
        public abstract Task<StorageStatisticsResult> GetStorageStatisticsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 健康检查
        /// </summary>
        public abstract Task<HealthCheckResult> HealthCheckAsync(CancellationToken cancellationToken = default);

        #region 文件快传功能 - REQ-010

        /// <summary>
        /// 检查文件是否已存在（基于MD5和文件大小）
        /// </summary>
        public virtual async Task<FileExistenceCheckResult> CheckFileExistenceAsync(FileExistenceCheckRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始检查文件存在性: MD5={MD5Hash}, Size={FileSize}", request.MD5Hash, request.FileSize);

                var result = await DoCheckFileExistenceAsync(request, cancellationToken);

                Logger.LogInformation("文件存在性检查完成: MD5={MD5Hash}, Exists={Exists}, MatchCount={MatchCount}",
                    request.MD5Hash, result.Exists, result.MatchCount);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "文件存在性检查失败: MD5={MD5Hash}", request.MD5Hash);
                return FileExistenceCheckResult.Failure(ex.Message);
            }
        }

        /// <summary>
        /// 快传文件（基于已存在的文件创建新的引用）
        /// </summary>
        public virtual async Task<QuickUploadResult> QuickUploadFileAsync(QuickUploadRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始快传文件: SourceFileId={SourceFileId}, NewFilePath={NewFilePath}",
                    request.SourceFileId, request.NewFilePath);

                var result = await DoQuickUploadFileAsync(request, cancellationToken);

                Logger.LogInformation("文件快传完成: SourceFileId={SourceFileId}, NewFileId={NewFileId}, Success={Success}",
                    request.SourceFileId, result.NewFileId, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "文件快传失败: SourceFileId={SourceFileId}", request.SourceFileId);
                return QuickUploadResult.Failure(ex.Message);
            }
        }

        /// <summary>
        /// 增加文件引用计数
        /// </summary>
        public virtual async Task<ReferenceCountResult> IncrementReferenceCountAsync(string fileId, int increment = 1, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始增加文件引用计数: FileId={FileId}, Increment={Increment}", fileId, increment);

                var result = await DoIncrementReferenceCountAsync(fileId, increment, cancellationToken);

                Logger.LogInformation("文件引用计数增加完成: FileId={FileId}, PreviousCount={PreviousCount}, CurrentCount={CurrentCount}",
                    fileId, result.PreviousCount, result.CurrentCount);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "增加文件引用计数失败: FileId={FileId}", fileId);
                return ReferenceCountResult.Failure(fileId, ex.Message, ReferenceCountOperationType.Increment);
            }
        }

        /// <summary>
        /// 减少文件引用计数
        /// </summary>
        public virtual async Task<ReferenceCountResult> DecrementReferenceCountAsync(string fileId, int decrement = 1, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始减少文件引用计数: FileId={FileId}, Decrement={Decrement}", fileId, decrement);

                var result = await DoDecrementReferenceCountAsync(fileId, decrement, cancellationToken);

                Logger.LogInformation("文件引用计数减少完成: FileId={FileId}, PreviousCount={PreviousCount}, CurrentCount={CurrentCount}",
                    fileId, result.PreviousCount, result.CurrentCount);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "减少文件引用计数失败: FileId={FileId}", fileId);
                return ReferenceCountResult.Failure(fileId, ex.Message, ReferenceCountOperationType.Decrement);
            }
        }

        /// <summary>
        /// 获取文件引用计数
        /// </summary>
        public virtual async Task<ReferenceCountResult> GetReferenceCountAsync(string fileId, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始获取文件引用计数: FileId={FileId}", fileId);

                var result = await DoGetReferenceCountAsync(fileId, cancellationToken);

                Logger.LogInformation("获取文件引用计数完成: FileId={FileId}, CurrentCount={CurrentCount}",
                    fileId, result.CurrentCount);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "获取文件引用计数失败: FileId={FileId}", fileId);
                return ReferenceCountResult.Failure(fileId, ex.Message, ReferenceCountOperationType.Get);
            }
        }

        /// <summary>
        /// 清理无引用的文件
        /// </summary>
        public virtual async Task<CleanupResult> CleanupUnreferencedFilesAsync(CleanupRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始清理无引用文件: Mode={Mode}, MaxCount={MaxCount}, PreviewOnly={PreviewOnly}",
                    request.Mode, request.MaxCleanupCount, request.PreviewOnly);

                var result = await DoCleanupUnreferencedFilesAsync(request, cancellationToken);

                Logger.LogInformation("清理无引用文件完成: CleanedCount={CleanedCount}, SavedSpace={SavedSpace}, Success={Success}",
                    result.CleanedCount, result.SavedSpace, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "清理无引用文件失败");
                return CleanupResult.Failure(ex.Message, request.Mode);
            }
        }

        #endregion

        #region 短链分享功能 - REQ-005

        /// <summary>
        /// 创建文件分享
        /// </summary>
        public virtual async Task<ShareCreationResult> CreateFileShareAsync(ShareCreationRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始创建文件分享: FileId={FileId}, ShareType={ShareType}",
                    request.FileId, request.ShareType);

                var result = await DoCreateFileShareAsync(request, cancellationToken);

                Logger.LogInformation("文件分享创建完成: FileId={FileId}, ShareCode={ShareCode}, Success={Success}",
                    request.FileId, result.ShareCode, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "创建文件分享失败: FileId={FileId}", request.FileId);
                return ShareCreationResult.Failure(ex.Message);
            }
        }

        /// <summary>
        /// 获取分享信息
        /// </summary>
        public virtual async Task<ShareInfoResult> GetShareInfoAsync(string shareCode, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始获取分享信息: ShareCode={ShareCode}", shareCode);

                var result = await DoGetShareInfoAsync(shareCode, cancellationToken);

                Logger.LogInformation("获取分享信息完成: ShareCode={ShareCode}, Success={Success}",
                    shareCode, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "获取分享信息失败: ShareCode={ShareCode}", shareCode);
                return ShareInfoResult.Failure(ex.Message);
            }
        }

        /// <summary>
        /// 验证分享访问权限
        /// </summary>
        public virtual async Task<ShareAccessResult> ValidateShareAccessAsync(ShareAccessRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始验证分享访问权限: ShareCode={ShareCode}, IP={IPAddress}",
                    request.ShareCode, request.IPAddress);

                var result = await DoValidateShareAccessAsync(request, cancellationToken);

                Logger.LogInformation("分享访问权限验证完成: ShareCode={ShareCode}, IsAllowed={IsAllowed}",
                    request.ShareCode, result.IsAllowed);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "验证分享访问权限失败: ShareCode={ShareCode}", request.ShareCode);
                return ShareAccessResult.Deny(ex.Message);
            }
        }

        /// <summary>
        /// 通过分享下载文件
        /// </summary>
        public virtual async Task<ShareDownloadResult> DownloadFileByShareAsync(ShareDownloadRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始通过分享下载文件: ShareCode={ShareCode}, IsPreview={IsPreview}",
                    request.ShareCode, request.IsPreview);

                var result = await DoDownloadFileByShareAsync(request, cancellationToken);

                Logger.LogInformation("分享文件下载完成: ShareCode={ShareCode}, Success={Success}",
                    request.ShareCode, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "通过分享下载文件失败: ShareCode={ShareCode}", request.ShareCode);
                return ShareDownloadResult.Failure(ex.Message);
            }
        }

        /// <summary>
        /// 更新分享设置
        /// </summary>
        public virtual async Task<ShareUpdateResult> UpdateShareAsync(ShareUpdateRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始更新分享设置: ShareCode={ShareCode}", request.ShareCode);

                var result = await DoUpdateShareAsync(request, cancellationToken);

                Logger.LogInformation("分享设置更新完成: ShareCode={ShareCode}, Success={Success}",
                    request.ShareCode, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "更新分享设置失败: ShareCode={ShareCode}", request.ShareCode);
                return ShareUpdateResult.Failure(ex.Message);
            }
        }

        /// <summary>
        /// 删除分享
        /// </summary>
        public virtual async Task<ShareDeleteResult> DeleteShareAsync(string shareCode, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始删除分享: ShareCode={ShareCode}", shareCode);

                var result = await DoDeleteShareAsync(shareCode, cancellationToken);

                Logger.LogInformation("分享删除完成: ShareCode={ShareCode}, Success={Success}",
                    shareCode, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "删除分享失败: ShareCode={ShareCode}", shareCode);
                return ShareDeleteResult.Failure(ex.Message);
            }
        }

        /// <summary>
        /// 获取文件的分享列表
        /// </summary>
        public virtual async Task<ShareListResult> GetFileSharesAsync(string fileId, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始获取文件分享列表: FileId={FileId}", fileId);

                var result = await DoGetFileSharesAsync(fileId, cancellationToken);

                Logger.LogInformation("获取文件分享列表完成: FileId={FileId}, Count={Count}",
                    fileId, result.Shares?.Count ?? 0);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "获取文件分享列表失败: FileId={FileId}", fileId);
                return ShareListResult.Failure(ex.Message);
            }
        }

        /// <summary>
        /// 批量管理分享
        /// </summary>
        public virtual async Task<BatchShareManagementResult> BatchManageSharesAsync(BatchShareManagementRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                Logger.LogInformation("开始批量管理分享: Operation={Operation}, Count={Count}",
                    request.Operation, request.ShareCodes?.Count ?? 0);

                var result = await DoBatchManageSharesAsync(request, cancellationToken);

                Logger.LogInformation("批量管理分享完成: Operation={Operation}, SuccessCount={SuccessCount}, FailedCount={FailedCount}",
                    request.Operation, result.SuccessCount, result.FailedCount);

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "批量管理分享失败: Operation={Operation}", request.Operation);
                return new BatchShareManagementResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        #endregion

        // 抽象方法，由具体实现类重写
        protected abstract Task<FileUploadResult> DoUploadFileAsync(FileUploadRequest request, CancellationToken cancellationToken);
        protected abstract Task<ChunkedUploadResult> DoUploadChunkAsync(ChunkedUploadRequest request, CancellationToken cancellationToken);
        protected abstract Task<FileUploadResult> DoCompleteChunkedUploadAsync(CompleteChunkedUploadRequest request, CancellationToken cancellationToken);
        protected abstract Task<FileDownloadResult> DoDownloadFileAsync(FileDownloadRequest request, CancellationToken cancellationToken);
        protected abstract Task<Stream> DoDownloadFileRangeAsync(FileRangeDownloadRequest request, CancellationToken cancellationToken);
        protected abstract Task<FileDeleteResult> DoDeleteFileAsync(FileDeleteRequest request, CancellationToken cancellationToken);
        protected abstract Task<FileCopyResult> DoCopyFileAsync(FileCopyRequest request, CancellationToken cancellationToken);
        protected abstract Task<FileMoveResult> DoMoveFileAsync(FileMoveRequest request, CancellationToken cancellationToken);

        // 文件快传相关抽象方法
        protected abstract Task<FileExistenceCheckResult> DoCheckFileExistenceAsync(FileExistenceCheckRequest request, CancellationToken cancellationToken);
        protected abstract Task<QuickUploadResult> DoQuickUploadFileAsync(QuickUploadRequest request, CancellationToken cancellationToken);
        protected abstract Task<ReferenceCountResult> DoIncrementReferenceCountAsync(string fileId, int increment, CancellationToken cancellationToken);
        protected abstract Task<ReferenceCountResult> DoDecrementReferenceCountAsync(string fileId, int decrement, CancellationToken cancellationToken);
        protected abstract Task<ReferenceCountResult> DoGetReferenceCountAsync(string fileId, CancellationToken cancellationToken);
        protected abstract Task<CleanupResult> DoCleanupUnreferencedFilesAsync(CleanupRequest request, CancellationToken cancellationToken);

        // 短链分享相关抽象方法
        protected abstract Task<ShareCreationResult> DoCreateFileShareAsync(ShareCreationRequest request, CancellationToken cancellationToken);
        protected abstract Task<ShareInfoResult> DoGetShareInfoAsync(string shareCode, CancellationToken cancellationToken);
        protected abstract Task<ShareAccessResult> DoValidateShareAccessAsync(ShareAccessRequest request, CancellationToken cancellationToken);
        protected abstract Task<ShareDownloadResult> DoDownloadFileByShareAsync(ShareDownloadRequest request, CancellationToken cancellationToken);
        protected abstract Task<ShareUpdateResult> DoUpdateShareAsync(ShareUpdateRequest request, CancellationToken cancellationToken);
        protected abstract Task<ShareDeleteResult> DoDeleteShareAsync(string shareCode, CancellationToken cancellationToken);
        protected abstract Task<ShareListResult> DoGetFileSharesAsync(string fileId, CancellationToken cancellationToken);
        protected abstract Task<BatchShareManagementResult> DoBatchManageSharesAsync(BatchShareManagementRequest request, CancellationToken cancellationToken);
    }
}
