<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<RootNamespace>TSZ.ServiceBase.FileStorageCenter</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Volo.Abp.EntityFrameworkCore.MySQL"  />
		<PackageReference Include="Volo.Abp.PermissionManagement.EntityFrameworkCore"  />
		<PackageReference Include="Volo.Abp.SettingManagement.EntityFrameworkCore"  />
		<PackageReference Include="Volo.Abp.Identity.EntityFrameworkCore"  />
		<PackageReference Include="Volo.Abp.IdentityServer.EntityFrameworkCore"  />
		<PackageReference Include="Volo.Abp.BackgroundJobs.EntityFrameworkCore"  />
		<PackageReference Include="Volo.Abp.AuditLogging.EntityFrameworkCore"  />
		<PackageReference Include="Volo.Abp.TenantManagement.EntityFrameworkCore"  />
		<PackageReference Include="Volo.Abp.FeatureManagement.EntityFrameworkCore"  />
		<PackageReference Include="TSZ.Abp.Modulies"  />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\2_Domain\TSZ.ServiceBase.FileStorageCenter.Domain.csproj" />
	</ItemGroup>

</Project>
