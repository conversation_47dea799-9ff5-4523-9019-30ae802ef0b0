﻿using AutoMapper;
using System.Text.Json;
using TSZ.ServiceBase.FileStorageCenter.FileStorage;
using TSZ.ServiceBase.FileStorageCenter.FileStorage.Dto;

namespace TSZ.ServiceBase.FileStorageCenter
{
    /// <summary>
    /// AutoMapper配置文件
    /// </summary>
    public class FileStorageCenterApplicationAutoMapperProfile : Profile
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public FileStorageCenterApplicationAutoMapperProfile()
        {
            /* You can configure your AutoMapper mapping configuration here.
             * Alternatively, you can split your mapping configurations
             * into multiple profile classes for a better organization. */

            // 文件信息映射
            CreateMap<FileInfo, FileInfoDto>()
                .ForMember(dest => dest.ExtraProperties, opt => opt.MapFrom(src =>
                    DeserializeExtraProperties(src.ExtraProperties)));

            CreateMap<FileInfoDto, FileInfo>()
                .ForMember(dest => dest.ExtraProperties, opt => opt.MapFrom(src =>
                    SerializeExtraProperties(src.ExtraProperties)));

            // 文件分享映射
            CreateMap<FileShare, FileShareDto>();
            CreateMap<FileShareDto, FileShare>();

            // 文件统计映射
            CreateMap<FileStatistics, FileStatisticsDto>()
                .ForMember(dest => dest.CountByStorageType, opt => opt.Ignore())
                .ForMember(dest => dest.CountByStatus, opt => opt.Ignore())
                .ForMember(dest => dest.CountByExtension, opt => opt.Ignore());

            CreateMap<ShareStatistics, ShareStatisticsDto>();
        }

        /// <summary>
        /// 反序列化扩展属性
        /// </summary>
        private static Dictionary<string, object> DeserializeExtraProperties(string extraProperties)
        {
            if (string.IsNullOrEmpty(extraProperties))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(extraProperties);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 序列化扩展属性
        /// </summary>
        private static string SerializeExtraProperties(Dictionary<string, object> extraProperties)
        {
            if (extraProperties == null)
                return null;

            try
            {
                return JsonSerializer.Serialize(extraProperties);
            }
            catch
            {
                return null;
            }
        }
    }
}
