using System.Collections.Generic;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 完成分片上传请求
    /// </summary>
    public class CompleteChunkedUploadRequest
    {
        /// <summary>
        /// 上传ID
        /// </summary>
        public string UploadId { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 分片信息列表
        /// </summary>
        public List<ChunkInfo> Chunks { get; set; } = new List<ChunkInfo>();

        /// <summary>
        /// 文件总大小
        /// </summary>
        public long TotalSize { get; set; }
    }
}
