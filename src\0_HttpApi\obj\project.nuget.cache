{"version": 2, "dgSpecHash": "lAZMCVe7SRU=", "success": false, "projectFilePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.endpointutil\\0.1.1\\alibabacloud.endpointutil.0.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.openapiclient\\0.0.6\\alibabacloud.openapiclient.0.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.openapiutil\\1.0.3\\alibabacloud.openapiutil.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.sdk.sts20150401\\1.0.0\\alibabacloud.sdk.sts20150401.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\alibabacloud.teautil\\0.1.9\\alibabacloud.teautil.0.1.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aliyun.credentials\\1.3.1\\aliyun.credentials.1.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bouncycastle\\1.8.5\\bouncycastle.1.8.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\configureawait.fody\\3.3.1\\configureawait.fody.3.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fody\\6.0.2\\fody.6.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jetbrains.annotations\\2020.3.0\\jetbrains.annotations.2020.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\5.0.5\\microsoft.aspnetcore.authorization.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.abstractions\\1.0.0\\microsoft.aspnetcore.hosting.abstractions.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.server.abstractions\\1.0.0\\microsoft.aspnetcore.hosting.server.abstractions.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http\\1.0.0\\microsoft.aspnetcore.http.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.abstractions\\1.0.0\\microsoft.aspnetcore.http.abstractions.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.extensions\\1.0.0\\microsoft.aspnetcore.http.extensions.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.features\\1.0.0\\microsoft.aspnetcore.http.features.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.jsonpatch\\5.0.5\\microsoft.aspnetcore.jsonpatch.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\5.0.5\\microsoft.aspnetcore.metadata.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.abstractions\\1.0.0\\microsoft.aspnetcore.mvc.abstractions.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.core\\1.0.0\\microsoft.aspnetcore.mvc.core.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.newtonsoftjson\\5.0.5\\microsoft.aspnetcore.mvc.newtonsoftjson.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor.extensions\\5.0.5\\microsoft.aspnetcore.mvc.razor.extensions.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor.runtimecompilation\\5.0.5\\microsoft.aspnetcore.mvc.razor.runtimecompilation.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.versioning\\5.0.0\\microsoft.aspnetcore.mvc.versioning.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.razor.language\\5.0.5\\microsoft.aspnetcore.razor.language.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.routing\\1.0.0\\microsoft.aspnetcore.routing.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.routing.abstractions\\1.0.0\\microsoft.aspnetcore.routing.abstractions.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.webutilities\\1.0.0\\microsoft.aspnetcore.webutilities.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\3.0.0\\microsoft.codeanalysis.analyzers.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\3.8.0\\microsoft.codeanalysis.common.3.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\3.8.0\\microsoft.codeanalysis.csharp.3.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.razor\\5.0.5\\microsoft.codeanalysis.razor.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\5.0.0\\microsoft.extensions.configuration.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\5.0.0\\microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\5.0.0\\microsoft.extensions.configuration.binder.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\5.0.0\\microsoft.extensions.configuration.commandline.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\5.0.0\\microsoft.extensions.configuration.environmentvariables.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\5.0.0\\microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\5.0.0\\microsoft.extensions.configuration.json.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\5.0.0\\microsoft.extensions.configuration.usersecrets.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\5.0.1\\microsoft.extensions.dependencyinjection.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\5.0.0\\microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\5.0.0\\microsoft.extensions.dependencymodel.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\5.0.0\\microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.composite\\5.0.0\\microsoft.extensions.fileproviders.composite.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.embedded\\1.0.0\\microsoft.extensions.fileproviders.embedded.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\5.0.0\\microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\5.0.0\\microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\5.0.0\\microsoft.extensions.hosting.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization\\5.0.5\\microsoft.extensions.localization.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization.abstractions\\5.0.5\\microsoft.extensions.localization.abstractions.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\5.0.0\\microsoft.extensions.logging.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\5.0.0\\microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\1.0.0\\microsoft.extensions.objectpool.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\5.0.0\\microsoft.extensions.options.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\5.0.0\\microsoft.extensions.options.configurationextensions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.platformabstractions\\1.0.0\\microsoft.extensions.platformabstractions.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\5.0.0\\microsoft.extensions.primitives.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.http.headers\\1.0.0\\microsoft.net.http.headers.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\2.1.2\\microsoft.netcore.platforms.2.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.primitives\\4.0.1\\microsoft.win32.primitives.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json.bson\\1.0.2\\newtonsoft.json.bson.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.context\\5.1.0\\nito.asyncex.context.5.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.coordination\\5.1.0\\nito.asyncex.coordination.5.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.tasks\\5.1.0\\nito.asyncex.tasks.5.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.collections.deque\\1.1.0\\nito.collections.deque.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.disposables\\2.2.0\\nito.disposables.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuglify\\1.13.8\\nuglify.1.13.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.0.0\\runtime.native.system.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.net.http\\4.0.1\\runtime.native.system.net.http.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography\\4.0.0\\runtime.native.system.security.cryptography.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\scriban\\3.6.0\\scriban.3.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.appcontext\\4.1.0\\system.appcontext.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.0.0\\system.buffers.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.0.12\\system.collections.concurrent.4.0.12.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\5.0.0\\system.collections.immutable.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.nongeneric\\4.0.1\\system.collections.nongeneric.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.specialized\\4.0.1\\system.collections.specialized.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel\\4.0.1\\system.componentmodel.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.annotations\\5.0.0\\system.componentmodel.annotations.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.primitives\\4.1.0\\system.componentmodel.primitives.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.typeconverter\\4.1.0\\system.componentmodel.typeconverter.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.contracts\\4.0.1\\system.diagnostics.contracts.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\4.0.0\\system.diagnostics.diagnosticsource.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.1.0\\system.diagnostics.tracing.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.calendars\\4.0.1\\system.globalization.calendars.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.0.1\\system.globalization.extensions.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.0.1\\system.io.filesystem.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.0.1\\system.io.filesystem.primitives.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.dynamic.core\\1.2.9\\system.linq.dynamic.core.1.2.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.queryable\\4.3.0\\system.linq.queryable.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.primitives\\4.0.11\\system.net.primitives.4.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.websockets\\4.0.0\\system.net.websockets.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\5.0.0\\system.reflection.metadata.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.7.1\\system.runtime.compilerservices.unsafe.4.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.0.1\\system.runtime.handles.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.1.0\\system.runtime.interopservices.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.loader\\4.3.0\\system.runtime.loader.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.numerics\\4.0.1\\system.runtime.numerics.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.claims\\4.0.1\\system.security.claims.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.2.0\\system.security.cryptography.algorithms.4.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\4.2.0\\system.security.cryptography.cng.4.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.csp\\4.0.0\\system.security.cryptography.csp.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.0.0\\system.security.cryptography.encoding.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.openssl\\4.0.0\\system.security.cryptography.openssl.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.0.0\\system.security.cryptography.primitives.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.x509certificates\\4.1.0\\system.security.cryptography.x509certificates.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal\\4.0.1\\system.security.principal.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\4.5.1\\system.text.encoding.codepages.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.extensions\\4.0.11\\system.text.encoding.extensions.4.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\4.0.0\\system.text.encodings.web.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.regularexpressions\\4.1.0\\system.text.regularexpressions.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\tea\\1.0.4\\tea.1.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\timezoneconverter\\3.4.0\\timezoneconverter.3.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.account.application.contracts\\0.19.0\\volo.abp.account.application.contracts.0.19.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.account.httpapi\\0.19.0\\volo.abp.account.httpapi.0.19.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.apiversioning.abstractions\\4.3.0\\volo.abp.apiversioning.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore\\4.3.0\\volo.abp.aspnetcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc\\4.3.0\\volo.abp.aspnetcore.mvc.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.contracts\\4.3.0\\volo.abp.aspnetcore.mvc.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditing\\4.3.0\\volo.abp.auditing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditlogging.domain.shared\\0.4.1\\volo.abp.auditlogging.domain.shared.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.authorization\\4.3.0\\volo.abp.authorization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.authorization.abstractions\\4.3.0\\volo.abp.authorization.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs.abstractions\\4.3.0\\volo.abp.backgroundjobs.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs.domain.shared\\0.4.1\\volo.abp.backgroundjobs.domain.shared.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.core\\4.3.0\\volo.abp.core.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.data\\4.3.0\\volo.abp.data.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.application\\4.3.0\\volo.abp.ddd.application.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.application.contracts\\4.3.0\\volo.abp.ddd.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.domain\\4.3.0\\volo.abp.ddd.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.emailing\\4.3.0\\volo.abp.emailing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.eventbus\\4.3.0\\volo.abp.eventbus.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.eventbus.abstractions\\4.3.0\\volo.abp.eventbus.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.exceptionhandling\\4.3.0\\volo.abp.exceptionhandling.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.application.contracts\\0.15.0\\volo.abp.featuremanagement.application.contracts.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.domain.shared\\0.15.0\\volo.abp.featuremanagement.domain.shared.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.httpapi\\0.15.0\\volo.abp.featuremanagement.httpapi.0.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.features\\4.3.0\\volo.abp.features.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.globalfeatures\\4.3.0\\volo.abp.globalfeatures.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.guids\\4.3.0\\volo.abp.guids.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.http\\4.3.0\\volo.abp.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.http.abstractions\\4.3.0\\volo.abp.http.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.application.contracts\\0.3.0\\volo.abp.identity.application.contracts.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.domain.shared\\0.3.0\\volo.abp.identity.domain.shared.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.httpapi\\0.3.0\\volo.abp.identity.httpapi.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identityserver.domain.shared\\0.6.0\\volo.abp.identityserver.domain.shared.0.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.json\\4.3.0\\volo.abp.json.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.localization.abstractions\\4.3.0\\volo.abp.localization.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.minify\\4.3.0\\volo.abp.minify.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.multitenancy\\4.3.0\\volo.abp.multitenancy.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectextending\\2.4.0\\volo.abp.objectextending.2.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectmapping\\4.3.0\\volo.abp.objectmapping.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain.shared\\0.3.0\\volo.abp.permissionmanagement.domain.shared.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.httpapi\\0.13.0\\volo.abp.permissionmanagement.httpapi.0.13.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.security\\4.3.0\\volo.abp.security.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.application.contracts\\4.3.0\\volo.abp.settingmanagement.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.httpapi\\4.3.0\\volo.abp.settingmanagement.httpapi.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settings\\4.3.0\\volo.abp.settings.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.specifications\\4.3.0\\volo.abp.specifications.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.tenantmanagement.application.contracts\\0.3.0\\volo.abp.tenantmanagement.application.contracts.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.tenantmanagement.domain.shared\\0.3.0\\volo.abp.tenantmanagement.domain.shared.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.tenantmanagement.httpapi\\0.3.0\\volo.abp.tenantmanagement.httpapi.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.texttemplating\\4.3.0\\volo.abp.texttemplating.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.threading\\4.3.0\\volo.abp.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.timing\\4.3.0\\volo.abp.timing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ui\\4.3.0\\volo.abp.ui.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ui.navigation\\4.3.0\\volo.abp.ui.navigation.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.uow\\4.3.0\\volo.abp.uow.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.domain.shared\\0.3.0\\volo.abp.users.domain.shared.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.validation\\0.3.0\\volo.abp.validation.0.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.virtualfilesystem\\4.3.0\\volo.abp.virtualfilesystem.4.3.0.nupkg.sha512"], "logs": [{"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.Account.HttpApi 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.Account.HttpApi", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.FeatureManagement.HttpApi 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.FeatureManagement.HttpApi", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.Identity.HttpApi 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.Identity.HttpApi", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.PermissionManagement.HttpApi 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.PermissionManagement.HttpApi", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.SettingManagement.HttpApi 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.SettingManagement.HttpApi", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "message": "项目依赖项 Volo.Abp.TenantManagement.HttpApi 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.TenantManagement.HttpApi", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Application.Contracts 不提供依赖项 AlibabaCloud.SDK.Sts20150401 的下限(含)。已改为解析 AlibabaCloud.SDK.Sts20150401 1.0.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "AlibabaCloud.SDK.Sts20150401", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Application.Contracts 不提供依赖项 Microsoft.AspNetCore.Mvc.Core 的下限(含)。已改为解析 Microsoft.AspNetCore.Mvc.Core 1.0.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Microsoft.AspNetCore.Mvc.Core", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Microsoft.Extensions.FileProviders.Embedded 的下限(含)。已改为解析 Microsoft.Extensions.FileProviders.Embedded 1.0.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Microsoft.Extensions.FileProviders.Embedded", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.Account.HttpApi 0.19.0 不提供依赖项 Volo.Abp.Account.Application.Contracts 的下限(含)。已改为解析 Volo.Abp.Account.Application.Contracts 0.19.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.Account.Application.Contracts", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.AuditLogging.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.AuditLogging.Domain.Shared 0.4.1。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.AuditLogging.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.BackgroundJobs.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.BackgroundJobs.Domain.Shared 0.4.1。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.BackgroundJobs.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.FeatureManagement.HttpApi 0.15.0 不提供依赖项 Volo.Abp.FeatureManagement.Application.Contracts 的下限(含)。已改为解析 Volo.Abp.FeatureManagement.Application.Contracts 0.15.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.FeatureManagement.Application.Contracts", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.FeatureManagement.Application.Contracts 0.15.0 不提供依赖项 Volo.Abp.FeatureManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.FeatureManagement.Domain.Shared 0.15.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.FeatureManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.Identity.HttpApi 0.3.0 不提供依赖项 Volo.Abp.Identity.Application.Contracts 的下限(含)。已改为解析 Volo.Abp.Identity.Application.Contracts 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.Identity.Application.Contracts", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.Identity.Application.Contracts 0.3.0 不提供依赖项 Volo.Abp.Identity.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.Identity.Domain.Shared 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.Identity.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.IdentityServer.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.IdentityServer.Domain.Shared 0.6.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.IdentityServer.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Application.Contracts 不提供依赖项 Volo.Abp.ObjectExtending 的下限(含)。已改为解析 Volo.Abp.ObjectExtending 2.4.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.ObjectExtending", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.PermissionManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.PermissionManagement.Domain.Shared 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.PermissionManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.SettingManagement.HttpApi 4.3.0 不提供依赖项 Volo.Abp.SettingManagement.Application.Contracts 的下限(含)。已改为解析 Volo.Abp.SettingManagement.Application.Contracts 4.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.SettingManagement.Application.Contracts", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.TenantManagement.HttpApi 0.3.0 不提供依赖项 Volo.Abp.TenantManagement.Application.Contracts 的下限(含)。已改为解析 Volo.Abp.TenantManagement.Application.Contracts 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.TenantManagement.Application.Contracts", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Volo.Abp.TenantManagement.Application.Contracts 0.3.0 不提供依赖项 Volo.Abp.TenantManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.TenantManagement.Domain.Shared 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.TenantManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.Validation 的下限(含)。已改为解析 Volo.Abp.Validation 0.3.0。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.Validation", "targetGraphs": ["net9.0"]}, {"code": "NU1701", "level": "Warning", "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net9.0”还原包“BouncyCastle 1.8.5”。此包可能与项目不完全兼容。", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "warningLevel": 1, "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "BouncyCastle", "targetGraphs": ["net9.0"]}, {"code": "NU1107", "level": "Error", "message": "Volo.Abp.PermissionManagement.Application.Contracts 中检测到版本冲突。直接安装/引用 Volo.Abp.PermissionManagement.Application.Contracts 0.3.0 到项目 TSZ.ServiceBase.FileStorageCenter.HttpApi 可解决此问题。 \r\n TSZ.ServiceBase.FileStorageCenter.HttpApi -> TSZ.ServiceBase.FileStorageCenter.Application.Contracts -> Volo.Abp.PermissionManagement.Application.Contracts \r\n TSZ.ServiceBase.FileStorageCenter.HttpApi -> Volo.Abp.PermissionManagement.HttpApi 0.13.0 -> Volo.Abp.PermissionManagement.Application.Contracts (>= 0.13.0).", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "filePath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "libraryId": "Volo.Abp.PermissionManagement.Application.Contracts", "targetGraphs": ["net9.0"]}]}