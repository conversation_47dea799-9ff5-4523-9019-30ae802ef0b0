using System;
using System.Collections.Generic;
using System.IO;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件上传请求
    /// </summary>
    public class FileUploadRequest
    {
        /// <summary>
        /// 文件流
        /// </summary>
        public Stream FileStream { get; set; }

        /// <summary>
        /// 文件路径（相对路径）
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 内容类型
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// MD5哈希值
        /// </summary>
        public string MD5Hash { get; set; }

        /// <summary>
        /// 是否加密
        /// </summary>
        public bool IsEncrypted { get; set; }

        /// <summary>
        /// 加密密钥
        /// </summary>
        public string EncryptionKey { get; set; }

        /// <summary>
        /// 元数据
        /// </summary>
        public Dictionary<string, string> Metadata { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 标签
        /// </summary>
        public Dictionary<string, string> Tags { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 是否覆盖现有文件
        /// </summary>
        public bool Overwrite { get; set; }
    }
}
