﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TSZ.ServiceBase.FileStorageCenter
{
    /// <summary>
    /// 文件上传的行为
    /// </summary>
    public enum FileUploadBehaviours
    {
        /// <summary>
        /// 创建新文件，如果文件存在则会自动创建新的文件名并返回
        /// </summary>
        CreateNew = 1,
        /// <summary>
        /// 覆盖文件，如果目标文件不存在则直接创建新文件，否则覆盖原文件
        /// </summary>
        Override = 2,
        /// <summary>
        /// 断点续传，必须确保服务器上已经存在未上传完整的文件的前半部分
        /// </summary>
        ResumeFromBreakPoint = 3,
    }
}
