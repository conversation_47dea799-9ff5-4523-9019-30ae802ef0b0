using System.Collections.Generic;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 批量删除结果
    /// </summary>
    public class BatchDeleteResult
    {
        /// <summary>
        /// 成功删除的文件
        /// </summary>
        public List<string> SuccessfulDeletes { get; set; } = new List<string>();

        /// <summary>
        /// 删除失败的文件
        /// </summary>
        public List<DeleteError> FailedDeletes { get; set; } = new List<DeleteError>();
    }
}
