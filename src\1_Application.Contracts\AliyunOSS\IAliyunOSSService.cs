﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using static AlibabaCloud.SDK.Sts20150401.Models.AssumeRoleResponseBody;

namespace TSZ.ServiceBase.FileStorageCenter.AliyunOSS
{
    /// <summary>
    /// STS
    /// </summary>
    public interface IAliyunOSSService : IApplicationService
    {
        /// <summary>
        /// 获取STS
        /// </summary>
        /// <returns></returns>
        Task<AssumeRoleResponseBodyCredentials> GetSTSToken();

        /// <summary>
        /// 删除文件信息（临时删除）
        /// </summary>
        /// <param name="bucketName">桶名</param>
        /// <param name="objectName">路径</param>
        /// <returns></returns>
        Task DeleteObject(string bucketName, string objectName);

        /// <summary>
        /// 删除文件信息(永久删除)
        /// </summary>
        /// <param name="bucketName">桶名</param>
        /// <param name="objectName">路径</param>
        /// <param name="versionId">版本号</param>
        /// <returns></returns>
        Task DeleteObjectByVersion(string bucketName, string objectName, string versionId);
    }
}
