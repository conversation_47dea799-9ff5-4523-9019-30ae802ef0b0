﻿using Microsoft.EntityFrameworkCore;
using TSZ.ServiceBase.FileStorageCenter.FileStorage;
using Volo.Abp;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore
{
    public static class FileStorageCenterDbContextModelCreatingExtensions
    {
        public static void ConfigureFileStorageCenter(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            /* Configure your own tables/entities inside here */

            // 配置文件信息实体
            builder.Entity<FileInfo>(b =>
            {
                b.ToTable(FileStorageCenterConsts.DbTablePrefix + "FileInfos", FileStorageCenterConsts.DbSchema);
                b.ConfigureByConvention(); // 自动配置基类属性

                // 配置主键
                b.HasKey(x => x.Id);

                // 配置属性
                b.Property(x => x.FileId).IsRequired().HasMaxLength(32);
                b.Property(x => x.FileName).IsRequired().HasMaxLength(255);
                b.Property(x => x.OriginalFileName).HasMaxLength(255);
                b.Property(x => x.RelativePath).IsRequired().HasMaxLength(1000);
                b.Property(x => x.AbsolutePath).HasMaxLength(2000);
                b.Property(x => x.FileSize).IsRequired();
                b.Property(x => x.MD5Hash).HasMaxLength(32);
                b.Property(x => x.SHA256Hash).HasMaxLength(64);
                b.Property(x => x.MimeType).HasMaxLength(100);
                b.Property(x => x.FileExtension).HasMaxLength(20);
                b.Property(x => x.StorageType).IsRequired();
                b.Property(x => x.StorageConfig).HasMaxLength(100);
                b.Property(x => x.Status).IsRequired();
                b.Property(x => x.ReferenceCount).IsRequired().HasDefaultValue(1);
                b.Property(x => x.IsEncrypted).IsRequired().HasDefaultValue(false);
                b.Property(x => x.EncryptionKey).HasMaxLength(256);
                b.Property(x => x.IsPublic).IsRequired().HasDefaultValue(false);
                b.Property(x => x.AccessCount).IsRequired().HasDefaultValue(0);
                b.Property(x => x.DownloadCount).IsRequired().HasDefaultValue(0);
                b.Property(x => x.Tags).HasMaxLength(1000);
                b.Property(x => x.ExtraProperties).HasMaxLength(2000);
                b.Property(x => x.Remarks).HasMaxLength(500);

                // 配置索引
                b.HasIndex(x => x.FileId).IsUnique(); // 文件ID唯一索引
                b.HasIndex(x => x.MD5Hash); // MD5哈希索引（用于文件快传）
                b.HasIndex(x => x.RelativePath); // 相对路径索引
                b.HasIndex(x => x.StorageType); // 存储类型索引
                b.HasIndex(x => x.Status); // 状态索引
                b.HasIndex(x => x.CreationTime); // 创建时间索引
                b.HasIndex(x => new { x.MD5Hash, x.FileSize }); // MD5+文件大小复合索引（用于文件快传）
                b.HasIndex(x => x.ExpirationTime); // 过期时间索引
                b.HasIndex(x => x.ReferenceCount); // 引用计数索引

                // REQ-009: 多维度查询优化索引
                b.HasIndex(x => x.MimeType); // MIME类型索引
                b.HasIndex(x => x.FileSize); // 文件大小索引
                b.HasIndex(x => x.LastModificationTime); // 最后修改时间索引
                b.HasIndex(x => x.AccessCount); // 访问次数索引（热门文件查询）
                b.HasIndex(x => x.DownloadCount); // 下载次数索引
                b.HasIndex(x => x.IsPublic); // 公开状态索引
                b.HasIndex(x => x.IsEncrypted); // 加密状态索引

                // 复合索引优化常用查询组合
                b.HasIndex(x => new { x.Status, x.CreationTime }); // 状态+创建时间复合索引
                b.HasIndex(x => new { x.StorageType, x.Status }); // 存储类型+状态复合索引
                b.HasIndex(x => new { x.Status, x.AccessCount }); // 状态+访问次数复合索引（热门文件）
                b.HasIndex(x => new { x.MimeType, x.Status }); // MIME类型+状态复合索引
                b.HasIndex(x => new { x.FileSize, x.Status }); // 文件大小+状态复合索引
                b.HasIndex(x => new { x.IsPublic, x.Status }); // 公开状态+文件状态复合索引
            });

            // 配置文件分享实体
            builder.Entity<FileShare>(b =>
            {
                b.ToTable(FileStorageCenterConsts.DbTablePrefix + "FileShares", FileStorageCenterConsts.DbSchema);
                b.ConfigureByConvention(); // 自动配置基类属性

                // 配置主键
                b.HasKey(x => x.Id);

                // 配置属性
                b.Property(x => x.FileInfoId).IsRequired();
                b.Property(x => x.ShareCode).IsRequired().HasMaxLength(12);
                b.Property(x => x.Title).HasMaxLength(200);
                b.Property(x => x.Description).HasMaxLength(500);
                b.Property(x => x.AccessPassword).HasMaxLength(50);
                b.Property(x => x.ShareType).IsRequired();
                b.Property(x => x.Status).IsRequired();
                b.Property(x => x.AccessCount).IsRequired().HasDefaultValue(0);
                b.Property(x => x.DownloadCount).IsRequired().HasDefaultValue(0);
                b.Property(x => x.LastAccessIP).HasMaxLength(45);
                b.Property(x => x.LastAccessUserAgent).HasMaxLength(500);
                b.Property(x => x.AllowedIPs).HasMaxLength(1000);
                b.Property(x => x.AllowDownload).IsRequired().HasDefaultValue(true);
                b.Property(x => x.AllowPreview).IsRequired().HasDefaultValue(true);
                b.Property(x => x.RequireLogin).IsRequired().HasDefaultValue(false);
                b.Property(x => x.ExtraProperties).HasMaxLength(1000);
                b.Property(x => x.Remarks).HasMaxLength(500);

                // 配置外键关系
                b.HasOne(x => x.FileInfo)
                    .WithMany()
                    .HasForeignKey(x => x.FileInfoId)
                    .OnDelete(DeleteBehavior.Cascade);

                // 配置索引
                b.HasIndex(x => x.ShareCode).IsUnique(); // 分享码唯一索引
                b.HasIndex(x => x.FileInfoId); // 文件信息ID索引
                b.HasIndex(x => x.ShareType); // 分享类型索引
                b.HasIndex(x => x.Status); // 状态索引
                b.HasIndex(x => x.CreationTime); // 创建时间索引
                b.HasIndex(x => x.ExpirationTime); // 过期时间索引
                b.HasIndex(x => x.LastAccessTime); // 最后访问时间索引
                b.HasIndex(x => x.AccessCount); // 访问次数索引（用于热门分享查询）
            });
        }
    }
}