{"format": 1, "restore": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj": {}}, "projects": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "projectName": "TSZ.ServiceBase.FileStorageCenter.HttpApi.Host", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\TSZ.ServiceBase.FileStorageCenter.HttpApi.Host.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi.Host\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj"}, "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application\\TSZ.ServiceBase.FileStorageCenter.Application.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application\\TSZ.ServiceBase.FileStorageCenter.Application.csproj"}, "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\3_EntityFrameworkCore.DbMigrations\\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.DbMigrations.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\3_EntityFrameworkCore.DbMigrations\\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.DbMigrations.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentFTP": {"target": "Package", "version": "(, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "(, )"}, "Microsoft.AspNetCore.DataProtection.StackExchangeRedis": {"target": "Package", "version": "(, )"}, "Microsoft.ICU.ICU4C.Runtime": {"target": "Package", "version": "(, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "(, )"}, "Serilog.Sinks.Async": {"target": "Package", "version": "(, )"}, "SkyAPM.Agent.AspNetCore": {"target": "Package", "version": "(, )"}, "TSZ.Abp.Modulies": {"target": "Package", "version": "(, )"}, "TSZ.Common.Core": {"target": "Package", "version": "(, )"}, "TSZ.Common.Core.Helper": {"target": "Package", "version": "(, )"}, "TSZ.Common.OfficeExtends": {"target": "Package", "version": "(, )"}, "TSZ.Infrastructures.Consul": {"target": "Package", "version": "(, )"}, "TSZ.ServiceProxy.Caching": {"target": "Package", "version": "(, )"}, "TSZ.ServiceProxy.Config": {"target": "Package", "version": "(, )"}, "TSZ.ServiceProxy.SeriLog": {"target": "Package", "version": "(, )"}, "Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy": {"target": "Package", "version": "(, )"}, "Volo.Abp.AspNetCore.Serilog": {"target": "Package", "version": "(, )"}, "Volo.Abp.Autofac": {"target": "Package", "version": "(, )"}, "Volo.Abp.Caching.CSRedis": {"target": "Package", "version": "(, )"}, "Volo.Abp.Caching.StackExchangeRedis": {"target": "Package", "version": "(, )"}, "Volo.Abp.Swashbuckle": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "projectName": "TSZ.ServiceBase.FileStorageCenter.HttpApi", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Volo.Abp.Account.HttpApi": {"target": "Package", "version": "(, )"}, "Volo.Abp.FeatureManagement.HttpApi": {"target": "Package", "version": "(, )"}, "Volo.Abp.Identity.HttpApi": {"target": "Package", "version": "(, )"}, "Volo.Abp.PermissionManagement.HttpApi": {"target": "Package", "version": "(, )"}, "Volo.Abp.SettingManagement.HttpApi": {"target": "Package", "version": "(, )"}, "Volo.Abp.TenantManagement.HttpApi": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj", "projectName": "TSZ.ServiceBase.FileStorageCenter.Application.Contracts", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AlibabaCloud.SDK.Sts20150401": {"target": "Package", "version": "(, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "(, )"}, "Volo.Abp.Account.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.FeatureManagement.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.Identity.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.ObjectExtending": {"target": "Package", "version": "(, )"}, "Volo.Abp.PermissionManagement.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.SettingManagement.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.TenantManagement.Application.Contracts": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application\\TSZ.ServiceBase.FileStorageCenter.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application\\TSZ.ServiceBase.FileStorageCenter.Application.csproj", "projectName": "TSZ.ServiceBase.FileStorageCenter.Application", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application\\TSZ.ServiceBase.FileStorageCenter.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj"}, "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[3.7.307.25, )"}, "AlibabaCloud.SDK.Sts20150401": {"target": "Package", "version": "(, )"}, "Aliyun.OSS.SDK.NetCore": {"target": "Package", "version": "(, )"}, "FluentFTP": {"target": "Package", "version": "(, )"}, "Minio": {"target": "Package", "version": "[6.0.3, )"}, "Volo.Abp.Account.Application": {"target": "Package", "version": "(, )"}, "Volo.Abp.FeatureManagement.Application": {"target": "Package", "version": "(, )"}, "Volo.Abp.Identity.Application": {"target": "Package", "version": "(, )"}, "Volo.Abp.PermissionManagement.Application": {"target": "Package", "version": "(, )"}, "Volo.Abp.SettingManagement.Application": {"target": "Package", "version": "(, )"}, "Volo.Abp.TenantManagement.Application": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj", "projectName": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "(, )"}, "Volo.Abp.AuditLogging.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.BackgroundJobs.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.FeatureManagement.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.Identity.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.IdentityServer.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.Localization": {"target": "Package", "version": "(, )"}, "Volo.Abp.ObjectExtending": {"target": "Package", "version": "(, )"}, "Volo.Abp.PermissionManagement.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.SettingManagement.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.TenantManagement.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.Validation": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "projectName": "TSZ.ServiceBase.FileStorageCenter.Domain", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Volo.Abp.AuditLogging.Domain": {"target": "Package", "version": "(, )"}, "Volo.Abp.BackgroundJobs.Domain": {"target": "Package", "version": "(, )"}, "Volo.Abp.Emailing": {"target": "Package", "version": "(, )"}, "Volo.Abp.FeatureManagement.Domain": {"target": "Package", "version": "(, )"}, "Volo.Abp.Identity.Domain": {"target": "Package", "version": "(, )"}, "Volo.Abp.IdentityServer.Domain": {"target": "Package", "version": "(, )"}, "Volo.Abp.PermissionManagement.Domain.Identity": {"target": "Package", "version": "(, )"}, "Volo.Abp.PermissionManagement.Domain.IdentityServer": {"target": "Package", "version": "(, )"}, "Volo.Abp.SettingManagement.Domain": {"target": "Package", "version": "(, )"}, "Volo.Abp.TenantManagement.Domain": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\3_EntityFrameworkCore.DbMigrations\\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.DbMigrations.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\3_EntityFrameworkCore.DbMigrations\\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.DbMigrations.csproj", "projectName": "TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.DbMigrations", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\3_EntityFrameworkCore.DbMigrations\\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.DbMigrations.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\3_EntityFrameworkCore.DbMigrations\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\4_EntityFrameworkCore\\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\4_EntityFrameworkCore\\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "Compile, Build, Native, ContentFiles, Analyzers, BuildTransitive", "target": "Package", "version": "(, )"}, "TSZ.Abp.Modulies": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\4_EntityFrameworkCore\\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\4_EntityFrameworkCore\\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.csproj", "projectName": "TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\4_EntityFrameworkCore\\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\4_EntityFrameworkCore\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"TSZ.Abp.Modulies": {"target": "Package", "version": "(, )"}, "Volo.Abp.AuditLogging.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.BackgroundJobs.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.EntityFrameworkCore.MySQL": {"target": "Package", "version": "(, )"}, "Volo.Abp.FeatureManagement.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.Identity.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.IdentityServer.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.PermissionManagement.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.SettingManagement.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.TenantManagement.EntityFrameworkCore": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}