using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore;
using TSZ.ServiceBase.FileStorageCenter.FileStorage;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件信息仓储EF Core实现
    /// </summary>
    public class EfCoreFileInfoRepository : EfCoreRepository<FileStorageCenterDbContext, FileInfo, Guid>, IFileInfoRepository
    {
        public EfCoreFileInfoRepository(IDbContextProvider<FileStorageCenterDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        /// <summary>
        /// 根据文件ID查找文件信息
        /// </summary>
        public async Task<FileInfo> FindByFileIdAsync(string fileId, bool includeDetails = true, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            if (includeDetails)
            {
                // 如果需要包含详细信息，可以在这里添加Include
            }

            return await query.FirstOrDefaultAsync(x => x.FileId == fileId, cancellationToken);
        }

        /// <summary>
        /// 根据MD5哈希值查找文件信息
        /// </summary>
        public async Task<FileInfo> FindByMD5HashAsync(string md5Hash, bool includeDetails = true, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            return await query.FirstOrDefaultAsync(x => x.MD5Hash == md5Hash, cancellationToken);
        }

        /// <summary>
        /// 根据MD5哈希值和文件大小查找文件信息（用于文件快传）
        /// </summary>
        public async Task<FileInfo> FindByMD5AndSizeAsync(string md5Hash, long fileSize, bool includeDetails = true, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            return await query.FirstOrDefaultAsync(x => x.MD5Hash == md5Hash && x.FileSize == fileSize, cancellationToken);
        }

        /// <summary>
        /// 根据相对路径查找文件信息
        /// </summary>
        public async Task<FileInfo> FindByRelativePathAsync(string relativePath, bool includeDetails = true, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            return await query.FirstOrDefaultAsync(x => x.RelativePath == relativePath, cancellationToken);
        }

        /// <summary>
        /// 根据文件名搜索文件信息（支持模糊查询）
        /// </summary>
        public async Task<List<FileInfo>> SearchByFileNameAsync(string fileName, int skipCount = 0, int maxResultCount = 10, string sorting = null, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            query = query.Where(x => x.FileName.Contains(fileName) || x.OriginalFileName.Contains(fileName));
            
            if (!string.IsNullOrEmpty(sorting))
            {
                query = query.OrderBy(sorting);
            }
            else
            {
                query = query.OrderByDescending(x => x.CreationTime);
            }

            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 根据存储类型获取文件列表
        /// </summary>
        public async Task<List<FileInfo>> GetListByStorageTypeAsync(StorageType storageType, int skipCount = 0, int maxResultCount = 10, string sorting = null, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            query = query.Where(x => x.StorageType == storageType);
            
            if (!string.IsNullOrEmpty(sorting))
            {
                query = query.OrderBy(sorting);
            }
            else
            {
                query = query.OrderByDescending(x => x.CreationTime);
            }

            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 根据文件状态获取文件列表
        /// </summary>
        public async Task<List<FileInfo>> GetListByStatusAsync(FileStatus status, int skipCount = 0, int maxResultCount = 10, string sorting = null, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            query = query.Where(x => x.Status == status);
            
            if (!string.IsNullOrEmpty(sorting))
            {
                query = query.OrderBy(sorting);
            }
            else
            {
                query = query.OrderByDescending(x => x.CreationTime);
            }

            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 获取过期文件列表
        /// </summary>
        public async Task<List<FileInfo>> GetExpiredFilesAsync(int skipCount = 0, int maxResultCount = 100, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            var now = DateTime.UtcNow;
            
            query = query.Where(x => x.ExpirationTime.HasValue && x.ExpirationTime.Value <= now);
            
            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 获取无引用的文件列表（引用计数为0的文件）
        /// </summary>
        public async Task<List<FileInfo>> GetUnreferencedFilesAsync(int skipCount = 0, int maxResultCount = 100, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            query = query.Where(x => x.ReferenceCount <= 0);
            
            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 获取文件统计信息
        /// </summary>
        public async Task<FileStatistics> GetStatisticsAsync(StorageType? storageType = null, FileStatus? status = null, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            if (storageType.HasValue)
            {
                query = query.Where(x => x.StorageType == storageType.Value);
            }
            
            if (status.HasValue)
            {
                query = query.Where(x => x.Status == status.Value);
            }

            var statistics = new FileStatistics
            {
                TotalCount = await query.CountAsync(cancellationToken),
                TotalSize = await query.SumAsync(x => x.FileSize, cancellationToken),
                ActiveCount = await query.CountAsync(x => x.Status == FileStatus.Active, cancellationToken),
                DeletedCount = await query.CountAsync(x => x.Status == FileStatus.Deleted, cancellationToken),
                ArchivedCount = await query.CountAsync(x => x.Status == FileStatus.Archived, cancellationToken),
                TotalAccessCount = await query.SumAsync(x => x.AccessCount, cancellationToken),
                TotalDownloadCount = await query.SumAsync(x => x.DownloadCount, cancellationToken)
            };

            if (statistics.TotalCount > 0)
            {
                statistics.AverageFileSize = (double)statistics.TotalSize / statistics.TotalCount;
                statistics.MaxFileSize = await query.MaxAsync(x => x.FileSize, cancellationToken);
                statistics.MinFileSize = await query.MinAsync(x => x.FileSize, cancellationToken);
            }

            return statistics;
        }

        /// <summary>
        /// 批量更新文件状态
        /// </summary>
        public async Task<int> BatchUpdateStatusAsync(List<Guid> fileIds, FileStatus status, CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            
            return await dbContext.Database.ExecuteSqlRawAsync(
                "UPDATE FileInfos SET Status = {0}, LastModificationTime = {1} WHERE Id IN ({2})",
                status,
                DateTime.UtcNow,
                string.Join(",", fileIds.Select(id => $"'{id}'")),
                cancellationToken);
        }

        /// <summary>
        /// 批量删除过期文件
        /// </summary>
        public async Task<int> DeleteExpiredFilesAsync(CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var now = DateTime.UtcNow;
            
            return await dbContext.Database.ExecuteSqlRawAsync(
                "DELETE FROM FileInfos WHERE ExpirationTime IS NOT NULL AND ExpirationTime <= {0}",
                now,
                cancellationToken);
        }

        /// <summary>
        /// 批量删除无引用文件
        /// </summary>
        public async Task<int> DeleteUnreferencedFilesAsync(CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            
            return await dbContext.Database.ExecuteSqlRawAsync(
                "DELETE FROM FileInfos WHERE ReferenceCount <= 0",
                cancellationToken);
        }

        /// <summary>
        /// 检查文件ID是否存在
        /// </summary>
        public async Task<bool> ExistsByFileIdAsync(string fileId, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            return await query.AnyAsync(x => x.FileId == fileId, cancellationToken);
        }

        /// <summary>
        /// 检查MD5哈希值是否存在
        /// </summary>
        public async Task<bool> ExistsByMD5HashAsync(string md5Hash, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            return await query.AnyAsync(x => x.MD5Hash == md5Hash, cancellationToken);
        }

        /// <summary>
        /// 获取文件总数
        /// </summary>
        public async Task<long> GetCountAsync(StorageType? storageType = null, FileStatus? status = null, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            if (storageType.HasValue)
            {
                query = query.Where(x => x.StorageType == storageType.Value);
            }
            
            if (status.HasValue)
            {
                query = query.Where(x => x.Status == status.Value);
            }

            return await query.CountAsync(cancellationToken);
        }

        /// <summary>
        /// 获取文件总大小
        /// </summary>
        public async Task<long> GetTotalSizeAsync(StorageType? storageType = null, FileStatus? status = null, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            
            if (storageType.HasValue)
            {
                query = query.Where(x => x.StorageType == storageType.Value);
            }
            
            if (status.HasValue)
            {
                query = query.Where(x => x.Status == status.Value);
            }

            return await query.SumAsync(x => x.FileSize, cancellationToken);
        }

        #region 高级查询方法 - REQ-009

        /// <summary>
        /// 高级搜索文件（支持多条件组合查询）
        /// </summary>
        public async Task<FileSearchResult> AdvancedSearchAsync(FileAdvancedSearchRequest searchRequest, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            var query = await GetQueryableAsync();

            // 应用搜索条件
            query = ApplySearchFilters(query, searchRequest);

            // 获取总数
            var totalCount = await query.CountAsync(cancellationToken);

            // 应用排序
            if (!string.IsNullOrEmpty(searchRequest.Sorting))
            {
                query = query.OrderBy(searchRequest.Sorting);
            }
            else
            {
                query = query.OrderByDescending(x => x.CreationTime);
            }

            // 应用分页
            var items = await query
                .Skip(searchRequest.SkipCount)
                .Take(searchRequest.MaxResultCount)
                .ToListAsync(cancellationToken);

            stopwatch.Stop();

            var result = new FileSearchResult(items, totalCount)
            {
                SearchTimeMs = stopwatch.ElapsedMilliseconds
            };

            // 计算统计信息
            if (totalCount > 0)
            {
                result.Statistics = await CalculateSearchStatistics(query, cancellationToken);
                result.Facets = await CalculateSearchFacets(query, cancellationToken);
            }

            return result;
        }

        /// <summary>
        /// 根据文件扩展名搜索文件
        /// </summary>
        public async Task<List<FileInfo>> SearchByExtensionAsync(string extension, int skipCount = 0, int maxResultCount = 10, string sorting = null, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();

            // 从文件名中提取扩展名进行匹配
            query = query.Where(x => x.FileName.EndsWith("." + extension) || x.OriginalFileName.EndsWith("." + extension));

            return await ApplySortingAndPaging(query, skipCount, maxResultCount, sorting, cancellationToken);
        }

        /// <summary>
        /// 根据MIME类型搜索文件
        /// </summary>
        public async Task<List<FileInfo>> SearchByMimeTypeAsync(string mimeType, int skipCount = 0, int maxResultCount = 10, string sorting = null, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();

            query = query.Where(x => x.MimeType == mimeType);

            return await ApplySortingAndPaging(query, skipCount, maxResultCount, sorting, cancellationToken);
        }

        /// <summary>
        /// 根据文件大小范围搜索文件
        /// </summary>
        public async Task<List<FileInfo>> SearchBySizeRangeAsync(long? minSize = null, long? maxSize = null, int skipCount = 0, int maxResultCount = 10, string sorting = null, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();

            if (minSize.HasValue)
            {
                query = query.Where(x => x.FileSize >= minSize.Value);
            }

            if (maxSize.HasValue)
            {
                query = query.Where(x => x.FileSize <= maxSize.Value);
            }

            return await ApplySortingAndPaging(query, skipCount, maxResultCount, sorting, cancellationToken);
        }

        /// <summary>
        /// 根据创建时间范围搜索文件
        /// </summary>
        public async Task<List<FileInfo>> SearchByDateRangeAsync(DateTime? startTime = null, DateTime? endTime = null, int skipCount = 0, int maxResultCount = 10, string sorting = null, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();

            if (startTime.HasValue)
            {
                query = query.Where(x => x.CreationTime >= startTime.Value);
            }

            if (endTime.HasValue)
            {
                query = query.Where(x => x.CreationTime <= endTime.Value);
            }

            return await ApplySortingAndPaging(query, skipCount, maxResultCount, sorting, cancellationToken);
        }

        /// <summary>
        /// 全文搜索（搜索文件名、原始文件名、描述等字段）
        /// </summary>
        public async Task<List<FileInfo>> FullTextSearchAsync(string keyword, int skipCount = 0, int maxResultCount = 10, string sorting = null, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();

            if (!string.IsNullOrWhiteSpace(keyword))
            {
                query = query.Where(x =>
                    x.FileName.Contains(keyword) ||
                    x.OriginalFileName.Contains(keyword) ||
                    (x.Description != null && x.Description.Contains(keyword)) ||
                    (x.Tags != null && x.Tags.Contains(keyword)));
            }

            return await ApplySortingAndPaging(query, skipCount, maxResultCount, sorting, cancellationToken);
        }

        /// <summary>
        /// 获取热门文件（按访问次数排序）
        /// </summary>
        public async Task<List<FileInfo>> GetPopularFilesAsync(int skipCount = 0, int maxResultCount = 10, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();

            query = query.Where(x => x.Status == FileStatus.Active)
                        .OrderByDescending(x => x.AccessCount)
                        .ThenByDescending(x => x.DownloadCount);

            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 获取最近上传的文件
        /// </summary>
        public async Task<List<FileInfo>> GetRecentFilesAsync(int skipCount = 0, int maxResultCount = 10, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();

            query = query.Where(x => x.Status == FileStatus.Active)
                        .OrderByDescending(x => x.CreationTime);

            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 应用搜索过滤条件
        /// </summary>
        private IQueryable<FileInfo> ApplySearchFilters(IQueryable<FileInfo> query, FileAdvancedSearchRequest request)
        {
            // 关键词搜索
            if (!string.IsNullOrWhiteSpace(request.Keyword))
            {
                query = query.Where(x =>
                    x.FileName.Contains(request.Keyword) ||
                    x.OriginalFileName.Contains(request.Keyword) ||
                    (x.Description != null && x.Description.Contains(request.Keyword)));
            }

            // 文件ID列表
            if (request.FileIds?.Count > 0)
            {
                query = query.Where(x => request.FileIds.Contains(x.FileId));
            }

            // MD5哈希值列表
            if (request.MD5Hashes?.Count > 0)
            {
                query = query.Where(x => request.MD5Hashes.Contains(x.MD5Hash));
            }

            // 文件扩展名
            if (request.Extensions?.Count > 0)
            {
                var extensionConditions = request.Extensions.Select(ext =>
                    $"x.FileName.EndsWith(\".{ext}\") || x.OriginalFileName.EndsWith(\".{ext}\")");
                var combinedCondition = string.Join(" || ", extensionConditions);
                query = query.Where(combinedCondition);
            }

            // MIME类型
            if (request.MimeTypes?.Count > 0)
            {
                query = query.Where(x => request.MimeTypes.Contains(x.MimeType));
            }

            // 存储类型
            if (request.StorageTypes?.Count > 0)
            {
                query = query.Where(x => request.StorageTypes.Contains(x.StorageType));
            }

            // 文件状态
            if (request.Statuses?.Count > 0)
            {
                query = query.Where(x => request.Statuses.Contains(x.Status));
            }
            else
            {
                // 默认不包含已删除文件
                if (!request.IncludeDeleted)
                {
                    query = query.Where(x => x.Status != FileStatus.Deleted);
                }

                // 默认包含已归档文件，除非明确排除
                if (!request.IncludeArchived)
                {
                    query = query.Where(x => x.Status != FileStatus.Archived);
                }
            }

            // 文件大小范围
            if (request.MinFileSize.HasValue)
            {
                query = query.Where(x => x.FileSize >= request.MinFileSize.Value);
            }
            if (request.MaxFileSize.HasValue)
            {
                query = query.Where(x => x.FileSize <= request.MaxFileSize.Value);
            }

            // 创建时间范围
            if (request.CreatedAfter.HasValue)
            {
                query = query.Where(x => x.CreationTime >= request.CreatedAfter.Value);
            }
            if (request.CreatedBefore.HasValue)
            {
                query = query.Where(x => x.CreationTime <= request.CreatedBefore.Value);
            }

            // 修改时间范围
            if (request.ModifiedAfter.HasValue)
            {
                query = query.Where(x => x.LastModificationTime >= request.ModifiedAfter.Value);
            }
            if (request.ModifiedBefore.HasValue)
            {
                query = query.Where(x => x.LastModificationTime <= request.ModifiedBefore.Value);
            }

            // 公开/私有文件
            if (request.IsPublic.HasValue)
            {
                query = query.Where(x => x.IsPublic == request.IsPublic.Value);
            }

            // 加密/非加密文件
            if (request.IsEncrypted.HasValue)
            {
                query = query.Where(x => x.IsEncrypted == request.IsEncrypted.Value);
            }

            // 访问次数范围
            if (request.MinAccessCount.HasValue)
            {
                query = query.Where(x => x.AccessCount >= request.MinAccessCount.Value);
            }
            if (request.MaxAccessCount.HasValue)
            {
                query = query.Where(x => x.AccessCount <= request.MaxAccessCount.Value);
            }

            // 下载次数范围
            if (request.MinDownloadCount.HasValue)
            {
                query = query.Where(x => x.DownloadCount >= request.MinDownloadCount.Value);
            }
            if (request.MaxDownloadCount.HasValue)
            {
                query = query.Where(x => x.DownloadCount <= request.MaxDownloadCount.Value);
            }

            // 标签搜索
            if (request.Tags?.Count > 0)
            {
                foreach (var tag in request.Tags)
                {
                    query = query.Where(x => x.Tags != null && x.Tags.Contains(tag));
                }
            }

            // 路径前缀过滤
            if (!string.IsNullOrWhiteSpace(request.PathPrefix))
            {
                query = query.Where(x => x.RelativePath.StartsWith(request.PathPrefix));
            }

            return query;
        }

        /// <summary>
        /// 应用排序和分页
        /// </summary>
        private async Task<List<FileInfo>> ApplySortingAndPaging(IQueryable<FileInfo> query, int skipCount, int maxResultCount, string sorting, CancellationToken cancellationToken)
        {
            if (!string.IsNullOrEmpty(sorting))
            {
                query = query.OrderBy(sorting);
            }
            else
            {
                query = query.OrderByDescending(x => x.CreationTime);
            }

            return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 计算搜索统计信息
        /// </summary>
        private async Task<FileSearchStatistics> CalculateSearchStatistics(IQueryable<FileInfo> query, CancellationToken cancellationToken)
        {
            var statistics = new FileSearchStatistics();

            if (await query.AnyAsync(cancellationToken))
            {
                statistics.MatchedCount = await query.CountAsync(cancellationToken);
                statistics.TotalSize = await query.SumAsync(x => x.FileSize, cancellationToken);
                statistics.AverageSize = (double)statistics.TotalSize / statistics.MatchedCount;
                statistics.MaxSize = await query.MaxAsync(x => x.FileSize, cancellationToken);
                statistics.MinSize = await query.MinAsync(x => x.FileSize, cancellationToken);
                statistics.TotalAccessCount = await query.SumAsync(x => x.AccessCount, cancellationToken);
                statistics.TotalDownloadCount = await query.SumAsync(x => x.DownloadCount, cancellationToken);
                statistics.EarliestCreated = await query.MinAsync(x => x.CreationTime, cancellationToken);
                statistics.LatestCreated = await query.MaxAsync(x => x.CreationTime, cancellationToken);
            }

            return statistics;
        }

        /// <summary>
        /// 计算搜索分面统计
        /// </summary>
        private async Task<FileSearchFacets> CalculateSearchFacets(IQueryable<FileInfo> query, CancellationToken cancellationToken)
        {
            var facets = new FileSearchFacets();

            // 按存储类型分组统计
            var storageTypeGroups = await query
                .GroupBy(x => x.StorageType)
                .Select(g => new { StorageType = g.Key, Count = g.Count() })
                .ToListAsync(cancellationToken);

            foreach (var group in storageTypeGroups)
            {
                facets.StorageTypes[group.StorageType] = group.Count;
            }

            // 按文件状态分组统计
            var statusGroups = await query
                .GroupBy(x => x.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync(cancellationToken);

            foreach (var group in statusGroups)
            {
                facets.Statuses[group.Status] = group.Count;
            }

            // 按文件扩展名分组统计（前10个）
            var extensionGroups = await query
                .Where(x => x.FileName.Contains("."))
                .Select(x => x.FileName.Substring(x.FileName.LastIndexOf(".") + 1).ToLower())
                .GroupBy(x => x)
                .Select(g => new { Extension = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count)
                .Take(10)
                .ToListAsync(cancellationToken);

            foreach (var group in extensionGroups)
            {
                facets.Extensions[group.Extension] = group.Count;
            }

            // 按MIME类型分组统计（前10个）
            var mimeTypeGroups = await query
                .Where(x => !string.IsNullOrEmpty(x.MimeType))
                .GroupBy(x => x.MimeType)
                .Select(g => new { MimeType = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count)
                .Take(10)
                .ToListAsync(cancellationToken);

            foreach (var group in mimeTypeGroups)
            {
                facets.MimeTypes[group.MimeType] = group.Count;
            }

            // 按文件大小范围分组统计
            var sizeRanges = new Dictionary<string, (long min, long max)>
            {
                ["< 1KB"] = (0, 1024),
                ["1KB - 10KB"] = (1024, 10 * 1024),
                ["10KB - 100KB"] = (10 * 1024, 100 * 1024),
                ["100KB - 1MB"] = (100 * 1024, 1024 * 1024),
                ["1MB - 10MB"] = (1024 * 1024, 10 * 1024 * 1024),
                ["10MB - 100MB"] = (10 * 1024 * 1024, 100 * 1024 * 1024),
                ["> 100MB"] = (100 * 1024 * 1024, long.MaxValue)
            };

            foreach (var range in sizeRanges)
            {
                var count = await query.CountAsync(x => x.FileSize >= range.Value.min && x.FileSize < range.Value.max, cancellationToken);
                if (count > 0)
                {
                    facets.SizeRanges[range.Key] = count;
                }
            }

            return facets;
        }

        #endregion
    }
}
