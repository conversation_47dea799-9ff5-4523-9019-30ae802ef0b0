using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件存储提供者工厂接口
    /// 负责创建和管理不同类型的存储提供者实例
    /// </summary>
    public interface IFileStorageProviderFactory
    {
        /// <summary>
        /// 创建存储提供者
        /// </summary>
        /// <param name="storageType">存储类型</param>
        /// <param name="configurationName">配置名称</param>
        /// <returns>存储提供者实例</returns>
        Task<IFileStorageProvider> CreateProviderAsync(StorageType storageType, string configurationName = null);

        /// <summary>
        /// 获取默认存储提供者
        /// </summary>
        /// <returns>默认存储提供者</returns>
        Task<IFileStorageProvider> GetDefaultProviderAsync();

        /// <summary>
        /// 获取指定配置的存储提供者
        /// </summary>
        /// <param name="configurationName">配置名称</param>
        /// <returns>存储提供者</returns>
        Task<IFileStorageProvider> GetProviderAsync(string configurationName);

        /// <summary>
        /// 获取所有可用的存储提供者
        /// </summary>
        /// <returns>存储提供者列表</returns>
        Task<IEnumerable<IFileStorageProvider>> GetAllProvidersAsync();

        /// <summary>
        /// 获取支持的存储类型
        /// </summary>
        /// <returns>支持的存储类型列表</returns>
        IEnumerable<StorageType> GetSupportedStorageTypes();

        /// <summary>
        /// 检查存储类型是否受支持
        /// </summary>
        /// <param name="storageType">存储类型</param>
        /// <returns>是否支持</returns>
        bool IsStorageTypeSupported(StorageType storageType);

        /// <summary>
        /// 注册存储提供者
        /// </summary>
        /// <param name="storageType">存储类型</param>
        /// <param name="providerFactory">提供者工厂函数</param>
        void RegisterProvider(StorageType storageType, Func<string, Task<IFileStorageProvider>> providerFactory);

        /// <summary>
        /// 注销存储提供者
        /// </summary>
        /// <param name="storageType">存储类型</param>
        void UnregisterProvider(StorageType storageType);

        /// <summary>
        /// 获取存储配置
        /// </summary>
        /// <param name="configurationName">配置名称</param>
        /// <returns>存储配置</returns>
        Task<StorageConfiguration> GetStorageConfigurationAsync(string configurationName);

        /// <summary>
        /// 获取所有存储配置
        /// </summary>
        /// <returns>存储配置列表</returns>
        Task<IEnumerable<StorageConfiguration>> GetAllStorageConfigurationsAsync();

        /// <summary>
        /// 验证存储配置
        /// </summary>
        /// <param name="configuration">存储配置</param>
        /// <returns>验证结果</returns>
        Task<ValidationResult> ValidateConfigurationAsync(StorageConfiguration configuration);

        /// <summary>
        /// 测试存储连接
        /// </summary>
        /// <param name="configuration">存储配置</param>
        /// <returns>测试结果</returns>
        Task<HealthCheckResult> TestConnectionAsync(StorageConfiguration configuration);
    }
}
