using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件信息仓储接口
    /// 提供文件元数据的数据访问抽象，支持多维度查询和文件快传功能
    /// </summary>
    public interface IFileInfoRepository : IRepository<FileInfo, Guid>
    {
        /// <summary>
        /// 根据文件ID查找文件信息
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<FileInfo> FindByFileIdAsync(
            string fileId, 
            bool includeDetails = true, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据MD5哈希值查找文件信息
        /// </summary>
        /// <param name="md5Hash">MD5哈希值</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<FileInfo> FindByMD5HashAsync(
            string md5Hash, 
            bool includeDetails = true, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据MD5哈希值和文件大小查找文件信息（用于文件快传）
        /// </summary>
        /// <param name="md5Hash">MD5哈希值</param>
        /// <param name="fileSize">文件大小</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<FileInfo> FindByMD5AndSizeAsync(
            string md5Hash, 
            long fileSize, 
            bool includeDetails = true, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据相对路径查找文件信息
        /// </summary>
        /// <param name="relativePath">相对路径</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<FileInfo> FindByRelativePathAsync(
            string relativePath, 
            bool includeDetails = true, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据文件名搜索文件信息（支持模糊查询）
        /// </summary>
        /// <param name="fileName">文件名（支持通配符）</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="sorting">排序字段</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileInfo>> SearchByFileNameAsync(
            string fileName,
            int skipCount = 0,
            int maxResultCount = 10,
            string sorting = null,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据存储类型获取文件列表
        /// </summary>
        /// <param name="storageType">存储类型</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="sorting">排序字段</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileInfo>> GetListByStorageTypeAsync(
            StorageType storageType,
            int skipCount = 0,
            int maxResultCount = 10,
            string sorting = null,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据文件状态获取文件列表
        /// </summary>
        /// <param name="status">文件状态</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="sorting">排序字段</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileInfo>> GetListByStatusAsync(
            FileStatus status,
            int skipCount = 0,
            int maxResultCount = 10,
            string sorting = null,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取过期文件列表
        /// </summary>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileInfo>> GetExpiredFilesAsync(
            int skipCount = 0,
            int maxResultCount = 100,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取无引用的文件列表（引用计数为0的文件）
        /// </summary>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileInfo>> GetUnreferencedFilesAsync(
            int skipCount = 0,
            int maxResultCount = 100,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取文件统计信息
        /// </summary>
        /// <param name="storageType">存储类型（可选）</param>
        /// <param name="status">文件状态（可选）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<FileStatistics> GetStatisticsAsync(
            StorageType? storageType = null,
            FileStatus? status = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量更新文件状态
        /// </summary>
        /// <param name="fileIds">文件ID列表</param>
        /// <param name="status">新状态</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<int> BatchUpdateStatusAsync(
            List<Guid> fileIds,
            FileStatus status,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量删除过期文件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>删除的文件数量</returns>
        Task<int> DeleteExpiredFilesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量删除无引用文件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>删除的文件数量</returns>
        Task<int> DeleteUnreferencedFilesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 检查文件ID是否存在
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<bool> ExistsByFileIdAsync(
            string fileId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 检查MD5哈希值是否存在
        /// </summary>
        /// <param name="md5Hash">MD5哈希值</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<bool> ExistsByMD5HashAsync(
            string md5Hash, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取文件总数
        /// </summary>
        /// <param name="storageType">存储类型（可选）</param>
        /// <param name="status">文件状态（可选）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<long> GetCountAsync(
            StorageType? storageType = null,
            FileStatus? status = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取文件总大小
        /// </summary>
        /// <param name="storageType">存储类型（可选）</param>
        /// <param name="status">文件状态（可选）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<long> GetTotalSizeAsync(
            StorageType? storageType = null,
            FileStatus? status = null,
            CancellationToken cancellationToken = default);

        #region 高级查询方法 - REQ-009

        /// <summary>
        /// 高级搜索文件（支持多条件组合查询）
        /// </summary>
        /// <param name="searchRequest">搜索请求参数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>分页查询结果</returns>
        Task<FileSearchResult> AdvancedSearchAsync(
            FileAdvancedSearchRequest searchRequest,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据文件扩展名搜索文件
        /// </summary>
        /// <param name="extension">文件扩展名（不包含点号）</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="sorting">排序字段</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileInfo>> SearchByExtensionAsync(
            string extension,
            int skipCount = 0,
            int maxResultCount = 10,
            string sorting = null,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据MIME类型搜索文件
        /// </summary>
        /// <param name="mimeType">MIME类型</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="sorting">排序字段</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileInfo>> SearchByMimeTypeAsync(
            string mimeType,
            int skipCount = 0,
            int maxResultCount = 10,
            string sorting = null,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据文件大小范围搜索文件
        /// </summary>
        /// <param name="minSize">最小文件大小（字节）</param>
        /// <param name="maxSize">最大文件大小（字节）</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="sorting">排序字段</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileInfo>> SearchBySizeRangeAsync(
            long? minSize = null,
            long? maxSize = null,
            int skipCount = 0,
            int maxResultCount = 10,
            string sorting = null,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据创建时间范围搜索文件
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="sorting">排序字段</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileInfo>> SearchByDateRangeAsync(
            DateTime? startTime = null,
            DateTime? endTime = null,
            int skipCount = 0,
            int maxResultCount = 10,
            string sorting = null,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 全文搜索（搜索文件名、原始文件名、描述等字段）
        /// </summary>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="sorting">排序字段</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileInfo>> FullTextSearchAsync(
            string keyword,
            int skipCount = 0,
            int maxResultCount = 10,
            string sorting = null,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取热门文件（按访问次数排序）
        /// </summary>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileInfo>> GetPopularFilesAsync(
            int skipCount = 0,
            int maxResultCount = 10,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取最近上传的文件
        /// </summary>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileInfo>> GetRecentFilesAsync(
            int skipCount = 0,
            int maxResultCount = 10,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        #endregion
    }

    /// <summary>
    /// 文件统计信息
    /// </summary>
    public class FileStatistics
    {
        /// <summary>
        /// 文件总数
        /// </summary>
        public long TotalCount { get; set; }

        /// <summary>
        /// 文件总大小（字节）
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// 活跃文件数量
        /// </summary>
        public long ActiveCount { get; set; }

        /// <summary>
        /// 已删除文件数量
        /// </summary>
        public long DeletedCount { get; set; }

        /// <summary>
        /// 已归档文件数量
        /// </summary>
        public long ArchivedCount { get; set; }

        /// <summary>
        /// 总访问次数
        /// </summary>
        public long TotalAccessCount { get; set; }

        /// <summary>
        /// 总下载次数
        /// </summary>
        public long TotalDownloadCount { get; set; }

        /// <summary>
        /// 平均文件大小
        /// </summary>
        public double AverageFileSize { get; set; }

        /// <summary>
        /// 最大文件大小
        /// </summary>
        public long MaxFileSize { get; set; }

        /// <summary>
        /// 最小文件大小
        /// </summary>
        public long MinFileSize { get; set; }
    }
}
