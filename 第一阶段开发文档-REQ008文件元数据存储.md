# 第一阶段开发文档 - REQ-008: 文件元数据存储到数据库

## 📋 文档信息
- **开发阶段**: 第一阶段 - 核心基础设施
- **需求编号**: REQ-008
- **需求名称**: 文件元数据存储到数据库
- **开发日期**: 2025-01-29
- **开发者**: AI Assistant
- **文档版本**: 1.0

---

## 🎯 需求概述

### 需求描述
实现文件信息的数据库存储功能，将文件元数据从纯文件系统管理转换为数据库管理，支持高效的文件查询、统计和管理功能。

### 技术目标
1. 设计完整的文件信息实体模型
2. 实现文件分享实体模型（为后续短链分享功能准备）
3. 创建高效的数据访问层
4. 配置数据库映射和索引优化
5. 支持多维度文件查询和统计

### 影响范围
- Domain层：新增文件存储相关实体和接口
- EntityFrameworkCore层：新增仓储实现和数据库配置
- 为后续API层和应用层的修改奠定基础

---

## 📝 详细修改记录

### 1. 创建FileInfo实体模型

**文件**: `src/2_Domain/FileStorage/FileInfo.cs`
**修改类型**: 新增
**代码行数**: 300+行

#### 核心功能设计
```csharp
public class FileInfo : FullAuditedAggregateRoot<Guid>, IMultiTenant
{
    // 业务主键
    public virtual string FileId { get; set; }  // 32位十六进制字符串
    
    // 文件基本信息
    public virtual string FileName { get; set; }
    public virtual string OriginalFileName { get; set; }
    public virtual string RelativePath { get; set; }
    public virtual string AbsolutePath { get; set; }
    public virtual long FileSize { get; set; }
    
    // 哈希值（用于文件快传和完整性校验）
    public virtual string MD5Hash { get; set; }
    public virtual string SHA256Hash { get; set; }
    
    // 存储相关
    public virtual StorageType StorageType { get; set; }
    public virtual string StorageConfig { get; set; }
    public virtual FileStatus Status { get; set; }
    
    // 引用计数（用于文件快传）
    public virtual int ReferenceCount { get; set; }
    
    // 访问统计
    public virtual long AccessCount { get; set; }
    public virtual long DownloadCount { get; set; }
    public virtual DateTime? LastAccessTime { get; set; }
    public virtual DateTime? LastDownloadTime { get; set; }
    
    // 扩展功能
    public virtual bool IsEncrypted { get; set; }
    public virtual bool IsPublic { get; set; }
    public virtual DateTime? ExpirationTime { get; set; }
    public virtual string Tags { get; set; }
    public virtual string ExtraProperties { get; set; }
}
```

#### 枚举定义
- **StorageType**: 支持Local、AliyunOSS、FTP、S3、MinIO、HuaweiOBS、TencentCOS
- **FileStatus**: 支持Active、Deleted、Archived、Uploading、Corrupted、Migrating

#### 业务方法
- `IncreaseReferenceCount()`: 增加引用计数
- `DecreaseReferenceCount()`: 减少引用计数
- `RecordAccess()`: 记录访问
- `RecordDownload()`: 记录下载
- `MarkAsDeleted()`: 标记删除
- `MarkAsArchived()`: 标记归档
- `IsExpired()`: 检查是否过期

### 2. 创建FileShare实体模型

**文件**: `src/2_Domain/FileStorage/FileShare.cs`
**修改类型**: 新增
**代码行数**: 300+行

#### 核心功能设计
```csharp
public class FileShare : FullAuditedAggregateRoot<Guid>, IMultiTenant
{
    // 关联文件
    public virtual Guid FileInfoId { get; set; }
    public virtual FileInfo FileInfo { get; set; }
    
    // 分享信息
    public virtual string ShareCode { get; set; }  // 8-12位短链码
    public virtual string Title { get; set; }
    public virtual string Description { get; set; }
    
    // 访问控制
    public virtual string AccessPassword { get; set; }
    public virtual ShareType ShareType { get; set; }
    public virtual ShareStatus Status { get; set; }
    
    // 限制设置
    public virtual DateTime? ExpirationTime { get; set; }
    public virtual int? MaxAccessCount { get; set; }
    public virtual int? MaxDownloadCount { get; set; }
    
    // 访问统计
    public virtual int AccessCount { get; set; }
    public virtual int DownloadCount { get; set; }
    public virtual DateTime? LastAccessTime { get; set; }
    public virtual string LastAccessIP { get; set; }
    
    // 权限设置
    public virtual bool AllowDownload { get; set; }
    public virtual bool AllowPreview { get; set; }
    public virtual bool RequireLogin { get; set; }
}
```

#### 枚举定义
- **ShareType**: Public、Password、Private、Temporary
- **ShareStatus**: Active、Disabled、Expired、Deleted

#### 业务方法
- `RecordAccess()`: 记录访问
- `RecordDownload()`: 记录下载
- `CanAccess()`: 检查访问权限
- `CanDownload()`: 检查下载权限
- `IsExpired()`: 检查是否过期
- `ValidatePassword()`: 验证访问密码

### 3. 创建仓储接口

#### IFileInfoRepository接口
**文件**: `src/2_Domain/FileStorage/IFileInfoRepository.cs`
**修改类型**: 新增

**核心方法**:
- `FindByFileIdAsync()`: 根据文件ID查找
- `FindByMD5HashAsync()`: 根据MD5查找
- `FindByMD5AndSizeAsync()`: 根据MD5+大小查找（文件快传）
- `SearchByFileNameAsync()`: 文件名模糊搜索
- `GetListByStorageTypeAsync()`: 按存储类型查询
- `GetListByStatusAsync()`: 按状态查询
- `GetExpiredFilesAsync()`: 获取过期文件
- `GetUnreferencedFilesAsync()`: 获取无引用文件
- `GetStatisticsAsync()`: 获取统计信息
- `BatchUpdateStatusAsync()`: 批量更新状态
- `DeleteExpiredFilesAsync()`: 批量删除过期文件

#### IFileShareRepository接口
**文件**: `src/2_Domain/FileStorage/IFileShareRepository.cs`
**修改类型**: 新增

**核心方法**:
- `FindByShareCodeAsync()`: 根据分享码查找
- `GetListByFileInfoIdAsync()`: 根据文件ID获取分享列表
- `GetExpiredSharesAsync()`: 获取过期分享
- `GetPopularSharesAsync()`: 获取热门分享
- `SearchAsync()`: 分享搜索
- `GetStatisticsAsync()`: 获取分享统计

### 4. 实现EF Core仓储类

#### EfCoreFileInfoRepository
**文件**: `src/4_EntityFrameworkCore/FileStorage/EfCoreFileInfoRepository.cs`
**修改类型**: 新增

**实现特点**:
- 继承自`EfCoreRepository<FileStorageCenterDbContext, FileInfo, Guid>`
- 实现所有IFileInfoRepository接口方法
- 支持动态排序和分页查询
- 优化的批量操作（使用原生SQL）
- 完整的统计信息计算

#### EfCoreFileShareRepository
**文件**: `src/4_EntityFrameworkCore/FileStorage/EfCoreFileShareRepository.cs`
**修改类型**: 新增

**实现特点**:
- 继承自`EfCoreRepository<FileStorageCenterDbContext, FileShare, Guid>`
- 支持Include导航属性查询
- 实现复杂的分享查询逻辑
- 支持热门分享和最近访问查询

### 5. 配置数据库映射和索引

**文件**: `src/4_EntityFrameworkCore/EntityFrameworkCore/FileStorageCenterDbContextModelCreatingExtensions.cs`
**修改类型**: 修改

#### FileInfo实体配置
```csharp
builder.Entity<FileInfo>(b =>
{
    b.ToTable(FileStorageCenterConsts.DbTablePrefix + "FileInfos", FileStorageCenterConsts.DbSchema);
    
    // 属性配置
    b.Property(x => x.FileId).IsRequired().HasMaxLength(32);
    b.Property(x => x.FileName).IsRequired().HasMaxLength(255);
    // ... 其他属性配置
    
    // 索引配置
    b.HasIndex(x => x.FileId).IsUnique(); // 文件ID唯一索引
    b.HasIndex(x => x.MD5Hash); // MD5哈希索引
    b.HasIndex(x => new { x.MD5Hash, x.FileSize }); // 复合索引（文件快传）
    b.HasIndex(x => x.RelativePath); // 路径索引
    b.HasIndex(x => x.StorageType); // 存储类型索引
    b.HasIndex(x => x.Status); // 状态索引
    b.HasIndex(x => x.ExpirationTime); // 过期时间索引
    b.HasIndex(x => x.ReferenceCount); // 引用计数索引
});
```

#### FileShare实体配置
```csharp
builder.Entity<FileShare>(b =>
{
    b.ToTable(FileStorageCenterConsts.DbTablePrefix + "FileShares", FileStorageCenterConsts.DbSchema);
    
    // 外键关系
    b.HasOne(x => x.FileInfo)
        .WithMany()
        .HasForeignKey(x => x.FileInfoId)
        .OnDelete(DeleteBehavior.Cascade);
    
    // 索引配置
    b.HasIndex(x => x.ShareCode).IsUnique(); // 分享码唯一索引
    b.HasIndex(x => x.FileInfoId); // 文件ID索引
    b.HasIndex(x => x.AccessCount); // 访问次数索引（热门查询）
    b.HasIndex(x => x.ExpirationTime); // 过期时间索引
});
```

### 6. 更新DbContext注册实体

**文件**: `src/4_EntityFrameworkCore/EntityFrameworkCore/FileStorageCenterDbContext.cs`
**修改类型**: 修改

#### 添加DbSet属性
```csharp
/// <summary>
/// 文件信息实体集
/// </summary>
public DbSet<FileInfo> FileInfos { get; set; }

/// <summary>
/// 文件分享实体集
/// </summary>
public DbSet<FileShare> FileShares { get; set; }
```

### 7. 创建辅助工具类

#### 文件ID生成器
**文件**: `src/2_Domain/FileStorage/IFileIdGenerator.cs` 和 `FileIdGenerator.cs`
**修改类型**: 新增

**功能**:
- 基于文件内容生成32位十六进制文件ID
- 生成8-12位分享码
- MD5和SHA256哈希计算
- 格式验证功能

#### 跨平台路径处理工具
**文件**: `src/2_Domain/FileStorage/ICrossPlatformPathHelper.cs` 和 `CrossPlatformPathHelper.cs`
**修改类型**: 新增

**功能**:
- 路径标准化（统一使用正斜杠）
- 平台特定路径转换
- 路径组合和相对路径计算
- 文件名安全处理
- 跨平台兼容性检查

---

## 🧪 测试验证

### 编译验证
- [x] 所有新增文件编译通过
- [x] 无语法错误和类型错误
- [x] 依赖注入配置正确

### 功能验证计划
- [ ] 实体模型单元测试
- [ ] 仓储接口单元测试
- [ ] 数据库映射测试
- [ ] 索引性能测试
- [ ] 跨平台路径处理测试

### 数据库验证
- [ ] 数据库迁移文件生成
- [ ] 表结构创建验证
- [ ] 索引创建验证
- [ ] 外键约束验证

---

## ⚠️ 风险评估

### 风险等级: 中等

### 潜在影响
1. **数据库性能**: 新增的索引可能影响写入性能
2. **存储空间**: 元数据存储会增加数据库存储需求
3. **兼容性**: 现有文件系统操作需要适配新的数据库模型

### 缓解措施
1. **性能优化**: 合理设计索引，避免过度索引
2. **分批迁移**: 现有文件数据可分批迁移到数据库
3. **向后兼容**: 保持现有API接口不变，内部逐步切换

### 回滚方案
1. 删除新增的数据库表
2. 移除新增的实体类和仓储
3. 恢复原有的文件系统操作逻辑

---

## 📊 开发统计

### 新增文件统计
- **实体类**: 2个 (FileInfo, FileShare)
- **接口**: 4个 (IFileInfoRepository, IFileShareRepository, IFileIdGenerator, ICrossPlatformPathHelper)
- **实现类**: 4个 (EfCoreFileInfoRepository, EfCoreFileShareRepository, FileIdGenerator, CrossPlatformPathHelper)
- **总代码行数**: 约1500行

### 修改文件统计
- **配置文件**: 2个 (DbContextModelCreatingExtensions, DbContext)
- **修改行数**: 约100行

---

## 🔄 后续计划

### 下一步工作
1. **REQ-007**: 文件ID访问优化 - 修改现有API支持文件ID访问
2. **REQ-006**: 跨平台路径处理 - 集成路径处理工具到现有服务
3. **数据库迁移**: 创建和执行数据库迁移
4. **单元测试**: 为新增功能编写完整的单元测试

### 长期规划
1. 现有文件数据迁移到数据库
2. API层适配新的数据模型
3. 性能监控和优化
4. 文档更新和培训

---

## 📝 备注

1. 本次开发完成了文件元数据存储的核心基础设施
2. 所有实体设计都考虑了多租户支持
3. 索引设计优化了常用查询场景
4. 为后续的文件快传、短链分享等功能预留了扩展空间
5. 跨平台路径处理为Linux/Windows混合部署提供了支持

**开发完成时间**: 2025-01-29
**下一阶段预计开始时间**: 2025-01-29
