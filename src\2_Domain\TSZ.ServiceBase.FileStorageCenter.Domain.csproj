﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <RootNamespace>TSZ.ServiceBase.FileStorageCenter</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Emailing"  />
    <PackageReference Include="Volo.Abp.Identity.Domain"  />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Identity"  />
    <PackageReference Include="Volo.Abp.IdentityServer.Domain"  />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.IdentityServer"  />
    <PackageReference Include="Volo.Abp.BackgroundJobs.Domain"  />
    <PackageReference Include="Volo.Abp.AuditLogging.Domain"  />
    <PackageReference Include="Volo.Abp.TenantManagement.Domain"  />
    <PackageReference Include="Volo.Abp.FeatureManagement.Domain"  />
    <PackageReference Include="Volo.Abp.SettingManagement.Domain"  />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\2_Domain.Shared\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj" />
  </ItemGroup>

</Project>
