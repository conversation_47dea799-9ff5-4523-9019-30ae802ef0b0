using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage.Dto
{
    /// <summary>
    /// 文件快传检查请求DTO
    /// </summary>
    public class FileQuickUploadRequestDto
    {
        /// <summary>
        /// 文件MD5哈希值
        /// </summary>
        [Required]
        [StringLength(32)]
        public string MD5Hash { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        [Required]
        [StringLength(255)]
        public string FileName { get; set; }
    }

    /// <summary>
    /// 文件快传检查响应DTO
    /// </summary>
    public class FileQuickUploadResponseDto
    {
        /// <summary>
        /// 是否可以快传
        /// </summary>
        public bool CanQuickUpload { get; set; }

        /// <summary>
        /// 已存在的文件ID（如果可以快传）
        /// </summary>
        public string ExistingFileId { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 文件分片上传请求DTO
    /// </summary>
    public class FileChunkUploadRequestDto
    {
        /// <summary>
        /// 文件名
        /// </summary>
        [Required]
        [StringLength(255)]
        public string FileName { get; set; }

        /// <summary>
        /// 分片索引（从0开始）
        /// </summary>
        public int ChunkIndex { get; set; }

        /// <summary>
        /// 分片总数
        /// </summary>
        public int TotalChunks { get; set; }

        /// <summary>
        /// 分片大小
        /// </summary>
        public long ChunkSize { get; set; }

        /// <summary>
        /// 文件总大小
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// 上传会话ID（用于关联分片）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string UploadSessionId { get; set; }

        /// <summary>
        /// 分片MD5哈希值
        /// </summary>
        [StringLength(32)]
        public string ChunkMD5 { get; set; }

        /// <summary>
        /// 目标目录
        /// </summary>
        [StringLength(1000)]
        public string TargetDirectory { get; set; }
    }

    /// <summary>
    /// 文件分片上传响应DTO
    /// </summary>
    public class FileChunkUploadResponseDto
    {
        /// <summary>
        /// 上传会话ID
        /// </summary>
        public string UploadSessionId { get; set; }

        /// <summary>
        /// 分片索引
        /// </summary>
        public int ChunkIndex { get; set; }

        /// <summary>
        /// 是否上传成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 已上传的分片数量
        /// </summary>
        public int UploadedChunks { get; set; }

        /// <summary>
        /// 总分片数量
        /// </summary>
        public int TotalChunks { get; set; }

        /// <summary>
        /// 上传进度百分比
        /// </summary>
        public double Progress { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 文件合并请求DTO
    /// </summary>
    public class FileMergeRequestDto
    {
        /// <summary>
        /// 上传会话ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string UploadSessionId { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        [Required]
        [StringLength(255)]
        public string FileName { get; set; }

        /// <summary>
        /// 文件总大小
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// 文件MD5哈希值（用于验证）
        /// </summary>
        [StringLength(32)]
        public string FileMD5 { get; set; }

        /// <summary>
        /// 是否删除分片文件
        /// </summary>
        public bool DeleteChunks { get; set; } = true;
    }

    /// <summary>
    /// 文件合并响应DTO
    /// </summary>
    public class FileMergeResponseDto
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// MD5哈希值
        /// </summary>
        public string MD5Hash { get; set; }

        /// <summary>
        /// 合并是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 文件分片下载请求DTO
    /// </summary>
    public class FileChunkDownloadRequestDto
    {
        /// <summary>
        /// 分片索引（从0开始）
        /// </summary>
        public int ChunkIndex { get; set; }

        /// <summary>
        /// 分片大小（字节）
        /// </summary>
        public long ChunkSize { get; set; } = 1024 * 1024; // 默认1MB

        /// <summary>
        /// 是否记录下载统计
        /// </summary>
        public bool RecordDownload { get; set; } = true;
    }

    /// <summary>
    /// 文件分片下载响应DTO
    /// </summary>
    public class FileChunkDownloadResponseDto
    {
        /// <summary>
        /// 分片内容
        /// </summary>
        public byte[] Content { get; set; }

        /// <summary>
        /// 分片索引
        /// </summary>
        public int ChunkIndex { get; set; }

        /// <summary>
        /// 分片大小
        /// </summary>
        public long ChunkSize { get; set; }

        /// <summary>
        /// 文件总大小
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// 是否为最后一个分片
        /// </summary>
        public bool IsLastChunk { get; set; }

        /// <summary>
        /// 分片MD5哈希值
        /// </summary>
        public string ChunkMD5 { get; set; }
    }

    /// <summary>
    /// 文件下载URL请求DTO
    /// </summary>
    public class FileDownloadUrlRequestDto
    {
        /// <summary>
        /// URL有效期（秒）
        /// </summary>
        public int ExpiresInSeconds { get; set; } = 3600; // 默认1小时

        /// <summary>
        /// 自定义下载文件名
        /// </summary>
        [StringLength(255)]
        public string DownloadFileName { get; set; }

        /// <summary>
        /// 是否内联显示
        /// </summary>
        public bool Inline { get; set; } = false;

        /// <summary>
        /// 访问IP限制
        /// </summary>
        [StringLength(45)]
        public string AllowedIP { get; set; }
    }

    /// <summary>
    /// 文件下载URL响应DTO
    /// </summary>
    public class FileDownloadUrlResponseDto
    {
        /// <summary>
        /// 下载URL
        /// </summary>
        public string DownloadUrl { get; set; }

        /// <summary>
        /// URL过期时间
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// 访问令牌
        /// </summary>
        public string AccessToken { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }
    }
}
