using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TSZ.ServiceBase.FileStorageCenter.FileStorage.Dto;
using Volo.Abp.Application.Services;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件管理应用服务接口
    /// 提供基于文件ID的现代化文件操作API，同时保持向后兼容性
    /// </summary>
    public interface IFileManagementAppService : IApplicationService
    {
        #region 文件上传

        /// <summary>
        /// 上传文件（基于文件ID）
        /// </summary>
        /// <param name="request">上传请求</param>
        /// <returns>上传响应，包含文件ID</returns>
        Task<FileUploadResponseDto> UploadFileAsync(FileUploadRequestDto request);

        /// <summary>
        /// 分片上传文件
        /// </summary>
        /// <param name="request">分片上传请求</param>
        /// <returns>分片上传响应</returns>
        Task<FileChunkUploadResponseDto> UploadFileChunkAsync(FileChunkUploadRequestDto request);

        /// <summary>
        /// 合并分片文件
        /// </summary>
        /// <param name="request">合并请求</param>
        /// <returns>合并响应，包含最终文件ID</returns>
        Task<FileMergeResponseDto> MergeFileChunksAsync(FileMergeRequestDto request);

        /// <summary>
        /// 检查文件是否存在（基于MD5快传）
        /// </summary>
        /// <param name="request">快传检查请求</param>
        /// <returns>快传检查响应</returns>
        Task<FileQuickUploadResponseDto> CheckQuickUploadAsync(FileQuickUploadRequestDto request);

        #endregion

        #region 文件下载

        /// <summary>
        /// 下载文件（基于文件ID）
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="request">下载请求参数</param>
        /// <returns>文件下载响应</returns>
        Task<FileDownloadResponseDto> DownloadFileAsync(string fileId, FileDownloadRequestDto request = null);

        /// <summary>
        /// 分片下载文件
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="request">分片下载请求</param>
        /// <returns>分片下载响应</returns>
        Task<FileChunkDownloadResponseDto> DownloadFileChunkAsync(string fileId, FileChunkDownloadRequestDto request);

        /// <summary>
        /// 获取文件下载URL（用于直接下载）
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="request">URL生成请求</param>
        /// <returns>下载URL响应</returns>
        Task<FileDownloadUrlResponseDto> GetDownloadUrlAsync(string fileId, FileDownloadUrlRequestDto request = null);

        #endregion

        #region 文件信息

        /// <summary>
        /// 获取文件信息（基于文件ID）
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>文件信息</returns>
        Task<FileInfoDto> GetFileInfoAsync(string fileId);

        /// <summary>
        /// 批量获取文件信息
        /// </summary>
        /// <param name="fileIds">文件ID列表</param>
        /// <returns>文件信息列表</returns>
        Task<List<FileInfoDto>> GetFileInfoListAsync(List<string> fileIds);

        /// <summary>
        /// 搜索文件
        /// </summary>
        /// <param name="request">搜索请求</param>
        /// <returns>搜索结果</returns>
        Task<FileSearchResponseDto> SearchFilesAsync(FileSearchRequestDto request);

        /// <summary>
        /// 获取文件统计信息
        /// </summary>
        /// <param name="request">统计请求</param>
        /// <returns>统计信息</returns>
        Task<FileStatisticsDto> GetStatisticsAsync(FileStatisticsRequestDto request = null);

        #endregion

        #region 文件操作

        /// <summary>
        /// 复制文件（基于文件ID）
        /// </summary>
        /// <param name="request">复制请求</param>
        /// <returns>复制响应，包含新文件ID</returns>
        Task<FileCopyResponseDto> CopyFileAsync(FileCopyRequestDto request);

        /// <summary>
        /// 移动文件（基于文件ID）
        /// </summary>
        /// <param name="request">移动请求</param>
        /// <returns>移动响应</returns>
        Task<FileMoveResponseDto> MoveFileAsync(FileMoveRequestDto request);

        /// <summary>
        /// 删除文件（基于文件ID）
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="request">删除请求参数</param>
        /// <returns>删除响应</returns>
        Task<FileDeleteResponseDto> DeleteFileAsync(string fileId, FileDeleteRequestDto request = null);

        /// <summary>
        /// 批量删除文件
        /// </summary>
        /// <param name="request">批量删除请求</param>
        /// <returns>批量删除响应</returns>
        Task<FileBatchDeleteResponseDto> BatchDeleteFilesAsync(FileBatchDeleteRequestDto request);

        /// <summary>
        /// 重命名文件
        /// </summary>
        /// <param name="request">重命名请求</param>
        /// <returns>重命名响应</returns>
        Task<FileRenameResponseDto> RenameFileAsync(FileRenameRequestDto request);

        #endregion

        #region 兼容性方法

        /// <summary>
        /// 根据文件路径获取文件ID（兼容性方法）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件ID</returns>
        Task<string> GetFileIdByPathAsync(string filePath);

        /// <summary>
        /// 根据文件ID获取文件路径（兼容性方法）
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>文件路径</returns>
        Task<string> GetFilePathByIdAsync(string fileId);

        /// <summary>
        /// 批量路径转文件ID
        /// </summary>
        /// <param name="filePaths">文件路径列表</param>
        /// <returns>路径和文件ID的映射</returns>
        Task<Dictionary<string, string>> BatchGetFileIdsByPathsAsync(List<string> filePaths);

        /// <summary>
        /// 批量文件ID转路径
        /// </summary>
        /// <param name="fileIds">文件ID列表</param>
        /// <returns>文件ID和路径的映射</returns>
        Task<Dictionary<string, string>> BatchGetPathsByFileIdsAsync(List<string> fileIds);

        #endregion

        #region 文件验证

        /// <summary>
        /// 验证文件完整性
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>验证结果</returns>
        Task<FileValidationResponseDto> ValidateFileIntegrityAsync(string fileId);

        /// <summary>
        /// 重新计算文件哈希值
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>重新计算的哈希值</returns>
        Task<FileHashResponseDto> RecalculateFileHashAsync(string fileId);

        #endregion

        #region 文件预览

        /// <summary>
        /// 获取文件预览信息
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="request">预览请求</param>
        /// <returns>预览信息</returns>
        Task<FilePreviewResponseDto> GetFilePreviewAsync(string fileId, FilePreviewRequestDto request = null);

        /// <summary>
        /// 生成文件缩略图
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="request">缩略图请求</param>
        /// <returns>缩略图响应</returns>
        Task<FileThumbnailResponseDto> GenerateThumbnailAsync(string fileId, FileThumbnailRequestDto request);

        #endregion
    }
}
