using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件分享仓储接口
    /// 提供文件分享数据的访问抽象，支持短链分享和访问控制功能
    /// </summary>
    public interface IFileShareRepository : IRepository<FileShare, Guid>
    {
        /// <summary>
        /// 根据分享码查找分享信息
        /// </summary>
        /// <param name="shareCode">分享码</param>
        /// <param name="includeDetails">是否包含详细信息（包含文件信息）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<FileShare> FindByShareCodeAsync(
            string shareCode, 
            bool includeDetails = true, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据文件信息ID获取分享列表
        /// </summary>
        /// <param name="fileInfoId">文件信息ID</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="sorting">排序字段</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileShare>> GetListByFileInfoIdAsync(
            Guid fileInfoId,
            int skipCount = 0,
            int maxResultCount = 10,
            string sorting = null,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据分享状态获取分享列表
        /// </summary>
        /// <param name="status">分享状态</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="sorting">排序字段</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileShare>> GetListByStatusAsync(
            ShareStatus status,
            int skipCount = 0,
            int maxResultCount = 10,
            string sorting = null,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据分享类型获取分享列表
        /// </summary>
        /// <param name="shareType">分享类型</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="sorting">排序字段</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileShare>> GetListByShareTypeAsync(
            ShareType shareType,
            int skipCount = 0,
            int maxResultCount = 10,
            string sorting = null,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取过期的分享列表
        /// </summary>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileShare>> GetExpiredSharesAsync(
            int skipCount = 0,
            int maxResultCount = 100,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取访问次数超限的分享列表
        /// </summary>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileShare>> GetAccessLimitExceededSharesAsync(
            int skipCount = 0,
            int maxResultCount = 100,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取分享统计信息
        /// </summary>
        /// <param name="shareType">分享类型（可选）</param>
        /// <param name="status">分享状态（可选）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<ShareStatistics> GetStatisticsAsync(
            ShareType? shareType = null,
            ShareStatus? status = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量更新分享状态
        /// </summary>
        /// <param name="shareIds">分享ID列表</param>
        /// <param name="status">新状态</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<int> BatchUpdateStatusAsync(
            List<Guid> shareIds,
            ShareStatus status,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量删除过期分享
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>删除的分享数量</returns>
        Task<int> DeleteExpiredSharesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 检查分享码是否存在
        /// </summary>
        /// <param name="shareCode">分享码</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<bool> ExistsByShareCodeAsync(
            string shareCode, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取分享总数
        /// </summary>
        /// <param name="shareType">分享类型（可选）</param>
        /// <param name="status">分享状态（可选）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<long> GetCountAsync(
            ShareType? shareType = null,
            ShareStatus? status = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取最近访问的分享列表
        /// </summary>
        /// <param name="days">最近天数</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileShare>> GetRecentlyAccessedSharesAsync(
            int days = 7,
            int skipCount = 0,
            int maxResultCount = 10,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取热门分享列表（按访问次数排序）
        /// </summary>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileShare>> GetPopularSharesAsync(
            int skipCount = 0,
            int maxResultCount = 10,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据创建者获取分享列表
        /// </summary>
        /// <param name="creatorId">创建者ID</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="sorting">排序字段</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileShare>> GetListByCreatorAsync(
            Guid creatorId,
            int skipCount = 0,
            int maxResultCount = 10,
            string sorting = null,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 搜索分享（根据标题和描述）
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <param name="skipCount">跳过数量</param>
        /// <param name="maxResultCount">最大结果数量</param>
        /// <param name="sorting">排序字段</param>
        /// <param name="includeDetails">是否包含详细信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task<List<FileShare>> SearchAsync(
            string keyword,
            int skipCount = 0,
            int maxResultCount = 10,
            string sorting = null,
            bool includeDetails = false,
            CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 分享统计信息
    /// </summary>
    public class ShareStatistics
    {
        /// <summary>
        /// 分享总数
        /// </summary>
        public long TotalCount { get; set; }

        /// <summary>
        /// 活跃分享数量
        /// </summary>
        public long ActiveCount { get; set; }

        /// <summary>
        /// 已禁用分享数量
        /// </summary>
        public long DisabledCount { get; set; }

        /// <summary>
        /// 已过期分享数量
        /// </summary>
        public long ExpiredCount { get; set; }

        /// <summary>
        /// 总访问次数
        /// </summary>
        public long TotalAccessCount { get; set; }

        /// <summary>
        /// 总下载次数
        /// </summary>
        public long TotalDownloadCount { get; set; }

        /// <summary>
        /// 平均访问次数
        /// </summary>
        public double AverageAccessCount { get; set; }

        /// <summary>
        /// 最受欢迎的分享ID
        /// </summary>
        public Guid? MostPopularShareId { get; set; }

        /// <summary>
        /// 最受欢迎的分享访问次数
        /// </summary>
        public int MostPopularAccessCount { get; set; }
    }
}
