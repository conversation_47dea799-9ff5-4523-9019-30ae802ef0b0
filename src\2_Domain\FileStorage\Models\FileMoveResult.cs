namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件移动结果
    /// </summary>
    public class FileMoveResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 源文件路径
        /// </summary>
        public string SourceFilePath { get; set; }

        /// <summary>
        /// 源路径（兼容性属性）
        /// </summary>
        public string SourcePath
        {
            get => SourceFilePath;
            set => SourceFilePath = value;
        }

        /// <summary>
        /// 目标文件路径
        /// </summary>
        public string DestinationFilePath { get; set; }

        /// <summary>
        /// 目标路径（兼容性属性）
        /// </summary>
        public string DestinationPath
        {
            get => DestinationFilePath;
            set => DestinationFilePath = value;
        }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}
