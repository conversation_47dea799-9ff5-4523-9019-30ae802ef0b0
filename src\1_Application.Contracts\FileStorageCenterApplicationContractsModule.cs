﻿using Volo.Abp.Account;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.ObjectExtending;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.TenantManagement;

namespace TSZ.ServiceBase.FileStorageCenter
{
    /// <summary>
    /// 
    /// </summary>
    [DependsOn(
        typeof(FileStorageCenterDomainSharedModule)
        //typeof(AbpAccountApplicationContractsModule),
        //typeof(AbpFeatureManagementApplicationContractsModule),
        //typeof(AbpIdentityApplicationContractsModule),
        //typeof(AbpPermissionManagementApplicationContractsModule),
        //typeof(AbpSettingManagementApplicationContractsModule),
        //typeof(AbpTenantManagementApplicationContractsModule),
        //typeof(AbpObjectExtendingModule)
    )]
    public class FileStorageCenterApplicationContractsModule : AbpModule
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        public override void PreConfigureServices(ServiceConfigurationContext context)
        {
            FileStorageCenterDtoExtensions.Configure();
        }
    }
}
