using System.IO;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件下载结果
    /// </summary>
    public class FileDownloadResult
    {
        /// <summary>
        /// 文件流
        /// </summary>
        public Stream Stream { get; set; }

        /// <summary>
        /// 文件流（别名，保持兼容性）
        /// </summary>
        public Stream FileStream
        {
            get => Stream;
            set => Stream = value;
        }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 内容类型
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long? ContentLength { get; set; }

        /// <summary>
        /// 文件大小（别名，保持兼容性）
        /// </summary>
        public long FileSize
        {
            get => ContentLength ?? 0;
            set => ContentLength = value;
        }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}
