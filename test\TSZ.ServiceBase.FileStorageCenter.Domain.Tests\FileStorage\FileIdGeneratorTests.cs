using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Shouldly;
using TSZ.ServiceBase.FileStorageCenter.FileStorage;
using Xunit;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件ID生成器测试
    /// </summary>
    public class FileIdGeneratorTests
    {
        private readonly FileIdGenerator _fileIdGenerator;

        public FileIdGeneratorTests()
        {
            _fileIdGenerator = new FileIdGenerator();
        }

        [Fact]
        public async Task GenerateFileIdAsync_Should_Return_32_Character_Hex_String()
        {
            // Arrange
            var fileName = "test.txt";
            var filePath = "/uploads/test.txt";
            var fileSize = 1024L;
            var md5Hash = "d41d8cd98f00b204e9800998ecf8427e";

            // Act
            var fileId = await _fileIdGenerator.GenerateFileIdAsync(fileName, filePath, fileSize, md5Hash);

            // Assert
            fileId.ShouldNotBeNull();
            fileId.Length.ShouldBe(32);
            
            // 验证是否为有效的十六进制字符串
            foreach (char c in fileId)
            {
                char.IsDigit(c).ShouldBeTrue(c >= '0' && c <= '9' || c >= 'a' && c <= 'f' || c >= 'A' && c <= 'F');
            }
        }

        [Fact]
        public async Task GenerateFileIdAsync_Should_Return_Different_Ids_For_Different_Files()
        {
            // Arrange
            var fileName1 = "test1.txt";
            var filePath1 = "/uploads/test1.txt";
            var fileName2 = "test2.txt";
            var filePath2 = "/uploads/test2.txt";
            var fileSize = 1024L;
            var md5Hash1 = "d41d8cd98f00b204e9800998ecf8427e";
            var md5Hash2 = "098f6bcd4621d373cade4e832627b4f6";

            // Act
            var fileId1 = await _fileIdGenerator.GenerateFileIdAsync(fileName1, filePath1, fileSize, md5Hash1);
            var fileId2 = await _fileIdGenerator.GenerateFileIdAsync(fileName2, filePath2, fileSize, md5Hash2);

            // Assert
            fileId1.ShouldNotBe(fileId2);
        }

        [Fact]
        public async Task GenerateFileIdAsync_Should_Return_Same_Id_For_Same_Parameters()
        {
            // Arrange
            var fileName = "test.txt";
            var filePath = "/uploads/test.txt";
            var fileSize = 1024L;
            var md5Hash = "d41d8cd98f00b204e9800998ecf8427e";

            // Act
            var fileId1 = await _fileIdGenerator.GenerateFileIdAsync(fileName, filePath, fileSize, md5Hash);
            var fileId2 = await _fileIdGenerator.GenerateFileIdAsync(fileName, filePath, fileSize, md5Hash);

            // Assert
            fileId1.ShouldBe(fileId2);
        }

        [Fact]
        public async Task GenerateFileIdAsync_Should_Handle_Null_MD5Hash()
        {
            // Arrange
            var fileName = "test.txt";
            var filePath = "/uploads/test.txt";
            var fileSize = 1024L;
            string md5Hash = null;

            // Act
            var fileId = await _fileIdGenerator.GenerateFileIdAsync(fileName, filePath, fileSize, md5Hash);

            // Assert
            fileId.ShouldNotBeNull();
            fileId.Length.ShouldBe(32);
        }

        [Fact]
        public async Task GenerateFileIdAsync_Should_Handle_Empty_MD5Hash()
        {
            // Arrange
            var fileName = "test.txt";
            var filePath = "/uploads/test.txt";
            var fileSize = 1024L;
            var md5Hash = string.Empty;

            // Act
            var fileId = await _fileIdGenerator.GenerateFileIdAsync(fileName, filePath, fileSize, md5Hash);

            // Assert
            fileId.ShouldNotBeNull();
            fileId.Length.ShouldBe(32);
        }

        [Fact]
        public async Task GenerateFileIdAsync_Should_Be_Deterministic_With_Same_Input()
        {
            // Arrange
            var fileName = "document.pdf";
            var filePath = "/documents/important/document.pdf";
            var fileSize = 2048576L; // 2MB
            var md5Hash = "5d41402abc4b2a76b9719d911017c592";

            // Act - 生成多次
            var ids = new List<string>();
            for (int i = 0; i < 10; i++)
            {
                var fileId = await _fileIdGenerator.GenerateFileIdAsync(fileName, filePath, fileSize, md5Hash);
                ids.Add(fileId);
            }

            // Assert - 所有ID应该相同
            for (int i = 1; i < ids.Count; i++)
            {
                ids[i].ShouldBe(ids[0], $"第{i+1}次生成的ID与第1次不同");
            }
        }

        [Theory]
        [InlineData("", "/path/file.txt", 1024L, "hash")]
        [InlineData("file.txt", "", 1024L, "hash")]
        [InlineData("file.txt", "/path/file.txt", 0L, "hash")]
        public async Task GenerateFileIdAsync_Should_Handle_Edge_Cases(string fileName, string filePath, long fileSize, string md5Hash)
        {
            // Act
            var fileId = await _fileIdGenerator.GenerateFileIdAsync(fileName, filePath, fileSize, md5Hash);

            // Assert
            fileId.ShouldNotBeNull();
            fileId.Length.ShouldBe(32);
        }

        [Fact]
        public async Task GenerateFileIdAsync_Should_Handle_Unicode_Filenames()
        {
            // Arrange
            var fileName = "测试文件.txt";
            var filePath = "/上传/测试文件.txt";
            var fileSize = 1024L;
            var md5Hash = "d41d8cd98f00b204e9800998ecf8427e";

            // Act
            var fileId = await _fileIdGenerator.GenerateFileIdAsync(fileName, filePath, fileSize, md5Hash);

            // Assert
            fileId.ShouldNotBeNull();
            fileId.Length.ShouldBe(32);
        }

        [Fact]
        public async Task GenerateFileIdAsync_Should_Handle_Very_Long_Paths()
        {
            // Arrange
            var fileName = "file.txt";
            var longPath = "/" + new string('a', 1000) + "/file.txt";
            var fileSize = 1024L;
            var md5Hash = "d41d8cd98f00b204e9800998ecf8427e";

            // Act
            var fileId = await _fileIdGenerator.GenerateFileIdAsync(fileName, longPath, fileSize, md5Hash);

            // Assert
            fileId.ShouldNotBeNull();
            fileId.Length.ShouldBe(32);
        }
    }
}
