{
  //"apollo": {
  //  "AppId": "TSZ.MicroservicePlat",
  //  "RefreshInterval": 36000000,
  //  "MetaServer": "http://localhost:8080/",
  //  "Secret": "75f1b8e36c594a4bbf7d7e50ffe31f67",
  //  "Namespaces": [ "TSZ_BaseCommon.json", "Base_FileStorageCenter.json" ],
  //  "Env": "Pro",
  //  "Meta": {
  //    "PRO": "http://localhost:8080/"
  //  }
  //}
  "NacosConfig": {
    "Listeners": [
      {
        "Optional": false,
        "DataId": "Base_FileStorageCenter",
        "Group": "DEFAULT_GROUP"
      },
      {
        "Optional": false,
        "DataId": "TSZ_BaseCommon",
        "Group": "DEFAULT_GROUP"
      }
    ],
    "Namespace": "Production",
    "ServerAddresses": [ "http://*************:8848/" ],
    "UserName": "nacos",
    "Password": "nacos",
    "ListenerInterval": 3600000, //毫秒，客户端多久检查一次配置是否有更新
    "ServiceName": "tsz-base-filestoragecenter"

  }
}