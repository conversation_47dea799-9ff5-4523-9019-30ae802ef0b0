﻿using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TSZ.ServiceBase.FileStorageCenter.Data
{
    /* This is used if database provider does't define
     * IFileStorageCenterDbSchemaMigrator implementation.
     */
    public class NullFileStorageCenterDbSchemaMigrator : IFileStorageCenterDbSchemaMigrator, ITransientDependency
    {
        public Task MigrateAsync()
        {
            return Task.CompletedTask;
        }
    }
}