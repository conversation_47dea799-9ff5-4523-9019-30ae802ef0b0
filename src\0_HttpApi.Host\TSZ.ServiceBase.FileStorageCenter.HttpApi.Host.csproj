﻿<Project Sdk="Microsoft.NET.Sdk.Web">
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<RootNamespace>TSZ.ServiceBase.FileStorageCenter</RootNamespace>
		<PreserveCompilationReferences>true</PreserveCompilationReferences>
		<UserSecretsId>TSZ.ServiceBase.FileStorageCenter-4681b4fd-151f-4221-84a4-929d86723e4c</UserSecretsId>
		<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
	</PropertyGroup>
	<ItemGroup>
		<RuntimeHostConfigurationOption Include="System.Globalization.AppLocalIcu" Value="********" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.ICU.ICU4C.Runtime"  />
		<PackageReference Include="FluentFTP"  />		
		<PackageReference Include="SkyAPM.Agent.AspNetCore"  />
		<PackageReference Include="Serilog.AspNetCore"  />
		<PackageReference Include="Serilog.Sinks.Async"  />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer"  />
		<PackageReference Include="Microsoft.AspNetCore.DataProtection.StackExchangeRedis"  />
		<PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy"  />
		<PackageReference Include="Volo.Abp.Autofac"  />
		<PackageReference Include="Volo.Abp.Caching.StackExchangeRedis"  />
		<PackageReference Include="Volo.Abp.AspNetCore.Serilog"  />
		<PackageReference Include="Volo.Abp.Swashbuckle"  />
		
		<PackageReference Include="TSZ.Common.Core"  />
		<PackageReference Include="TSZ.Common.Core.Helper"  />
		<PackageReference Include="TSZ.Common.OfficeExtends"  />
		<PackageReference Include="TSZ.Abp.Modulies"  />
		<PackageReference Include="TSZ.Infrastructures.Consul"  />
		<PackageReference Include="Volo.Abp.Caching.CSRedis"  />
		<PackageReference Include="TSZ.ServiceProxy.Caching"  />
		<PackageReference Include="TSZ.ServiceProxy.Config"  />
		<PackageReference Include="TSZ.ServiceProxy.SeriLog"  />
		
	</ItemGroup>

	<ItemGroup>
		<Compile Remove="Logs\**" />
		<Content Remove="Logs\**" />
		<EmbeddedResource Remove="Logs\**" />
		<None Remove="Logs\**" />
	</ItemGroup>

	<ItemGroup>
		<Content Remove="skyapm.json" />
		<Content Remove="wwwroot\js\app.js" />
		<Content Remove="wwwroot\js\app.min.js" />
		<Content Remove="wwwroot\js\jquery.js" />
		<Content Remove="wwwroot\js\jquery.min.js" />
		<Content Remove="wwwroot\js\jquery.min.map" />
		<Content Remove="wwwroot\js\swaggerdoc.js" />
		<Content Remove="wwwroot\SwaggerDoc.cshtml" />
	</ItemGroup>

	<ItemGroup>
		<_WebToolingArtifacts Remove="Properties\PublishProfiles\Mjy.PublishRelease-LinuxSelf.pubxml" />
		<_WebToolingArtifacts Remove="Properties\PublishProfiles\Mjy.PublishRelease.Win.pubxml" />
	</ItemGroup>

	<ItemGroup>
		<None Include="wwwroot\js\app.js" />
		<None Include="wwwroot\js\app.min.js" />
		<None Include="wwwroot\js\jquery.js" />
		<None Include="wwwroot\js\jquery.min.js" />
		<None Include="wwwroot\js\jquery.min.map" />
		<None Include="wwwroot\js\swaggerdoc.js" />
		<None Include="wwwroot\SwaggerDoc.cshtml" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\0_HttpApi\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj" />
		<ProjectReference Include="..\1_Application\TSZ.ServiceBase.FileStorageCenter.Application.csproj" />
		<ProjectReference Include="..\3_EntityFrameworkCore.DbMigrations\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.DbMigrations.csproj" />
	</ItemGroup>

</Project>
