﻿using System.IO;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore
{
    /* This class is needed for EF Core console commands
     * (like Add-Migration and Update-Database commands) */
    public class FileStorageCenterMigrationsDbContextFactory : IDesignTimeDbContextFactory<FileStorageCenterMigrationsDbContext>
    {
        public FileStorageCenterMigrationsDbContext CreateDbContext(string[] args)
        {
            FileStorageCenterEfCoreEntityExtensionMappings.Configure();

            var configuration = BuildConfiguration();

            var builder = new DbContextOptionsBuilder<FileStorageCenterMigrationsDbContext>()
                .UseMySql(configuration.GetConnectionString("Default"), MySqlServerVersion.LatestSupportedServerVersion);

            return new FileStorageCenterMigrationsDbContext(builder.Options, configuration);
        }

        private static IConfigurationRoot BuildConfiguration()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Path.Combine(Directory.GetCurrentDirectory(), "../TSZ.ServiceBase.FileStorageCenter.DbMigrator/"))
                .AddJsonFile("appsettings.json", optional: false);

            return builder.Build();
        }
    }
}
