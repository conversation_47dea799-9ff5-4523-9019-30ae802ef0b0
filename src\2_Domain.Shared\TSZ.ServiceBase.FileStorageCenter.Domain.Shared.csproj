<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
	  <TargetFramework>net9.0</TargetFramework>
	  <RootNamespace>TSZ.ServiceBase.FileStorageCenter.Domain.Shared</RootNamespace>
	  <PackageId>TSZ.ServiceBase.FileStorageCenter.Domain.Shared</PackageId>
	  <IncludeBuildOutput>true</IncludeBuildOutput>
	  <Version>1.0.0</Version>
	  <Authors>探索者软件</Authors>
	  <Company>探索者软件</Company>
	  <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
	  <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
	  <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
	  <GeneratePackageDependencyFile>true</GeneratePackageDependencyFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Identity.Domain.Shared"  />
    <PackageReference Include="Volo.Abp.IdentityServer.Domain.Shared"  />
    <PackageReference Include="Volo.Abp.BackgroundJobs.Domain.Shared"  />
    <PackageReference Include="Volo.Abp.AuditLogging.Domain.Shared"  />
    <PackageReference Include="Volo.Abp.TenantManagement.Domain.Shared"  />
    <PackageReference Include="Volo.Abp.FeatureManagement.Domain.Shared"  />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Shared"  />
    <PackageReference Include="Volo.Abp.SettingManagement.Domain.Shared"  />
    <PackageReference Include="Volo.Abp.Validation"  />
    <PackageReference Include="Volo.Abp.ObjectExtending"  />
    <PackageReference Include="Volo.Abp.Localization"  />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Localization\FileStorageCenter\*.json" />
    <Content Remove="Localization\FileStorageCenter\*.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded"  />
  </ItemGroup>

</Project>
