using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore;
using TSZ.ServiceBase.FileStorageCenter.MultiTenancy;
using StackExchange.Redis;
using Microsoft.OpenApi.Models;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Autofac;
using Volo.Abp.Caching;
using Volo.Abp.Caching.StackExchangeRedis;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.Swashbuckle;
using Volo.Abp.VirtualFileSystem;
using TSZ.Common.Core.Helper;
using Volo.Abp.AspNetCore.Mvc.Conventions;
using TSZ.Common.Core.Helper.AbpExtentions;
using Microsoft.AspNetCore.Http;
using TSZ.Infrastructures.Consul;
using TSZ.Abp.Modulies;
using Volo.Abp.Caching.CSRedis;
using Volo.Abp.Json;

namespace TSZ.ServiceBase.FileStorageCenter
{
    [DependsOn(
        typeof(FileStorageCenterHttpApiModule),
        typeof(AbpAutofacModule),
        //typeof(AbpCachingCSRedisModule),
        //typeof(AbpCachingStackExchangeRedisModule),
        //typeof(AbpAspNetCoreMvcUiMultiTenancyModule),
        typeof(NacosModule),
        typeof(FileStorageCenterApplicationModule),
        typeof(FileStorageCenterEntityFrameworkCoreDbMigrationsModule),
        typeof(AbpAspNetCoreSerilogModule),
        typeof(AbpSwashbuckleModule)
    )]
    public class FileStorageCenterHttpApiHostModule : BaseTszAbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            //调用通用父类的服务配置方法（必须调用）
            base.ConfigureServices(context);

            //设置接收文件长度的最大值。
            context.Services.Configure<Microsoft.AspNetCore.Http.Features.FormOptions>(x =>
            {
                x.ValueLengthLimit = int.MaxValue;
                x.MultipartBodyLengthLimit = int.MaxValue;
                x.MultipartHeadersLengthLimit = int.MaxValue;
            });

            var configuration = context.Services.GetConfiguration();
            //路由配置
            TszConfigureConventionalControllers(context, typeof(FileStorageCenterApplicationModule).Assembly, rootPath: "base/filestoragecenter");

            #region update by lijie 2022-09-13
            ////授权配置ID4
            ////TszConfigureAuthenticationID4(context, authServerPrefix: "ServiceBase_FileStorageCenter");

            //授权配置JWT
            TszConfigureAuthenticationJWT(context, authServerPrefix: "ServiceBase_FileStorageCenter");

            #endregion

            //配置Swagger
            TszConfigureSwaggerServices(context,
                "TSZ.ServiceBase.FileStorageCenter.Application.xml",
                "TSZ.ServiceBase.FileStorageCenter.Application.Contracts.xml");

            //配置客户端代理，基类会配置客户端代理的授权配置TszConfigureAuthenticationIdentityClients
            //TszConfigureHttpClientProxies(context,
            //    PlatMicroserviceClientProxyFlags.Base_LogCenter);
        }


        /// <summary>
        /// 去掉Redis分布式缓存（此微服务不需要）
        /// </summary>
        /// <param name="context"></param>
        protected override void TszConfigDistributedCache(ServiceConfigurationContext context)
        {

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            base.OnApplicationInitialization(context);
        }

        /*/// <summary>
        /// 给机会配置ConsulOptions，空方法，修改ConsulOptions.ServiceName，允许自定义在服务注册发现里的服务名称
        /// </summary>
        /// <param name="consulOptions"></param>
        /// <param name="isRenameServiceName"></param>
        protected override void OnConfigConsulOptions(TSZ.Infrastructures.Consul.ConsulOptions consulOptions, bool isRenameServiceName)
        {
            if (isRenameServiceName && consulOptions != null)
            {
                consulOptions.ServiceName = "tsz-base-filestoragecenter";
            }

            base.OnConfigConsulOptions(consulOptions, isRenameServiceName);
        }*/
    }
}
