﻿/*****************************************************************************
*Copyright (c) 2021, 北京探索者软件股份有限公司
*All rights reserved.       
*文件名称: FileResultDto
*文件描述: 待填写描述
*创建者: 李杰
*创建日期: 2021/7/27 13:41:21  
*版本号：1.0.0.0 
*个人审查：待填写姓名-时间
*组长审查：待填写姓名-时间
*******************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorageCenter
{
    /// <summary>
    /// Vue文件上传返回结果
    /// </summary>
    public class FileResultDto
    {
        ///// <summary>
        ///// 
        ///// </summary>
        //public int Code { get; set; }

        /// <summary>
        /// 是否需要合并
        /// </summary>
        public bool NeedMerge { get; set; }
        ///// <summary>
        /////// 
        /////// </summary>
        //public bool SkipUpload { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Message { get; set; }
        /// <summary>
        /// 扩展名
        /// </summary>
        public string Extension { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<int> Uploaded { get; set; }
        /// <summary>
        /// 服务器存贮路径ID
        /// </summary>
        public string ServerId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MD5 { get; set; }
        /// <summary>
        /// 文件名
        /// </summary>

        public string FileName { get; set; }

        /// <summary>
        /// 文件别名
        /// </summary>
        public string Alias { get; set; }
        /// <summary>
        /// 总分片
        /// </summary>
        public string TotalSize { get; set; }
        /// <summary>
        /// 文件全路径 
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 子目录
        /// </summary>
        public string BucketName { get; set; }
    }
}
