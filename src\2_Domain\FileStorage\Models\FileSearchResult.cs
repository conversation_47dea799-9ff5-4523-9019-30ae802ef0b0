using System;
using System.Collections.Generic;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件搜索结果
    /// 包含分页信息和搜索统计的文件查询结果
    /// </summary>
    public class FileSearchResult
    {
        /// <summary>
        /// 文件列表
        /// </summary>
        public List<FileInfo> Items { get; set; }

        /// <summary>
        /// 总记录数
        /// </summary>
        public long TotalCount { get; set; }

        /// <summary>
        /// 搜索耗时（毫秒）
        /// </summary>
        public long SearchTimeMs { get; set; }

        /// <summary>
        /// 搜索统计信息
        /// </summary>
        public FileSearchStatistics Statistics { get; set; }

        /// <summary>
        /// 搜索建议（当结果较少时提供）
        /// </summary>
        public List<string> Suggestions { get; set; }

        /// <summary>
        /// 分面统计（用于搜索结果的分类统计）
        /// </summary>
        public FileSearchFacets Facets { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public FileSearchResult()
        {
            Items = new List<FileInfo>();
            Statistics = new FileSearchStatistics();
            Suggestions = new List<string>();
            Facets = new FileSearchFacets();
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="items">文件列表</param>
        /// <param name="totalCount">总记录数</param>
        public FileSearchResult(List<FileInfo> items, long totalCount)
        {
            Items = items ?? new List<FileInfo>();
            TotalCount = totalCount;
            Statistics = new FileSearchStatistics();
            Suggestions = new List<string>();
            Facets = new FileSearchFacets();
        }
    }

    /// <summary>
    /// 文件搜索统计信息
    /// </summary>
    public class FileSearchStatistics
    {
        /// <summary>
        /// 匹配的文件总数
        /// </summary>
        public long MatchedCount { get; set; }

        /// <summary>
        /// 匹配文件的总大小（字节）
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// 平均文件大小（字节）
        /// </summary>
        public double AverageSize { get; set; }

        /// <summary>
        /// 最大文件大小（字节）
        /// </summary>
        public long MaxSize { get; set; }

        /// <summary>
        /// 最小文件大小（字节）
        /// </summary>
        public long MinSize { get; set; }

        /// <summary>
        /// 总访问次数
        /// </summary>
        public long TotalAccessCount { get; set; }

        /// <summary>
        /// 总下载次数
        /// </summary>
        public long TotalDownloadCount { get; set; }

        /// <summary>
        /// 最早创建时间
        /// </summary>
        public DateTime? EarliestCreated { get; set; }

        /// <summary>
        /// 最晚创建时间
        /// </summary>
        public DateTime? LatestCreated { get; set; }
    }

    /// <summary>
    /// 文件搜索分面统计
    /// 用于提供搜索结果的分类统计信息，帮助用户进一步筛选
    /// </summary>
    public class FileSearchFacets
    {
        /// <summary>
        /// 按存储类型分组统计
        /// </summary>
        public Dictionary<StorageType, long> StorageTypes { get; set; }

        /// <summary>
        /// 按文件状态分组统计
        /// </summary>
        public Dictionary<FileStatus, long> Statuses { get; set; }

        /// <summary>
        /// 按文件扩展名分组统计（前10个）
        /// </summary>
        public Dictionary<string, long> Extensions { get; set; }

        /// <summary>
        /// 按MIME类型分组统计（前10个）
        /// </summary>
        public Dictionary<string, long> MimeTypes { get; set; }

        /// <summary>
        /// 按文件大小范围分组统计
        /// </summary>
        public Dictionary<string, long> SizeRanges { get; set; }

        /// <summary>
        /// 按创建时间范围分组统计
        /// </summary>
        public Dictionary<string, long> DateRanges { get; set; }

        /// <summary>
        /// 按标签分组统计（前20个）
        /// </summary>
        public Dictionary<string, long> Tags { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public FileSearchFacets()
        {
            StorageTypes = new Dictionary<StorageType, long>();
            Statuses = new Dictionary<FileStatus, long>();
            Extensions = new Dictionary<string, long>();
            MimeTypes = new Dictionary<string, long>();
            SizeRanges = new Dictionary<string, long>();
            DateRanges = new Dictionary<string, long>();
            Tags = new Dictionary<string, long>();
        }
    }

    /// <summary>
    /// 文件搜索排序选项
    /// </summary>
    public static class FileSearchSorting
    {
        /// <summary>
        /// 按相关性排序（默认）
        /// </summary>
        public const string Relevance = "relevance";

        /// <summary>
        /// 按创建时间降序
        /// </summary>
        public const string CreationTimeDesc = "creationTime desc";

        /// <summary>
        /// 按创建时间升序
        /// </summary>
        public const string CreationTimeAsc = "creationTime asc";

        /// <summary>
        /// 按修改时间降序
        /// </summary>
        public const string LastModificationTimeDesc = "lastModificationTime desc";

        /// <summary>
        /// 按修改时间升序
        /// </summary>
        public const string LastModificationTimeAsc = "lastModificationTime asc";

        /// <summary>
        /// 按文件大小降序
        /// </summary>
        public const string FileSizeDesc = "fileSize desc";

        /// <summary>
        /// 按文件大小升序
        /// </summary>
        public const string FileSizeAsc = "fileSize asc";

        /// <summary>
        /// 按文件名升序
        /// </summary>
        public const string FileNameAsc = "fileName asc";

        /// <summary>
        /// 按文件名降序
        /// </summary>
        public const string FileNameDesc = "fileName desc";

        /// <summary>
        /// 按访问次数降序
        /// </summary>
        public const string AccessCountDesc = "accessCount desc";

        /// <summary>
        /// 按下载次数降序
        /// </summary>
        public const string DownloadCountDesc = "downloadCount desc";

        /// <summary>
        /// 获取所有可用的排序选项
        /// </summary>
        /// <returns>排序选项列表</returns>
        public static List<string> GetAvailableOptions()
        {
            return new List<string>
            {
                Relevance,
                CreationTimeDesc,
                CreationTimeAsc,
                LastModificationTimeDesc,
                LastModificationTimeAsc,
                FileSizeDesc,
                FileSizeAsc,
                FileNameAsc,
                FileNameDesc,
                AccessCountDesc,
                DownloadCountDesc
            };
        }

        /// <summary>
        /// 验证排序字符串是否有效
        /// </summary>
        /// <param name="sorting">排序字符串</param>
        /// <returns>是否有效</returns>
        public static bool IsValid(string sorting)
        {
            if (string.IsNullOrWhiteSpace(sorting))
                return true;

            var availableOptions = GetAvailableOptions();
            var sortingParts = sorting.Split(',');

            foreach (var part in sortingParts)
            {
                var trimmedPart = part.Trim();
                if (!availableOptions.Contains(trimmedPart))
                {
                    // 检查是否是自定义格式：字段名 [asc|desc]
                    var words = trimmedPart.Split(' ');
                    if (words.Length == 2)
                    {
                        var direction = words[1].ToLower();
                        if (direction != "asc" && direction != "desc")
                            return false;
                    }
                    else if (words.Length > 2)
                    {
                        return false;
                    }
                }
            }

            return true;
        }
    }
}
