using System.Collections.Generic;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息列表
        /// </summary>
        public List<string> ErrorMessages { get; set; } = new List<string>();

        /// <summary>
        /// 警告消息列表
        /// </summary>
        public List<string> WarningMessages { get; set; } = new List<string>();

        /// <summary>
        /// 添加错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        public void AddError(string message)
        {
            ErrorMessages.Add(message);
            IsValid = false;
        }

        /// <summary>
        /// 添加警告消息
        /// </summary>
        /// <param name="message">警告消息</param>
        public void AddWarning(string message)
        {
            WarningMessages.Add(message);
        }

        /// <summary>
        /// 创建成功的验证结果
        /// </summary>
        /// <returns>成功的验证结果</returns>
        public static ValidationResult Success()
        {
            return new ValidationResult { IsValid = true };
        }

        /// <summary>
        /// 创建失败的验证结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>失败的验证结果</returns>
        public static ValidationResult Failure(string errorMessage)
        {
            var result = new ValidationResult();
            result.AddError(errorMessage);
            return result;
        }
    }
}
