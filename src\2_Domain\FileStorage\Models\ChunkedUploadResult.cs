namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 分片上传结果
    /// </summary>
    public class ChunkedUploadResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 上传ID
        /// </summary>
        public string UploadId { get; set; }

        /// <summary>
        /// 分片号
        /// </summary>
        public int ChunkNumber { get; set; }

        /// <summary>
        /// 分片ETag
        /// </summary>
        public string ETag { get; set; }

        /// <summary>
        /// 分片ETag（别名，保持兼容性）
        /// </summary>
        public string ChunkETag
        {
            get => ETag;
            set => ETag = value;
        }

        /// <summary>
        /// 是否已完成
        /// </summary>
        public bool IsCompleted { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 是否需要合并
        /// </summary>
        public bool NeedMerge { get; set; }
    }
}
