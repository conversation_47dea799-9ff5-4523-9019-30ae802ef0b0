﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using TSZ.Common.Core.Helper;
using TSZ.Common.Services;
using TSZ.Common.Shared;
using Volo.Abp.Application.Services;

namespace TSZ.ServiceBase.FileStorageCenter
{
    /// <summary>
    /// 文件同步服务
    /// </summary>
    public class FileSynchronizerAppService : ApplicationService, IFileSynchronizerAppService
    {
        private const int MAX_RETRY_NUM = 5; //最大重试次数
        private readonly IConfiguration _configuration;
        private readonly bool _isLinuxPlatform;
        private readonly string _rootPath;
        private readonly string _fileStorageCenter;
        private readonly bool _isSameServer; //是否同一服务器
        private readonly IHttpClientFactory _httpClientFactory;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="configuration"></param>
        /// <param name="httpClientFactory"></param>
        public FileSynchronizerAppService(IConfiguration configuration, IHttpClientFactory httpClientFactory)
        {
            _configuration = configuration;
            _httpClientFactory = httpClientFactory;
            _isLinuxPlatform = FileStorageCenterAppService.IsLinuxPlatform;
            //模型发布服务的ngix配置应修改为通用名称：tszappdatasstatic，暂未修改
            _rootPath = _isLinuxPlatform ? "/tszappdatas" : configuration["TSZ_BaseCommon:WindowsPlatform:TszAppDatas"];

            _fileStorageCenter = _configuration["ServiceBase_FileStorageCenter:DesignStorageCenter"];
            _isSameServer = string.IsNullOrEmpty(_fileStorageCenter);
        }

        /// <summary>
        /// 从相同架构文件服务同步下载文件
        /// </summary>
        /// <param name="strFullFileName"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        public async Task<ServiceResult<string>> DownloadAsync(string strFullFileName, string tenantId)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            string strNewFileName = GetNewFileName(strFullFileName, tenantId);
            if (_isSameServer)
            {
                File.Copy(strFullFileName, strNewFileName, true);
            }
            else
            {
                var fileSize = await GetFileSize(strFullFileName);
                if (fileSize < 0) return ServiceResult.Failed<string>("获取文件大小出错");
                long position = 0;
                var fs = new FileStream(strNewFileName, FileMode.Create, FileAccess.Write);
                while (position < fileSize)
                {
                    var readSize = Math.Min(fileSize - position, FileServiceConfig.MAXFILESIZE);
                    var bytes = await DownloadFileAsync(strFullFileName, position, (int)readSize);
                    if (bytes == null) return ServiceResult.Failed<string>($"下载文件出错，当前位置：{position}");
                    fs.Write(bytes);
                    position += readSize;
                }
                fs.Close();
            }
            return ServiceResult.Success(strNewFileName.Replace(_rootPath, "").TrimStart(Path.DirectorySeparatorChar), strNewFileName);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="strFullFileName"></param>
        /// <param name="position"></param>
        /// <param name="readSize"></param>
        /// <returns></returns>
        private Task<byte[]> DownloadFileAsync(string strFullFileName, long position, int readSize)
        {
            var bytes = FileOperateProxy.DownloadFileHelperPosition(strFullFileName, position, readSize, false);
            int retry = 0;
            while (bytes == null)
            {
                bytes = FileOperateProxy.DownloadFileHelperPosition(strFullFileName, position, readSize, false);
                retry++;
                if (retry >= MAX_RETRY_NUM) break;
            }
            return Task.FromResult(bytes);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="strFullFileName"></param>
        /// <returns></returns>

        private async Task<long> GetFileSize(string strFullFileName)
        {
            string url = $"{_fileStorageCenter}/apis/base/filestoragecenter/file-storage-center/read/get-file-length?strFullFileName={strFullFileName}";
            var httpClient = _httpClientFactory.CreateClient();
            {
                using (var response = await httpClient.GetAsync(url))
                {
                    if (response.IsSuccessStatusCode)
                    {
                        var json = await response.Content.ReadAsStringAsync();
                        var result = JsonHelper.ParseFromJson<ServiceResult<long>>(json);
                        return result.Data;
                    }
                    return -1;
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="strFullFileName"></param>
        /// <returns></returns>
        private string GetAvalidatedFileName(string strFullFileName)
        {
            if (_isLinuxPlatform)
            {
                if (strFullFileName.Contains(@"\"))
                {
                    strFullFileName = strFullFileName.Replace(@"\", @"/");
                }
            }
            if (!strFullFileName.ToLower().StartsWith(_rootPath.ToLower()))
            {
                strFullFileName = PubMethod.Combine(_rootPath, strFullFileName);
            }
            return strFullFileName;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="strFullFileName"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        private string GetNewFileName(string strFullFileName, string tenantId)
        {
            if (string.IsNullOrWhiteSpace(tenantId))
            {
                tenantId = (CurrentTenant?.Id ?? Guid.Empty).ToString();
            }
            var targetPath = PubMethod.Combine(_rootPath, tenantId);
            if (!Directory.Exists(targetPath))
            {
                Directory.CreateDirectory(targetPath);
            }
            var ext = Path.GetExtension(strFullFileName);
            return PubMethod.Combine(targetPath, $"{PubMethod.CreateGuidKey()}{ext}");
        }
    }
}