﻿/*****************************************************************************
*Copyright (c) 2021, 北京探索者软件股份有限公司
*All rights reserved.       
*文件名称: AliyunOSS
*文件描述: STS (获取临时访问凭证)
*创建者: 李杰
*创建日期: 2023/3/6 14:30:16  
*版本号：1.0.0.0 
*个人审查：待填写姓名-时间
*组长审查：待填写姓名-时间
*******************************************************************************/
using AlibabaCloud.SDK.Sts20150401;
using AlibabaCloud.SDK.Sts20150401.Models;
using AlibabaCloud.TeaUtil.Models;
using Aliyun.OSS;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tea;
using TSZ.Common.Core.Helper;
using Volo.Abp.Application.Services;
using static AlibabaCloud.SDK.Sts20150401.Models.AssumeRoleResponseBody;

namespace TSZ.ServiceBase.FileStorageCenter.AliyunOSS
{
    /// <summary>
    /// 
    /// </summary>
    public class AliyunOSSService : ApplicationService, IAliyunOSSService
    {
        private readonly IConfiguration _configuration;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="configuration"></param>
        /// <param name=""></param>
        public AliyunOSSService(IConfiguration configuration)
        {
            _configuration=configuration;
        }

       /**
       * 使用AK&SK初始化账号Client
       * @param accessKeyId
       * @param accessKeySecret
       * @return Client
       * @throws Exception
       */
        private static Client CreateClient(string accessKeyId, string accessKeySecret,string endPoint)
        {
            AlibabaCloud.OpenApiClient.Models.Config config = new AlibabaCloud.OpenApiClient.Models.Config
            {
                // 必填，您的 AccessKey ID
                AccessKeyId = accessKeyId,
                // 必填，您的 AccessKey Secret
                AccessKeySecret = accessKeySecret,
            };
            // 访问的域名
            config.Endpoint = endPoint;// "sts.cn-beijing.aliyuncs.com";
            return new Client(config);
        }

        /// <summary>
        /// 获取上传文件STS权限
        /// </summary>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpGet]
        public async Task<AssumeRoleResponseBodyCredentials> GetSTSToken()
        {
            #region policy 只提供上传文件操作权限
            string policy = "{\n" +
             "  \"Version\": \"1\",\n" +
             "  \"Statement\": [\n" +
             "   {\n" +
             "     \"Effect\": \"Allow\",\n" +
             "     \"Action\": [\n" +
             "     \"oss:PutObject\",\n" +
             "     \"oss:GetObject\" \n" +
             "     ],\n" +
             "     \"Resource\": \"*\",\n" +
             "     \"Condition\": { }\n" +
             "   }\n" +
             " ]\n" +
            "}";
            #endregion
            string accessKeyId = _configuration["TSZ_BaseCommon:AliyunOSS:AccessKeyId"];
            string accessKeySecret = _configuration["TSZ_BaseCommon:AliyunOSS:AccessKeySecret"];
            string endPoint = _configuration["TSZ_BaseCommon:AliyunOSS:Endpoint"];
            Client client = CreateClient(accessKeyId, accessKeySecret, endPoint);
            AssumeRoleRequest assumeRoleRequest = new AssumeRoleRequest
            {
                DurationSeconds =Convert.ToInt32(_configuration["TSZ_BaseCommon:AliyunOSS:DurationSeconds"]),
                Policy = policy,
                RoleArn = _configuration["TSZ_BaseCommon:AliyunOSS:RoleArn"],// "acs:ram::1052476646703671:role/ramosstsz",
                RoleSessionName = _configuration["TSZ_BaseCommon:AliyunOSS:RoleSessionName"]//"RamOssTsz",
            };
            try
            {
                AssumeRoleResponse response = await client.AssumeRoleWithOptionsAsync(assumeRoleRequest, new RuntimeOptions());
                if (response.Body != null)
                {
                    AssumeRoleResponseBody responseBody = response.Body;
                    if (responseBody != null)
                    {
                        return responseBody.Credentials;
                    }
                }
            }
            catch (TeaException error)
            {
                AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
            }
            catch (Exception _error)
            {
                TeaException error = new TeaException(new Dictionary<string, object>
                {
                    { "message", _error.Message }
                });
                AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
            }
            return null;
        }

        /// <summary>
        /// 获取可以操作oss所有的权限(管理员用)
        /// </summary>
        /// <returns></returns>
        //[ActionPrefix("read")]
        //[HttpGet]
        private async Task<AssumeRoleResponseBodyCredentials> GetAllPermissionSTSToken()
        {
            #region policy
            string policy = "{\n" +
 "  \"Version\": \"1\",\n" +
 "  \"Statement\": [\n" +
 "   {\n" +
 "     \"Effect\": \"Allow\",\n" +
 "     \"Action\": [\n" +
// "       \"oss:*\",\n" +
 "       \"oss:Get*\",\n" +
 "       \"oss:Put*\",\n" +
 "       \"oss:List*\",\n" +
 "       \"oss:GetBucketReplicationProgress\",\n" +
 "       \"oss:GetBucketReplicationLocation\",\n" +
 "       \"oss:DeleteBucketReplication\",\n" +
 "       \"oss:GetBucketReplication\",\n" +
 "       \"oss:PutBucketReplication\",\n" +
 "       \"oss:DeleteBucketCors\",\n" +
 "       \"oss:GetBucketCors\",\n" +
 "       \"oss:PutBucketCors\",\n" +
 "       \"oss:DeleteBucketLifecycle\",\n" +
 "       \"oss:GetBucketLifecycle\",\n" +
 "       \"oss:PutBucketLifecycle\",\n" +
 "       \"oss:DeleteBucketWebsite\",\n" +
 "       \"oss:GetBucketWebsite\",\n" +
 "       \"oss:PutBucketWebsite\",\n" +
 "       \"oss:DeleteBucketLogging\",\n" +
 "       \"oss:GetBucketLogging\",\n" +
 "       \"oss:PutBucketLogging\",\n" +
 "       \"oss:GetBucketReferer\",\n" +
 "       \"oss:PutBucketReferer\",\n" +
 "       \"oss:GetBucketAcl\",\n" +
 "       \"oss:PutBucketAcl\",\n" +
 "       \"oss:ListMultipartUploads\",\n" +
 "       \"oss:GetBucketLocation\",\n" +
 "       \"oss:DeleteBucket\",\n" +
 "       \"oss:PutBucket\",\n" +
 "       \"oss:ListBuckets\",\n" +
 "       \"oss:RestoreObject\",\n" +
 "       \"oss:ListObjects\",\n" +
 "       \"oss:AbortMultipartUpload\",\n" +
 "       \"oss:ListParts\",\n" +
 "       \"oss:PutObjectAcl\",\n" +
 "       \"oss:GetObjectAcl\",\n" +
 "       \"oss:DeleteObject\",\n" +
 "       \"oss:DeleteMultipleObjects\",\n" +
 "       \"oss:PutObject\",\n" +
 "       \"oss:GetObject\" \n" +
 "     ],\n" +
 "     \"Resource\": \"*\",\n" +
 "     \"Condition\": { }\n" +
 "           }\n" +
 " ]\n" +
"}";
            #endregion
            string accessKeyId = _configuration["TSZ_BaseCommon:AliyunOSS:AccessKeyId"];
            string accessKeySecret = _configuration["TSZ_BaseCommon:AliyunOSS:AccessKeySecret"];
            string endPoint = _configuration["TSZ_BaseCommon:AliyunOSS:Endpoint"];
            Client client = CreateClient(accessKeyId, accessKeySecret, endPoint);
            AssumeRoleRequest assumeRoleRequest = new AssumeRoleRequest
            {
                DurationSeconds = 3600,
                Policy = policy,
                RoleArn = _configuration["TSZ_BaseCommon:AliyunOSS:RoleArn"],// "acs:ram::1052476646703671:role/ramosstsz",
                RoleSessionName = _configuration["TSZ_BaseCommon:AliyunOSS:RoleSessionName"]//"RamOssTsz",
            };
            try
            {
                AssumeRoleResponse response = await client.AssumeRoleWithOptionsAsync(assumeRoleRequest, new RuntimeOptions());
                if (response.Body != null)
                {
                    AssumeRoleResponseBody responseBody = response.Body;
                    if (responseBody != null)
                    {
                        return responseBody.Credentials;
                    }
                }
            }
            catch (TeaException error)
            {
                AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
            }
            catch (Exception _error)
            {
                TeaException error = new TeaException(new Dictionary<string, object>
                {
                    { "message", _error.Message }
                });
                AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
            }
            return null;
        }

        /// <summary>
        /// 删除文件信息（临时删除）
        /// </summary>
        /// <param name="bucketName">桶名</param>
        /// <param name="objectName">路径</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task DeleteObject(string bucketName, string objectName)
        {
            AssumeRoleResponseBodyCredentials credentials =await GetAllPermissionSTSToken();
            // yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
            //var endpoint = "sts.cn-beijing.aliyuncs.com";
            var endpoint = "oss-cn-beijing.aliyuncs.com";
            // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
            var accessKeyId = credentials.AccessKeyId;
            var accessKeySecret = credentials.AccessKeySecret;
            // 填写Bucket名称，例如examplebucket。
            //var bucketName = "tszobs";
            // 填写Object完整路径，完整路径中不能包含Bucket名称，例如exampledir/exampleobject.txt。
            //var objectName = "exampledir/exampleobject.txt";
            // 填写Object的版本ID或删除标记的版本ID。
            //var versionid = "yourObjectVersionidOrDelMarkerVersionid";
            // 创建OSSClient实例。
            var client = new OssClient(endpoint, accessKeyId, accessKeySecret, credentials.SecurityToken);
            try
            {
                // 不指定versionId对Object进行临时删除，此操作会为Object添加删除标记。
                var result = client.DeleteObject(bucketName, objectName);
                Console.WriteLine("Delete object succeeded");
            }
            catch (Exception ex)
            {
                Console.WriteLine("Delete object failed. {0}", ex.Message);
            }
        }


        /// <summary>
        /// 删除文件信息(永久删除)
        /// </summary>
        /// <param name="bucketName">桶名</param>
        /// <param name="objectName">路径</param>
        /// <param name="versionId">版本号</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task DeleteObjectByVersion(string bucketName, string objectName, string versionId)
        {
            AssumeRoleResponseBodyCredentials credentials = await GetAllPermissionSTSToken();
            // yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
            //var endpoint = "sts.cn-beijing.aliyuncs.com";
            var endpoint = "oss-cn-beijing.aliyuncs.com";
            // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
            var accessKeyId = credentials.AccessKeyId;
            var accessKeySecret = credentials.AccessKeySecret;
            // 填写Bucket名称，例如examplebucket。
            //var bucketName = "tszobs";
            // 填写Object完整路径，完整路径中不能包含Bucket名称，例如exampledir/exampleobject.txt。
            //var objectName = "exampledir/exampleobject.txt";
            // 填写Object的版本ID或删除标记的版本ID。
            var versionid = versionId;
            // 创建OSSClient实例。
            var client = new OssClient(endpoint, accessKeyId, accessKeySecret, credentials.SecurityToken);
            try
            {
                // 指定Object的versionId，也可以是删除标记的versionId。
                var request = new DeleteObjectRequest(bucketName, objectName)
                {
                    VersionId = versionid
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine("Delete object failed. {0}", ex.Message);
            }
        }

        /// <summary>
        /// 删除文件信息（临时删除）
        /// </summary>
        /*[ActionPrefix("write")]
        [HttpPost]*/
        private async Task DeleteMultipleObjects(string bucketName)
        {
            AssumeRoleResponseBodyCredentials credentials = await GetAllPermissionSTSToken();
            // yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
            //var endpoint = "sts.cn-beijing.aliyuncs.com";
            var endpoint = "oss-cn-beijing.aliyuncs.com";
            // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
            var accessKeyId = credentials.AccessKeyId;
            var accessKeySecret = credentials.AccessKeySecret;
            // 填写Bucket名称，例如examplebucket。
            //var bucketName = "tszobs";
            // 填写Object完整路径，完整路径中不能包含Bucket名称，例如exampledir/exampleobject.txt。
            //var objectName = "exampledir/exampleobject.txt";
            // 填写Object的版本ID或删除标记的版本ID。
            //var versionid = "yourObjectVersionidOrDelMarkerVersionid";
            // 创建OSSClient实例。
            var client = new OssClient(endpoint, accessKeyId, accessKeySecret, credentials.SecurityToken);
            try
            {
                var keys = new List<string>();
                var listResult = client.ListObjects(bucketName);
                foreach (var summary in listResult.ObjectSummaries)
                {
                    keys.Add(summary.Key);
                }
                // quietMode为true表示简单模式，即OSS不返回消息体。quietMode为false表示详细模式，即返回删除成功的文件列表。默认为详细模式。
                var quietMode = false;
                // 通过DeleteObjectsRequest的quietMode参数指定返回模式。
                var request = new DeleteObjectsRequest(bucketName, keys, quietMode);
                // 执行不指定versionId的删除操作后，将为Object添加删除标记。
                var result = client.DeleteObjects(request);
                if ((!quietMode) && (result.Keys != null))
                {
                    foreach (var obj in result.Keys)
                    {
                        Console.WriteLine("Delete successfully : {0} ", obj.Key);
                    }
                }
                Console.WriteLine("Delete objects succeeded");
            }
            catch (Exception ex)
            {
                Console.WriteLine("Delete object failed. {0}", ex.Message);
            }
        }

        /// <summary>
        /// 删除文件信息（永久删除）
        /// </summary>
        /*[ActionPrefix("write")]
        [HttpPost]*/
        private async Task DeleteMultipleObjectsByVersion(string bucketName, List<ObjectIdentifier> objects)
        {
            AssumeRoleResponseBodyCredentials credentials = await GetAllPermissionSTSToken();
            // yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
            //var endpoint = "sts.cn-beijing.aliyuncs.com";
            var endpoint = "oss-cn-beijing.aliyuncs.com";
            // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
            var accessKeyId = credentials.AccessKeyId;
            var accessKeySecret = credentials.AccessKeySecret;
            // 填写Bucket名称，例如examplebucket。
            //var bucketName = "tszobs";
            // 填写Object完整路径，完整路径中不能包含Bucket名称，例如exampledir/exampleobject.txt。
            //var objectName = "exampledir/exampleobject.txt";
            // 填写Object的版本ID或删除标记的版本ID。
            //var versionid = "yourObjectVersionidOrDelMarkerVersionid";
            // 创建OSSClient实例。
            var client = new OssClient(endpoint, accessKeyId, accessKeySecret, credentials.SecurityToken);
            try
            {
                var request = new DeleteObjectVersionsRequest(bucketName, objects);
                // 发起deleteVersions请求。
                client.DeleteObjectVersions(request);
                Console.WriteLine("DeleteObjectVersions succeeded ");
            }
            catch (Exception ex)
            {
                Console.WriteLine("Delete object failed. {0}", ex.Message);
            }
        }
    }
}
