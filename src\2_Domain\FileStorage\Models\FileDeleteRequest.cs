namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件删除请求
    /// </summary>
    public class FileDeleteRequest
    {
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 版本ID
        /// </summary>
        public string VersionId { get; set; }

        /// <summary>
        /// 是否永久删除
        /// </summary>
        public bool PermanentDelete { get; set; }
    }
}
