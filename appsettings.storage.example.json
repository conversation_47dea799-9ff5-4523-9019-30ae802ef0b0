{"FileStorage": {"DefaultStorageType": "S3", "DefaultConfigurationName": "S3", "Storages": {"S3": {"StorageType": "S3", "IsEnabled": true, "IsDefault": true, "Priority": 1, "AccessKeyId": "your-access-key-id", "SecretAccessKey": "your-secret-access-key", "Region": "us-east-1", "BucketName": "your-bucket-name", "UseHttps": true, "ForcePathStyle": false, "TimeoutSeconds": 300}, "MinIO": {"StorageType": "MinIO", "IsEnabled": false, "IsDefault": false, "Priority": 2, "AccessKeyId": "minioadmin", "SecretAccessKey": "minioadmin", "Region": "us-east-1", "ServiceUrl": "http://localhost:9000", "BucketName": "test-bucket", "UseHttps": false, "ForcePathStyle": true, "TimeoutSeconds": 300}, "HuaweiOBS": {"StorageType": "HuaweiOBS", "IsEnabled": false, "IsDefault": false, "Priority": 3, "AccessKeyId": "your-huawei-access-key", "SecretAccessKey": "your-huawei-secret-key", "Region": "cn-north-4", "ServiceUrl": "https://obs.cn-north-4.myhuaweicloud.com", "BucketName": "your-obs-bucket", "UseHttps": true, "ForcePathStyle": false, "TimeoutSeconds": 300}, "TencentCOS": {"StorageType": "TencentCOS", "IsEnabled": false, "IsDefault": false, "Priority": 4, "AccessKeyId": "your-tencent-secret-id", "SecretAccessKey": "your-tencent-secret-key", "Region": "ap-beijing", "ServiceUrl": "https://cos.ap-beijing.myqcloud.com", "BucketName": "your-cos-bucket-1234567890", "UseHttps": true, "ForcePathStyle": false, "TimeoutSeconds": 300}, "Local": {"StorageType": "Local", "IsEnabled": true, "IsDefault": false, "Priority": 5, "ConnectionParameters": {"RootPath": "D:\\FileStorage", "MaxFileSize": "104857600", "AllowedExtensions": ".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar"}}, "AliyunOSS": {"StorageType": "AliyunOSS", "IsEnabled": false, "IsDefault": false, "Priority": 6, "ConnectionParameters": {"AccessKeyId": "your-aliyun-access-key-id", "AccessKeySecret": "your-aliyun-access-key-secret", "Endpoint": "https://oss-cn-hangzhou.aliyuncs.com", "BucketName": "your-oss-bucket", "SecurityToken": ""}}, "FTP": {"StorageType": "FTP", "IsEnabled": false, "IsDefault": false, "Priority": 7, "ConnectionParameters": {"Host": "ftp.example.com", "Port": "21", "Username": "ftpuser", "Password": "ftppassword", "RootPath": "/uploads", "UseSSL": "false", "UsePassive": "true"}}}}}