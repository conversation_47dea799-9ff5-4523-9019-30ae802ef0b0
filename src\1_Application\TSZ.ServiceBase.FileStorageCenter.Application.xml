<?xml version="1.0"?>
<doc>
    <assembly>
        <name>TSZ.ServiceBase.FileStorageCenter.Application</name>
    </assembly>
    <members>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.AliyunOSS.AliyunOSSService">
            <summary>
            
            </summary>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.AliyunOSS.AliyunOSSService.#ctor(Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            
            </summary>
            <param name="configuration"></param>
            <param name=""></param>
        </member>
        <!-- Badly formed XML comment ignored for member "M:TSZ.ServiceBase.FileStorageCenter.AliyunOSS.AliyunOSSService.CreateClient(System.String,System.String,System.String)" -->
        <member name="M:TSZ.ServiceBase.FileStorageCenter.AliyunOSS.AliyunOSSService.GetSTSToken">
            <summary>
            获取上传文件STS权限
            </summary>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.AliyunOSS.AliyunOSSService.GetAllPermissionSTSToken">
            <summary>
            获取可以操作oss所有的权限(管理员用)
            </summary>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.AliyunOSS.AliyunOSSService.DeleteObject(System.String,System.String)">
            <summary>
            删除文件信息（临时删除）
            </summary>
            <param name="bucketName">桶名</param>
            <param name="objectName">路径</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.AliyunOSS.AliyunOSSService.DeleteObjectByVersion(System.String,System.String,System.String)">
            <summary>
            删除文件信息(永久删除)
            </summary>
            <param name="bucketName">桶名</param>
            <param name="objectName">路径</param>
            <param name="versionId">版本号</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.AliyunOSS.AliyunOSSService.DeleteMultipleObjects(System.String)">
            <summary>
            删除文件信息（临时删除）
            </summary>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.AliyunOSS.AliyunOSSService.DeleteMultipleObjectsByVersion(System.String,System.Collections.Generic.List{Aliyun.OSS.ObjectIdentifier})">
            <summary>
            删除文件信息（永久删除）
            </summary>
        </member>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterApplicationAutoMapperProfile">
            <summary>
            
            </summary>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterApplicationAutoMapperProfile.#ctor">
            <summary>
            
            </summary>
        </member>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterApplicationModule">
            <summary>
            
            </summary>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterApplicationModule.ConfigureServices(Volo.Abp.Modularity.ServiceConfigurationContext)">
            <summary>
            
            </summary>
            <param name="context"></param>
        </member>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService">
            <summary>
            
            </summary>
        </member>
        <member name="F:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.MyInstanceId">
            <summary>
            
            </summary>
        </member>
        <member name="F:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.IsUserHighPerformanceAPI">
            <summary>
            上传下载操作是否启用高性能的内存操作（大数据对象，字节数组，1MB大小）
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.Request">
            <summary>
            
            </summary>
        </member>
        <member name="F:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.Application_Octet_Stream_ContentType">
            <summary>
            
            </summary>
        </member>
        <member name="F:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService._ftpClient">
            <summary>
            
            </summary>
        </member>
        <member name="F:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService._windowspath">
            <summary>
            
            </summary>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.#ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            
            </summary>
            <param name="httpContextAccessor">IHttpContextAccessor上下文接口</param>
            <param name="configuration">IConfiguration配置接口</param>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.UploadFile(System.String,System.Int32,System.Boolean)">
            <summary>
            上传文件，分割上传（也可以理解为分片上传，但是不需要合并，每次上传都是直接追加到文件的末尾，顺序一直上传，直到上传全部完成）
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名）</param>
            <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.UploadFileChunk(System.String,System.Boolean,System.String,System.Int32,System.Int64,System.Int32,System.Int64)">
            <summary>
            分片上传文件，不管分片数量多少，后续都必须调用FileMerge合并文件
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <param name="identifier">分片标识符</param>
            <param name="chunkNumber">分片号</param>
            <param name="chunkSize">分片大小</param>
            <param name="totalChunks">总分片数量，不管分片数量多少，后续都必须调用FileMerge合并文件</param>
            <param name="totalSize">文件总大小</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.FileMerge(System.String,TSZ.ServiceBase.FileStorageCenter.FileUploadBehaviours,System.String,System.Int32)">
            <summary>
            合并分片
            </summary>
            <param name="strFullFileName">完成服务器文件名</param>
            <param name="fileUploadBehaviour"></param>
            <param name="identifier"></param>
            <param name="totalChunks"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.GetChunkNumberTranslateToGroup(System.Int32,System.Int32)">
            <summary>
            不可取，不能处理多线程同步（mjy）
            超大文件，按照1MB来分片，导致服务器上临时文件太多，1GB = 1024个1MB的分片文件，碎片化严重
            特根据totalChunks来变换chunkNumber的值，让多碎片文件在服务器上临时存储为几个分片，比如1GB文件 = 1,2,3,4,5,6个分片
            </summary>
            <param name="chunkNumber"></param>
            <param name="totalChunks"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.DownloadFile(System.String,System.Int32,System.Boolean)">
            <summary>
            下载文件，分割下载，一次只下载 FileServiceConfig.MAXFILESIZE（默认为1MB）大小，顺序一直下载，直到下载全部完成
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名）</param>
            <param name="intReadCount">文件读取的次数（第几次，从0开始计数）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.DownloadFilePosition(System.String,System.Int64,System.Int32,System.Boolean)">
            <summary>
            下载文件，从指定节点
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名）</param>
            <param name="lPosition">读取文件的位置（文件从0开始的偏移量）</param>
            <param name="intReadSize">读取文件大小（一次读取的文件大小）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.OverrideFile(System.String,System.Int32,System.Boolean)">
            <summary>
            覆盖文件
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名）</param>
            <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.CreateSignatureFile(System.String,System.Int16,System.Boolean)">
            <summary>
            创建签名文件【分块】
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="intChunkSize">分块数量</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.CreateDeltaFile(System.String,System.String,System.Boolean)">
            <summary>
            创建Delta文件【分块】
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strSignatureFile">Delta完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.ApplyDeltaFileToCreateNewFile(System.String,System.String,System.String,System.Boolean)">
            <summary>
            从Delta文件创建新文件
            </summary>
            <param name="strReferenceServerFullFileName">参考的完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strDeltaFile">Delta完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.DownloadFileWhole(System.String,System.Boolean)">
            <summary>
            下载文件
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否是加密文件</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.DownloadFileWholePart(System.String,System.Int32,System.Boolean)">
            <summary>
            下载文件
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="intReadCount">文件读取的次数（第几次，从0开始计数）</param>
            <param name="isEncryptDecrypt">是否是加密文件</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.DownloadModelFileWhole(System.String,System.Boolean)">
            <summary>
            下载文件(发布目录下的模型等文件)（通用），在特定目录下（发布目录）去下载
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否是加密文件</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.UploadFileWhole(System.String,System.Boolean)">
            <summary>
            上传文件（整个一起上传）
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否加密文件</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.UploadFileWholePart(System.String,System.Int32,System.Boolean)">
            <summary>
            上传文件，切割上传
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
            <param name="isEncryptDecrypt">是否加密文件</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.UploadFileWholeEx(System.String,System.Boolean)">
            <summary>
            上传文件（整个一起上传）,文件压测接口，不实际读写文件，避免磁盘爆炸和大量垃圾
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否加密文件</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.UploadFileWholePartEx(System.String,System.Int32,System.Boolean)">
            <summary>
            上传文件，切割上传,文件压测接口，不实际读写文件，避免磁盘爆炸和大量垃圾
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
            <param name="isEncryptDecrypt">是否加密文件</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.UploadFileChunks">
            <summary>
            GET请求，获取已经上传的分片信息（分片上传前返回已经上传的分片给前端）【vue前端专用】
            </summary>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.UploadFileChunks(System.Int32,System.Int64,System.Int64,System.String,System.Int32,System.String,System.String)">
            <summary>
            文件分片上传，后续需要调用FileMergeChunks合并分片（除非totalChunks=1则直接处理为直接上传，不需要FileMergeChunks合并）【vue前端专用】
            临时分片文件存储目录为：根目录+oneceGuid+identifier
            </summary>
            <param name="chunkNumber">分片编号</param>
            <param name="chunkSize">分片大小</param>
            <param name="totalSize">文件总大小</param>
            <param name="identifier">文件标识符，临时分片文件存储目录为：根目录+oneceGuid+identifier</param>      
            <param name="totalChunks">总分片数，大于1的总分片，后续需要调用FileMergeChunks合并分片，如果等于1，则变成直接上传</param>
            <param name="bucketName">模拟对象存储的bucketName，必填，可以为租户Id，可以是任意字符串，如tszobs、Guid等</param>
            <param name="objectName">模拟对象存储的objectName，可以是文件对象名称而且可以带子目录，如exampledir/exampleobject.txt</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.FileMergeChunks(System.String,System.Int32,System.String,System.String)">
            <summary>
            合并分片文件，完成上传（UploadFileChunks的后续方法）【vue前端专用】
            临时分片文件存储目录为：根目录+oneceGuid+identifier
            </summary>
            <param name="identifier">文件标识符，临时分片文件存储目录为：根目录+oneceGuid+identifier</param>       
            <param name="totalChunks">总分片数量</param>
            <param name="bucketName">模拟对象存储的bucketName，必填，可以为租户Id，可以是任意字符串，如tszobs、Guid等</param>
            <param name="objectName">模拟对象存储的objectName，可以是文件对象名称而且可以带子目录，如exampledir/exampleobject.txt</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.FileMergeChunksAll(System.String,System.String,System.String)">
            <summary>
            合并分片方法，忽略totalChunks参数，进行全部合并（合并分片目录下的全部分片，而忽略totalChunks的限制），对应于FileMergeChunks的扩展方法【vue前端专用】
            </summary>
            <param name="identifier">文件MD5值</param>
            <param name="bucketName">模拟对象存储的bucketName，必填，可以为租户Id，可以是任意字符串，如tszobs、Guid等</param>
            <param name="objectName">模拟对象存储的objectName，可以是文件对象名称而且可以带子目录，如exampledir/exampleobject.txt</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.CanOpened">
            <summary>
            测试API，检查文件服务是否正常
            </summary>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.GetServerDateTime">
            <summary>
            获取服务器时间，返回标准时间
            </summary>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.ExistsFile(System.String)">
            <summary>
            判断文件是否存在
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.GetFileLength(System.String)">
            <summary>
            获取文件大小
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.GetFileInfo(System.String,System.Boolean)">
            <summary>
            获取文件信息
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.DeleteFile(System.String)">
            <summary>
            删除文件
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.DeleteDirectory(System.String)">
            <summary>
            删除文件夹
            </summary>
            <param name="strDirectoryName"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.CopyFile(System.String,System.String)">
            <summary>
            复制文件
            </summary>
            <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.CopyFileOverwrite(System.String,System.String)">
            <summary>
            复制文件
            </summary>
            <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.CopyFileEncryptToDecrypt(System.String,System.String)">
            <summary>
            解密文件
            </summary>
            <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.CopyFileDecryptToEncrypt(System.String,System.String)">
            <summary>
            加密文件
            </summary>
            <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.ComputeFileMD5(System.String,System.Int64,System.Boolean)">
            <summary>
            获取文件MD5值
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="lFileLength">文件长度</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.ReNameFile(System.String,System.String)">
            <summary>
            重命名文件
            </summary>
            <param name="strFullFileName">文件原始路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strNewFileName">文件新路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.MoveFile(System.String,System.String)">
            <summary>
            移动文件
            </summary>
            <param name="strSrcFullFileName">文件原始路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetFullFileName">文件目的路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.GetDirectoyChild(System.String)">
            <summary>
            获取文件夹文件
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.GetDirectoryFiles(System.String)">
            <summary>
            获取文件下面目录
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.MoveDirectoryFile(System.String,System.String)">
            <summary>
            移动文件夹下面的文件
            </summary>
            <param name="sourcePath"></param>
            <param name="targetPath"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.UnzipFile(System.String,System.String,System.Boolean)">
            <summary>
            解压指定文件到指定目录
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatas，则API会自动加上，只能在 /tszappdatas 根目录下解压到指定的子目录</param>
            <param name="isEncryptDecrypt">原始文件是否是加密的</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.GetZipFolderArchive(TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto.ZipFolderArchivesDto)">
            <summary>
            生成压缩包的方法(TSV5专用)-改为通用方法
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.CreateDictionaryFileInfos(TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto.ZipFolderArchivesDto,System.String,System.String,System.Collections.Generic.List{TSZ.Common.Shared.CompressHelper.ZipEntryInfo}@)">
            <summary>
            创建目录文件实体对象的文件信息对象集合（ZipEntryInfo）
            </summary>
            <param name="zipFolderArchivesDto"></param>
            <param name="parentPath"></param>
            <param name="floderName"></param>
            <param name="fileInfos"></param>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.UnzipFileWithJsonInfo(System.String,System.String,System.Boolean,System.String)">
            <summary>
            解压指定文件到指定目录并生成2d图纸信息json文件（BimBang专属接口，其它应用不能使用）
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下解压到指定的子目录</param>
            <param name="isEncryptDecrypt">原始文件是否是加密的</param>
            <param name="jsonFileName">生成json的文件名</param>
            <returns>true：有2d图纸，false：无2d图纸</returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.DeleteFileAndFolder(System.String,System.String)">
            <summary>
            删除指定文件并删除模型发布服务的指定目录（通用）
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下解压到指定的子目录</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.UnzipModelFile(System.String,System.String,System.Boolean)">
            <summary>
            解压模型文件到模型web服务（通用）
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下解压到指定的子目录</param>
            <param name="isEncryptDecrypt">原始文件是否是加密的</param>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.CopyModelFile(System.String,System.String)">
            <summary>
            复制模型文件到模型web服务（通用）
            </summary>
            <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下复制到指定的子目录</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.UpLoadFileWholeByFtp(System.String,System.String)">
            <summary>
            单个传文件
            </summary>
            <param name="clientFullName">客户端的全路径</param>
            <param name="serverFullName">服务端的全路径</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.UpLoadFilesWholeByFtp(System.Collections.Generic.List{System.String},System.String)">
            <summary>
            批量上传文件
            </summary>
            <param name="clientFullNames">上传的文件客户端全路径</param>
            <param name="serverDirec">上传的目录</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.DownLoadFileByFtp(System.String,System.String)">
            <summary>
            ftp下载文件
            </summary>
            <param name="clientFullName">客户端全路径</param>
            <param name="serverFullName">服务器全路径</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterAppService.CreateDirectoryByFtp(System.String)">
            <summary>
            ftp创建目录
            </summary>
            <param name="drecName">目录名称</param>
            <returns></returns>
        </member>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.FileSynchronizerAppService">
            <summary>
            文件同步服务
            </summary>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileSynchronizerAppService.#ctor(Microsoft.Extensions.Configuration.IConfiguration,System.Net.Http.IHttpClientFactory)">
            <summary>
            构造函数
            </summary>
            <param name="configuration"></param>
            <param name="httpClientFactory"></param>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileSynchronizerAppService.DownloadAsync(System.String,System.String)">
            <summary>
            从相同架构文件服务同步下载文件
            </summary>
            <param name="strFullFileName"></param>
            <param name="tenantId"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileSynchronizerAppService.DownloadFileAsync(System.String,System.Int64,System.Int32)">
            <summary>
            
            </summary>
            <param name="strFullFileName"></param>
            <param name="position"></param>
            <param name="readSize"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileSynchronizerAppService.GetFileSize(System.String)">
            <summary>
            
            </summary>
            <param name="strFullFileName"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileSynchronizerAppService.GetAvalidatedFileName(System.String)">
            <summary>
            
            </summary>
            <param name="strFullFileName"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileSynchronizerAppService.GetNewFileName(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="strFullFileName"></param>
            <param name="tenantId"></param>
            <returns></returns>
        </member>
    </members>
</doc>
