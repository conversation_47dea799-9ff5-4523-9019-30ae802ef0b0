using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件存储提供者接口
    /// 定义统一的文件存储操作抽象，支持多种存储后端
    /// </summary>
    public interface IFileStorageProvider
    {
        /// <summary>
        /// 存储类型
        /// </summary>
        StorageType StorageType { get; }

        /// <summary>
        /// 存储配置名称
        /// </summary>
        string ConfigurationName { get; }

        /// <summary>
        /// 是否支持分片上传
        /// </summary>
        bool SupportsChunkedUpload { get; }

        /// <summary>
        /// 是否支持断点续传
        /// </summary>
        bool SupportsResumableUpload { get; }

        /// <summary>
        /// 是否支持预签名URL
        /// </summary>
        bool SupportsPresignedUrl { get; }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="request">上传请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>上传结果</returns>
        Task<FileUploadResult> UploadFileAsync(FileUploadRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 分片上传文件
        /// </summary>
        /// <param name="request">分片上传请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>分片上传结果</returns>
        Task<ChunkedUploadResult> UploadChunkAsync(ChunkedUploadRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 完成分片上传
        /// </summary>
        /// <param name="request">完成分片上传请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>完成结果</returns>
        Task<FileUploadResult> CompleteChunkedUploadAsync(CompleteChunkedUploadRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="request">下载请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>下载结果</returns>
        Task<FileDownloadResult> DownloadFileAsync(FileDownloadRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 分片下载文件
        /// </summary>
        /// <param name="request">分片下载请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>文件流</returns>
        Task<Stream> DownloadFileRangeAsync(FileRangeDownloadRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="request">删除请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>删除结果</returns>
        Task<FileDeleteResult> DeleteFileAsync(FileDeleteRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量删除文件
        /// </summary>
        /// <param name="request">批量删除请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>删除结果</returns>
        Task<BatchDeleteResult> BatchDeleteFilesAsync(BatchDeleteRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 复制文件
        /// </summary>
        /// <param name="request">复制请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>复制结果</returns>
        Task<FileCopyResult> CopyFileAsync(FileCopyRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 移动文件
        /// </summary>
        /// <param name="request">移动请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>移动结果</returns>
        Task<FileMoveResult> MoveFileAsync(FileMoveRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="request">检查请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>检查结果</returns>
        Task<FileExistsResult> FileExistsAsync(FileExistsRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取文件信息
        /// </summary>
        /// <param name="request">获取文件信息请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>文件信息结果</returns>
        Task<FileInfoResult> GetFileInfoAsync(FileInfoRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成预签名URL
        /// </summary>
        /// <param name="request">预签名URL请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>预签名URL结果</returns>
        Task<PresignedUrlResult> GeneratePresignedUrlAsync(PresignedUrlRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 列出文件
        /// </summary>
        /// <param name="request">列出文件请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>文件列表</returns>
        Task<FileListResult> ListFilesAsync(FileListRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取存储统计信息
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>存储统计信息</returns>
        Task<StorageStatisticsResult> GetStorageStatisticsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 健康检查
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>健康状态</returns>
        Task<HealthCheckResult> HealthCheckAsync(CancellationToken cancellationToken = default);

        #region 文件快传功能 - REQ-010

        /// <summary>
        /// 检查文件是否已存在（基于MD5和文件大小）
        /// </summary>
        /// <param name="request">文件检查请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>文件检查结果</returns>
        Task<FileExistenceCheckResult> CheckFileExistenceAsync(
            FileExistenceCheckRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 快传文件（基于已存在的文件创建新的引用）
        /// </summary>
        /// <param name="request">快传请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>快传结果</returns>
        Task<QuickUploadResult> QuickUploadFileAsync(
            QuickUploadRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 增加文件引用计数
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="increment">增加数量（默认为1）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        Task<ReferenceCountResult> IncrementReferenceCountAsync(
            string fileId,
            int increment = 1,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 减少文件引用计数
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="decrement">减少数量（默认为1）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        Task<ReferenceCountResult> DecrementReferenceCountAsync(
            string fileId,
            int decrement = 1,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取文件引用计数
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>引用计数结果</returns>
        Task<ReferenceCountResult> GetReferenceCountAsync(
            string fileId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 清理无引用的文件
        /// </summary>
        /// <param name="request">清理请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>清理结果</returns>
        Task<CleanupResult> CleanupUnreferencedFilesAsync(
            CleanupRequest request,
            CancellationToken cancellationToken = default);

        #endregion

        #region 短链分享功能 - REQ-005

        /// <summary>
        /// 创建文件分享
        /// </summary>
        /// <param name="request">分享创建请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>分享创建结果</returns>
        Task<ShareCreationResult> CreateFileShareAsync(
            ShareCreationRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取分享信息
        /// </summary>
        /// <param name="shareCode">分享码</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>分享信息</returns>
        Task<ShareInfoResult> GetShareInfoAsync(
            string shareCode,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 验证分享访问权限
        /// </summary>
        /// <param name="request">访问验证请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>验证结果</returns>
        Task<ShareAccessResult> ValidateShareAccessAsync(
            ShareAccessRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 通过分享下载文件
        /// </summary>
        /// <param name="request">分享下载请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>下载结果</returns>
        Task<ShareDownloadResult> DownloadFileByShareAsync(
            ShareDownloadRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 更新分享设置
        /// </summary>
        /// <param name="request">分享更新请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>更新结果</returns>
        Task<ShareUpdateResult> UpdateShareAsync(
            ShareUpdateRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 删除分享
        /// </summary>
        /// <param name="shareCode">分享码</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>删除结果</returns>
        Task<ShareDeleteResult> DeleteShareAsync(
            string shareCode,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取文件的分享列表
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>分享列表</returns>
        Task<ShareListResult> GetFileSharesAsync(
            string fileId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量管理分享
        /// </summary>
        /// <param name="request">批量管理请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>管理结果</returns>
        Task<BatchShareManagementResult> BatchManageSharesAsync(
            BatchShareManagementRequest request,
            CancellationToken cancellationToken = default);

        #endregion
    }
}
