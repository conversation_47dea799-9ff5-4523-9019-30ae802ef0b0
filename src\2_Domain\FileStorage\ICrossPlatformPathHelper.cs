using System.Collections.Generic;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 跨平台路径处理接口
    /// 提供Linux和Windows混合部署环境下的路径标准化和转换功能
    /// </summary>
    public interface ICrossPlatformPathHelper
    {
        /// <summary>
        /// 标准化路径（统一使用正斜杠作为分隔符）
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>标准化后的路径</returns>
        string NormalizePath(string path);

        /// <summary>
        /// 转换为平台特定路径
        /// </summary>
        /// <param name="standardPath">标准化路径</param>
        /// <returns>平台特定路径</returns>
        string ToPlatformPath(string standardPath);

        /// <summary>
        /// 转换为Unix风格路径（使用正斜杠）
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>Unix风格路径</returns>
        string ToUnixPath(string path);

        /// <summary>
        /// 转换为Windows风格路径（使用反斜杠）
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>Windows风格路径</returns>
        string ToWindowsPath(string path);

        /// <summary>
        /// 组合路径（使用标准化分隔符）
        /// </summary>
        /// <param name="paths">路径片段</param>
        /// <returns>组合后的标准化路径</returns>
        string CombinePaths(params string[] paths);

        /// <summary>
        /// 获取相对路径
        /// </summary>
        /// <param name="basePath">基础路径</param>
        /// <param name="fullPath">完整路径</param>
        /// <returns>相对路径</returns>
        string GetRelativePath(string basePath, string fullPath);

        /// <summary>
        /// 获取绝对路径
        /// </summary>
        /// <param name="basePath">基础路径</param>
        /// <param name="relativePath">相对路径</param>
        /// <returns>绝对路径</returns>
        string GetAbsolutePath(string basePath, string relativePath);

        /// <summary>
        /// 验证路径是否有效
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>是否有效</returns>
        bool IsValidPath(string path);

        /// <summary>
        /// 检查路径是否为绝对路径
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>是否为绝对路径</returns>
        bool IsAbsolutePath(string path);

        /// <summary>
        /// 检查路径是否为相对路径
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>是否为相对路径</returns>
        bool IsRelativePath(string path);

        /// <summary>
        /// 获取文件名（不包含路径）
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>文件名</returns>
        string GetFileName(string path);

        /// <summary>
        /// 获取文件名（不包含扩展名）
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>文件名（无扩展名）</returns>
        string GetFileNameWithoutExtension(string path);

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>文件扩展名（包含点号）</returns>
        string GetFileExtension(string path);

        /// <summary>
        /// 获取目录路径
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>目录路径</returns>
        string GetDirectoryPath(string path);

        /// <summary>
        /// 清理路径中的非法字符
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <param name="replacement">替换字符（默认为下划线）</param>
        /// <returns>清理后的路径</returns>
        string CleanPath(string path, char replacement = '_');

        /// <summary>
        /// 生成安全的文件名
        /// </summary>
        /// <param name="fileName">原始文件名</param>
        /// <param name="replacement">替换字符（默认为下划线）</param>
        /// <returns>安全的文件名</returns>
        string GenerateSafeFileName(string fileName, char replacement = '_');

        /// <summary>
        /// 检查路径是否包含非法字符
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>是否包含非法字符</returns>
        bool ContainsInvalidChars(string path);

        /// <summary>
        /// 获取路径深度（目录层级数）
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>路径深度</returns>
        int GetPathDepth(string path);

        /// <summary>
        /// 分割路径为片段
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>路径片段列表</returns>
        List<string> SplitPath(string path);

        /// <summary>
        /// 检查路径是否在指定的基础路径下
        /// </summary>
        /// <param name="basePath">基础路径</param>
        /// <param name="targetPath">目标路径</param>
        /// <returns>是否在基础路径下</returns>
        bool IsUnderBasePath(string basePath, string targetPath);

        /// <summary>
        /// 确保路径以指定字符结尾
        /// </summary>
        /// <param name="path">路径</param>
        /// <param name="endChar">结尾字符（默认为正斜杠）</param>
        /// <returns>处理后的路径</returns>
        string EnsureEndsWith(string path, char endChar = '/');

        /// <summary>
        /// 确保路径不以指定字符结尾
        /// </summary>
        /// <param name="path">路径</param>
        /// <param name="endChar">结尾字符（默认为正斜杠）</param>
        /// <returns>处理后的路径</returns>
        string EnsureNotEndsWith(string path, char endChar = '/');

        /// <summary>
        /// 获取当前操作系统的路径分隔符
        /// </summary>
        /// <returns>路径分隔符</returns>
        char GetPlatformPathSeparator();

        /// <summary>
        /// 检查当前是否为Windows平台
        /// </summary>
        /// <returns>是否为Windows平台</returns>
        bool IsWindows();

        /// <summary>
        /// 检查当前是否为Unix平台（Linux/macOS）
        /// </summary>
        /// <returns>是否为Unix平台</returns>
        bool IsUnix();
    }
}
