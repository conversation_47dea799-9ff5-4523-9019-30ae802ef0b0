using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 分享访问控制配置
    /// 用于高级权限控制功能
    /// </summary>
    public class ShareAccessControl
    {
        /// <summary>
        /// 分享ID
        /// </summary>
        public Guid ShareId { get; set; }

        /// <summary>
        /// IP白名单（JSON格式存储）
        /// </summary>
        [StringLength(2000)]
        public string AllowedIPs { get; set; }

        /// <summary>
        /// IP黑名单（JSON格式存储）
        /// </summary>
        [StringLength(2000)]
        public string BlockedIPs { get; set; }

        /// <summary>
        /// 允许的用户ID列表（JSON格式存储）
        /// </summary>
        [StringLength(2000)]
        public string AllowedUserIds { get; set; }

        /// <summary>
        /// 允许的用户组列表（JSON格式存储）
        /// </summary>
        [StringLength(1000)]
        public string AllowedUserGroups { get; set; }

        /// <summary>
        /// 地理位置限制（国家代码，JSON格式存储）
        /// </summary>
        [StringLength(500)]
        public string AllowedCountries { get; set; }

        /// <summary>
        /// 时间段限制（JSON格式存储）
        /// </summary>
        [StringLength(1000)]
        public string TimeRestrictions { get; set; }

        /// <summary>
        /// 设备类型限制
        /// </summary>
        public DeviceTypeRestriction DeviceRestriction { get; set; }

        /// <summary>
        /// 浏览器限制（JSON格式存储）
        /// </summary>
        [StringLength(500)]
        public string AllowedBrowsers { get; set; }

        /// <summary>
        /// 引用页面限制（JSON格式存储）
        /// </summary>
        [StringLength(1000)]
        public string AllowedReferrers { get; set; }

        /// <summary>
        /// 是否启用验证码
        /// </summary>
        public bool RequireCaptcha { get; set; }

        /// <summary>
        /// 最大并发访问数
        /// </summary>
        public int? MaxConcurrentAccess { get; set; }

        /// <summary>
        /// 访问频率限制（每分钟最大访问次数）
        /// </summary>
        public int? MaxAccessPerMinute { get; set; }

        /// <summary>
        /// 自定义访问规则（JSON格式存储）
        /// </summary>
        [StringLength(2000)]
        public string CustomRules { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LastModificationTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ShareAccessControl()
        {
            CreationTime = DateTime.UtcNow;
            DeviceRestriction = DeviceTypeRestriction.None;
        }

        /// <summary>
        /// 解析IP白名单
        /// </summary>
        /// <returns></returns>
        public List<string> GetAllowedIPList()
        {
            if (string.IsNullOrEmpty(AllowedIPs))
                return new List<string>();

            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<List<string>>(AllowedIPs);
            }
            catch
            {
                return new List<string>();
            }
        }

        /// <summary>
        /// 设置IP白名单
        /// </summary>
        /// <param name="ips"></param>
        public void SetAllowedIPList(List<string> ips)
        {
            AllowedIPs = ips?.Count > 0 ? System.Text.Json.JsonSerializer.Serialize(ips) : null;
        }

        /// <summary>
        /// 解析IP黑名单
        /// </summary>
        /// <returns></returns>
        public List<string> GetBlockedIPList()
        {
            if (string.IsNullOrEmpty(BlockedIPs))
                return new List<string>();

            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<List<string>>(BlockedIPs);
            }
            catch
            {
                return new List<string>();
            }
        }

        /// <summary>
        /// 设置IP黑名单
        /// </summary>
        /// <param name="ips"></param>
        public void SetBlockedIPList(List<string> ips)
        {
            BlockedIPs = ips?.Count > 0 ? System.Text.Json.JsonSerializer.Serialize(ips) : null;
        }

        /// <summary>
        /// 解析允许的用户ID列表
        /// </summary>
        /// <returns></returns>
        public List<Guid> GetAllowedUserIdList()
        {
            if (string.IsNullOrEmpty(AllowedUserIds))
                return new List<Guid>();

            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<List<Guid>>(AllowedUserIds);
            }
            catch
            {
                return new List<Guid>();
            }
        }

        /// <summary>
        /// 设置允许的用户ID列表
        /// </summary>
        /// <param name="userIds"></param>
        public void SetAllowedUserIdList(List<Guid> userIds)
        {
            AllowedUserIds = userIds?.Count > 0 ? System.Text.Json.JsonSerializer.Serialize(userIds) : null;
        }
    }

    /// <summary>
    /// 设备类型限制
    /// </summary>
    public enum DeviceTypeRestriction
    {
        /// <summary>
        /// 无限制
        /// </summary>
        None = 0,

        /// <summary>
        /// 仅桌面设备
        /// </summary>
        DesktopOnly = 1,

        /// <summary>
        /// 仅移动设备
        /// </summary>
        MobileOnly = 2,

        /// <summary>
        /// 仅平板设备
        /// </summary>
        TabletOnly = 3,

        /// <summary>
        /// 桌面和平板
        /// </summary>
        DesktopAndTablet = 4,

        /// <summary>
        /// 移动和平板
        /// </summary>
        MobileAndTablet = 5
    }

    /// <summary>
    /// 时间段限制配置
    /// </summary>
    public class TimeRestriction
    {
        /// <summary>
        /// 开始时间（24小时制，如：09:00）
        /// </summary>
        public string StartTime { get; set; }

        /// <summary>
        /// 结束时间（24小时制，如：18:00）
        /// </summary>
        public string EndTime { get; set; }

        /// <summary>
        /// 允许的星期几（1-7，1为周一）
        /// </summary>
        public List<int> AllowedDaysOfWeek { get; set; }

        /// <summary>
        /// 时区（如：Asia/Shanghai）
        /// </summary>
        public string TimeZone { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public TimeRestriction()
        {
            AllowedDaysOfWeek = new List<int>();
            TimeZone = "UTC";
        }
    }
}
