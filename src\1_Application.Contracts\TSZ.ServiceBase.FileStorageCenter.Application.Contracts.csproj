﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<RootNamespace>TSZ.ServiceBase.FileStorageCenter.Application.Contracts</RootNamespace>
		<PackageId>TSZ.ServiceBase.FileStorageCenter.Application.Contracts</PackageId>
		<IncludeBuildOutput>true</IncludeBuildOutput>
		<Version>1.0.0</Version>
		<Authors>探索者软件</Authors>
		<Company>探索者软件</Company>
		<GeneratePackageOnBuild>false</GeneratePackageOnBuild>
		<PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
		<GeneratePackageDependencyFile>true</GeneratePackageDependencyFile>
		<GeneratePackageOnBuild>true</GeneratePackageOnBuild>
		<TreatProjectReferencesAsPackageReferences>true</TreatProjectReferencesAsPackageReferences>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>TSZ.ServiceBase.FileStorageCenter.Application.Contracts.xml</DocumentationFile>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Core"  />
		<PackageReference Include="Volo.Abp.ObjectExtending"  />
		<PackageReference Include="Volo.Abp.Account.Application.Contracts"  />
		<PackageReference Include="Volo.Abp.Identity.Application.Contracts"  />
		<PackageReference Include="Volo.Abp.PermissionManagement.Application.Contracts"  />
		<PackageReference Include="Volo.Abp.TenantManagement.Application.Contracts"  />
		<PackageReference Include="Volo.Abp.FeatureManagement.Application.Contracts"  />
		<PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts"  />
		<PackageReference Include="AlibabaCloud.SDK.Sts20150401"  />


		<!-- 手动包含引用的项目 DLL -->
		<!--<Content Include="bin\$(Configuration)\$(TargetFramework)\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.dll">
			<Pack>true</Pack>
			<PackagePath>lib\$(TargetFramework)</PackagePath>
		</Content>-->
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\2_Domain.Shared\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj"></ProjectReference>
	</ItemGroup>

	<ItemGroup>
		<None Update="TSZ.ServiceBase.FileStorageCenter.Application.Contracts.xml">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
