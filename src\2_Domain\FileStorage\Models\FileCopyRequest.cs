using System.Collections.Generic;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件复制请求
    /// </summary>
    public class FileCopyRequest
    {
        /// <summary>
        /// 源文件路径
        /// </summary>
        public string SourceFilePath { get; set; }

        /// <summary>
        /// 源路径（兼容性属性）
        /// </summary>
        public string SourcePath
        {
            get => SourceFilePath;
            set => SourceFilePath = value;
        }

        /// <summary>
        /// 目标文件路径
        /// </summary>
        public string DestinationFilePath { get; set; }

        /// <summary>
        /// 目标路径（兼容性属性）
        /// </summary>
        public string DestinationPath
        {
            get => DestinationFilePath;
            set => DestinationFilePath = value;
        }

        /// <summary>
        /// 是否覆盖
        /// </summary>
        public bool Overwrite { get; set; }

        /// <summary>
        /// 元数据
        /// </summary>
        public Dictionary<string, string> Metadata { get; set; } = new Dictionary<string, string>();
    }
}
