using System;
using System.Collections.Generic;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 存储统计信息
    /// </summary>
    public class StorageStatistics
    {
        /// <summary>
        /// 总文件数
        /// </summary>
        public long TotalFiles { get; set; }

        /// <summary>
        /// 总大小（字节）
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// 可用空间（字节）
        /// </summary>
        public long AvailableSpace { get; set; }

        /// <summary>
        /// 已用空间（字节）
        /// </summary>
        public long UsedSpace { get; set; }

        /// <summary>
        /// 存储利用率（百分比）
        /// </summary>
        public double UsagePercentage => TotalSize > 0 ? (double)UsedSpace / TotalSize * 100 : 0;

        /// <summary>
        /// 按存储类别统计
        /// </summary>
        public Dictionary<string, StorageClassStatistics> StorageClassStats { get; set; } = new Dictionary<string, StorageClassStatistics>();

        /// <summary>
        /// 统计时间
        /// </summary>
        public DateTime StatisticsTime { get; set; } = DateTime.UtcNow;
    }
}
