{"version": 3, "targets": {"net9.0": {"AutoMapper/6.2.2": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.3.0", "NETStandard.Library": "1.6.1", "System.Collections.Concurrent": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Linq.Queryable": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.RegularExpressions": "4.3.0"}, "compile": {"lib/netstandard1.3/AutoMapper.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/AutoMapper.dll": {"related": ".xml"}}}, "ConfigureAwait.Fody/3.3.1": {"type": "package", "dependencies": {"Fody": "6.0.2"}, "compile": {"lib/netstandard2.0/ConfigureAwait.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/ConfigureAwait.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "Fody/6.0.2": {"type": "package", "build": {"build/_._": {}}}, "Google.Protobuf/3.5.1": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.1"}, "compile": {"lib/netstandard1.0/Google.Protobuf.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/Google.Protobuf.dll": {"related": ".xml"}}}, "IdentityModel/3.6.1": {"type": "package", "dependencies": {"Newtonsoft.Json": "11.0.2", "System.Text.Encodings.Web": "4.4.0"}, "compile": {"lib/netstandard2.0/IdentityModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/IdentityModel.dll": {"related": ".xml"}}}, "IdentityServer4/2.2.0": {"type": "package", "dependencies": {"IdentityModel": "3.6.1", "Microsoft.AspNetCore.Authentication": "2.0.3", "Microsoft.AspNetCore.Authentication.Cookies": "2.0.3", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "2.0.3", "Microsoft.AspNetCore.Cors": "2.0.2", "Microsoft.Extensions.Caching.Memory": "2.0.1", "Microsoft.Extensions.Logging": "2.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.0.1", "System.IdentityModel.Tokens.Jwt": "5.2.1", "System.Security.Cryptography.Cng": "4.4.0"}, "compile": {"lib/netstandard2.0/IdentityServer4.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/IdentityServer4.dll": {"related": ".pdb;.xml"}}}, "IdentityServer4.AspNetIdentity/2.1.0": {"type": "package", "dependencies": {"IdentityServer4": "2.1.1", "Microsoft.AspNetCore.Identity": "2.0.1"}, "compile": {"lib/netstandard2.0/IdentityServer4.AspNetIdentity.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/IdentityServer4.AspNetIdentity.dll": {"related": ".pdb;.xml"}}}, "JetBrains.Annotations/2019.1.3": {"type": "package", "compile": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}, "runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}}, "Microsoft.AspNetCore.Authentication/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.1.0", "Microsoft.AspNetCore.DataProtection": "2.1.0", "Microsoft.AspNetCore.Http": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0", "Microsoft.Extensions.WebEncoders": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.Cookies/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Cookies.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Cookies.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.Core/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.OAuth/2.0.3": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication": "2.0.3", "Newtonsoft.Json": "10.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.OAuth.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.OAuth.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/2.0.3": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.OAuth": "2.0.3", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "2.1.4"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authorization/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Cors/2.0.2": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.0.2", "Microsoft.Extensions.Configuration.Abstractions": "2.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.0.1", "Microsoft.Extensions.Options": "2.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Cryptography.Internal/2.1.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.1.0"}, "compile": {"lib/netcoreapp2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.DataProtection/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.1.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.1.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0", "Microsoft.Win32.Registry": "4.5.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Security.Principal.Windows": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.1.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.Hosting.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.0", "Microsoft.Extensions.Configuration.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.AspNetCore.WebUtilities": "2.1.0", "Microsoft.Extensions.ObjectPool": "2.1.0", "Microsoft.Extensions.Options": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Extensions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Features/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Identity/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Cookies": "2.1.0", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "2.1.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.0", "Microsoft.Extensions.Identity.Core": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Identity.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.WebUtilities/2.1.0": {"type": "package", "dependencies": {"Microsoft.Net.Http.Headers": "2.1.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}}, "Microsoft.CSharp/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.0/Microsoft.CSharp.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.CSharp.dll": {}}}, "Microsoft.EntityFrameworkCore/2.2.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "2.2.0", "Microsoft.EntityFrameworkCore.Analyzers": "2.2.0", "Microsoft.Extensions.Caching.Memory": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Remotion.Linq": "2.2.0", "System.Collections.Immutable": "1.5.0", "System.ComponentModel.Annotations": "4.5.0", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Interactive.Async": "3.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Abstractions/2.2.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/2.2.0": {"type": "package"}, "Microsoft.EntityFrameworkCore.Relational/2.2.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Memory/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Binder/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.CommandLine/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.FileExtensions/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "3.1.2", "Microsoft.Extensions.FileProviders.Physical": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Json/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "3.1.2", "Microsoft.Extensions.Configuration.FileExtensions": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.UserSecrets/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Json": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"build/netstandard2.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.2": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Composite/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Embedded/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Physical/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "3.1.2", "Microsoft.Extensions.FileSystemGlobbing": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileSystemGlobbing/3.1.2": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Hosting.Abstractions/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.2", "Microsoft.Extensions.FileProviders.Abstractions": "3.1.2", "Microsoft.Extensions.Logging.Abstractions": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Identity.Core/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "2.1.0", "Microsoft.Extensions.Logging": "2.1.0", "Microsoft.Extensions.Options": "2.1.0", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Identity.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Identity.Core.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.2", "Microsoft.Extensions.Localization.Abstractions": "3.1.2", "Microsoft.Extensions.Logging.Abstractions": "3.1.2", "Microsoft.Extensions.Options": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization.Abstractions/3.1.2": {"type": "package", "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Binder": "3.1.2", "Microsoft.Extensions.DependencyInjection": "3.1.2", "Microsoft.Extensions.Logging.Abstractions": "3.1.2", "Microsoft.Extensions.Options": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/3.1.2": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.ObjectPool/2.1.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.2", "Microsoft.Extensions.Primitives": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/3.1.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.2", "Microsoft.Extensions.Configuration.Binder": "3.1.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.2", "Microsoft.Extensions.Options": "3.1.2"}, "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/3.1.2": {"type": "package", "compile": {"lib/netcoreapp3.1/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}}, "Microsoft.Extensions.WebEncoders/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/5.2.1": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0"}, "compile": {"lib/netstandard1.4/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.4/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/2.1.4": {"type": "package", "dependencies": {"System.Collections.Specialized": "4.3.0", "System.Diagnostics.Contracts": "4.3.0", "System.IdentityModel.Tokens.Jwt": "5.1.4", "System.Net.Http": "4.3.0"}, "compile": {"lib/netstandard1.4/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.4/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/2.1.4": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "2.1.4", "System.Dynamic.Runtime": "4.3.0"}, "compile": {"lib/netstandard1.4/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.4/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/5.2.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "5.2.1", "NETStandard.Library": "1.6.1", "Newtonsoft.Json": "10.0.1", "System.Collections": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Serialization.Xml": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"lib/netstandard1.4/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.4/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.Net.Http.Headers/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/2.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/Microsoft.Win32.Primitives.dll": {"related": ".xml"}}}, "Microsoft.Win32.Registry/4.5.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.5.0", "System.Security.Principal.Windows": "4.5.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "MySql.Data/8.0.11": {"type": "package", "dependencies": {"Google.Protobuf": "3.5.1", "System.Configuration.ConfigurationManager": "4.4.0", "System.Security.Permissions": "4.4.0", "System.Text.Encoding.CodePages": "4.0.1"}, "compile": {"lib/netcoreapp2.0/MySql.Data.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/MySql.Data.dll": {"related": ".xml"}}}, "MySql.Data.EntityFrameworkCore/8.0.11": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": "2.0.1", "MySql.Data": "8.0.11"}, "compile": {"lib/netstandard2.0/MySql.Data.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/MySql.Data.EntityFrameworkCore.dll": {"related": ".xml"}}}, "NETStandard.Library/1.6.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/12.0.1": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".pdb;.xml"}}}, "Nito.AsyncEx.Context/5.0.0": {"type": "package", "dependencies": {"Nito.AsyncEx.Tasks": "5.0.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Coordination/5.0.0": {"type": "package", "dependencies": {"Nito.AsyncEx.Tasks": "5.0.0", "Nito.Collections.Deque": "1.0.4", "Nito.Disposables": "2.0.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Tasks/5.0.0": {"type": "package", "dependencies": {"Nito.Disposables": "2.0.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}}, "Nito.Collections.Deque/1.0.4": {"type": "package", "compile": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"related": ".xml"}}}, "Nito.Disposables/2.0.0": {"type": "package", "dependencies": {"System.Collections.Immutable": "1.4.0"}, "compile": {"lib/netstandard2.0/Nito.Disposables.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Nito.Disposables.dll": {"related": ".pdb;.xml"}}}, "Remotion.Linq/2.2.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Linq.Queryable": "4.0.1", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.0/Remotion.Linq.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/Remotion.Linq.dll": {"related": ".xml"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/debian.8-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "debian.8-x64"}}}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/fedora.23-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "fedora.23-x64"}}}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/fedora.24-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "fedora.24-x64"}}}, "runtime.native.System/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/opensuse.13.2-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "opensuse.13.2-x64"}}}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/opensuse.42.1-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "opensuse.42.1-x64"}}}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.Apple.dylib": {"assetType": "native", "rid": "osx.10.10-x64"}}}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.OpenSsl.dylib": {"assetType": "native", "rid": "osx.10.10-x64"}}}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/rhel.7-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "rhel.7-x64"}}}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/ubuntu.14.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "ubuntu.14.04-x64"}}}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/ubuntu.16.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "ubuntu.16.04-x64"}}}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/ubuntu.16.10-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "ubuntu.16.10-x64"}}}, "System.AppContext/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.6/System.AppContext.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.AppContext.dll": {}}}, "System.Buffers/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {"related": ".xml"}}}, "System.Collections.Concurrent/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.Concurrent.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.Concurrent.dll": {}}}, "System.Collections.Immutable/1.7.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.NonGeneric.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.NonGeneric.dll": {}}}, "System.Collections.Specialized/4.3.0": {"type": "package", "dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.Specialized.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.Specialized.dll": {}}}, "System.ComponentModel/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ComponentModel.dll": {}}}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.ComponentModel.Primitives.dll": {}}}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.5/System.ComponentModel.TypeConverter.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.ComponentModel.TypeConverter.dll": {}}}, "System.Configuration.ConfigurationManager/4.4.0": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "4.4.0"}, "compile": {"ref/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {}}}, "System.Console/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Console.dll": {"related": ".xml"}}}, "System.Diagnostics.Contracts/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Diagnostics.Contracts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.Diagnostics.Contracts.dll": {}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Diagnostics.Debug.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/4.5.0": {"type": "package", "compile": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Diagnostics.Tools.dll": {"related": ".xml"}}}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Diagnostics.Tracing.dll": {"related": ".xml"}}}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Dynamic.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Dynamic.Runtime.dll": {}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Globalization.dll": {"related": ".xml"}}}, "System.Globalization.Calendars/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Globalization.Calendars.dll": {"related": ".xml"}}}, "System.Globalization.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IdentityModel.Tokens.Jwt/5.2.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "5.2.1", "NETStandard.Library": "1.6.1", "Newtonsoft.Json": "10.0.1"}, "compile": {"lib/netstandard1.4/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.4/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.Interactive.Async/3.2.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Interactive.Async.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Interactive.Async.dll": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.Compression.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.IO.Compression.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "dependencies": {"System.Buffers": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.Compression.ZipFile.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.Compression.ZipFile.dll": {}}}, "System.IO.FileSystem/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.dll": {"related": ".xml"}}}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Linq.Dynamic.Core/1.0.19": {"type": "package", "compile": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}}, "System.Linq.Expressions/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {}}}, "System.Linq.Queryable/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Linq.Queryable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Linq.Queryable.dll": {}}}, "System.Net.Http/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Net.Http.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Net.Http.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Net.Http.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Net.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Net.Primitives.dll": {"related": ".xml"}}}, "System.Net.Sockets/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Net.Sockets.dll": {"related": ".xml"}}}, "System.ObjectModel/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.ObjectModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {}}}, "System.Private.DataContractSerialization/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}, "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Private.DataContractSerialization.dll": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.1/System.Reflection.Emit.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Extensions.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Resources.ResourceManager.dll": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Extensions.dll": {"related": ".xml"}}}, "System.Runtime.Handles/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Handles.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netcoreapp1.1/System.Runtime.InteropServices.dll": {}}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}, "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtime": {"lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.Loader/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Loader.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Runtime.Loader.dll": {}}}, "System.Runtime.Numerics/4.3.0": {"type": "package", "dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.1/System.Runtime.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.Numerics.dll": {}}}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {}}}, "System.Runtime.Serialization.Xml/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Serialization.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.Serialization.Xml.dll": {}}}, "System.Security.AccessControl/4.5.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Security.Principal.Windows": "4.5.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Claims/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Security.Claims.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Security.Claims.dll": {}}}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {}}, "runtimeTargets": {"runtimes/osx/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Csp.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Csp.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.6/_._": {}}, "runtime": {"lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll": {"assetType": "runtime", "rid": "unix"}}}, "System.Security.Cryptography.Pkcs/4.5.0": {"type": "package", "dependencies": {"System.Security.Cryptography.Cng": "4.5.0"}, "compile": {"ref/netcoreapp2.1/_._": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Security.Cryptography.Pkcs.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Primitives.dll": {}}, "runtime": {"lib/netstandard1.3/System.Security.Cryptography.Primitives.dll": {}}}, "System.Security.Cryptography.ProtectedData/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.4/System.Security.Cryptography.X509Certificates.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Xml/4.5.0": {"type": "package", "dependencies": {"System.Security.Cryptography.Pkcs": "4.5.0", "System.Security.Permissions": "4.5.0"}, "compile": {"ref/netstandard2.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.Xml.dll": {}}}, "System.Security.Permissions/4.5.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.5.0"}, "compile": {"ref/netstandard2.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Permissions.dll": {}}}, "System.Security.Principal/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Security.Principal.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.Security.Principal.dll": {}}}, "System.Security.Principal.Windows/4.5.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "2.0.0"}, "compile": {"ref/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.Extensions.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/4.5.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netcoreapp1.1/System.Text.RegularExpressions.dll": {}}, "runtime": {"lib/netstandard1.6/System.Text.RegularExpressions.dll": {}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"lib/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.Threading.Timer/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.2/System.Threading.Timer.dll": {"related": ".xml"}}}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Xml.ReaderWriter.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.ReaderWriter.dll": {}}}, "System.Xml.XDocument/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Xml.XDocument.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.XDocument.dll": {}}}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.XmlDocument.dll": {}}}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.XmlSerializer.dll": {}}}, "Volo.Abp.Auditing/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Data": "0.15.0", "Volo.Abp.Json": "0.15.0", "Volo.Abp.MultiTenancy.Abstractions": "0.15.0", "Volo.Abp.Security": "0.15.0", "Volo.Abp.Threading": "0.15.0", "Volo.Abp.Timing": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Auditing.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Auditing.dll": {"related": ".pdb"}}}, "Volo.Abp.AuditLogging.Domain/0.4.1": {"type": "package", "dependencies": {"Volo.Abp.AuditLogging.Domain.Shared": "0.4.1", "Volo.Abp.Auditing": "0.4.1", "Volo.Abp.Ddd.Domain": "0.4.1"}, "compile": {"lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.dll": {"related": ".pdb"}}}, "Volo.Abp.AuditLogging.Domain.Shared/0.4.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.4.1", "Volo.Abp.Localization": "0.4.1"}, "compile": {"lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.AuditLogging.EntityFrameworkCore/0.4.1": {"type": "package", "dependencies": {"Volo.Abp.AuditLogging.Domain": "0.4.1", "Volo.Abp.EntityFrameworkCore": "0.4.1"}, "compile": {"lib/netstandard2.0/Volo.Abp.AuditLogging.EntityFrameworkCore.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.AuditLogging.EntityFrameworkCore.dll": {"related": ".pdb"}}}, "Volo.Abp.Authorization/0.14.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "2.2.0", "Volo.Abp.Localization.Abstractions": "0.14.0", "Volo.Abp.Security": "0.14.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Authorization.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Authorization.dll": {"related": ".pdb"}}}, "Volo.Abp.AutoMapper/0.6.0": {"type": "package", "dependencies": {"AutoMapper": "6.2.2", "Volo.Abp.ObjectMapping": "0.6.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.AutoMapper.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.AutoMapper.dll": {"related": ".pdb"}}}, "Volo.Abp.BackgroundJobs/0.4.1": {"type": "package", "dependencies": {"Volo.Abp.BackgroundJobs.Abstractions": "0.4.1", "Volo.Abp.BackgroundWorkers": "0.4.1", "Volo.Abp.Core": "0.4.1", "Volo.Abp.Guids": "0.4.1", "Volo.Abp.Timing": "0.4.1"}, "compile": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.dll": {"related": ".pdb"}}}, "Volo.Abp.BackgroundJobs.Abstractions/0.4.1": {"type": "package", "dependencies": {"Volo.Abp.Json": "0.4.1"}, "compile": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.dll": {"related": ".pdb"}}}, "Volo.Abp.BackgroundJobs.Domain/0.4.1": {"type": "package", "dependencies": {"Volo.Abp.AutoMapper": "0.4.1", "Volo.Abp.BackgroundJobs": "0.4.1", "Volo.Abp.BackgroundJobs.Domain.Shared": "0.4.1", "Volo.Abp.Ddd.Domain": "0.4.1"}, "compile": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.dll": {"related": ".pdb"}}}, "Volo.Abp.BackgroundJobs.Domain.Shared/0.4.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.4.1"}, "compile": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.BackgroundJobs.EntityFrameworkCore/0.4.1": {"type": "package", "dependencies": {"System.Linq.Dynamic.Core": "1.0.8.9", "Volo.Abp.BackgroundJobs.Domain": "0.4.1", "Volo.Abp.EntityFrameworkCore": "0.4.1"}, "compile": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.EntityFrameworkCore.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.EntityFrameworkCore.dll": {"related": ".pdb"}}}, "Volo.Abp.BackgroundWorkers/0.4.1": {"type": "package", "dependencies": {"Volo.Abp.Threading": "0.4.1"}, "compile": {"lib/netstandard2.0/Volo.Abp.BackgroundWorkers.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.BackgroundWorkers.dll": {"related": ".pdb"}}}, "Volo.Abp.Caching/0.15.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Memory": "2.2.0", "Volo.Abp.MultiTenancy.Abstractions": "0.15.0", "Volo.Abp.Serialization": "0.15.0", "Volo.Abp.Threading": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Caching.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Caching.dll": {"related": ".pdb"}}}, "Volo.Abp.Core/2.4.0": {"type": "package", "dependencies": {"ConfigureAwait.Fody": "3.3.1", "JetBrains.Annotations": "2019.1.3", "Microsoft.Extensions.Configuration.CommandLine": "3.1.2", "Microsoft.Extensions.Configuration.EnvironmentVariables": "3.1.2", "Microsoft.Extensions.Configuration.UserSecrets": "3.1.2", "Microsoft.Extensions.DependencyInjection": "3.1.2", "Microsoft.Extensions.Hosting.Abstractions": "3.1.2", "Microsoft.Extensions.Localization": "3.1.2", "Microsoft.Extensions.Logging": "3.1.2", "Microsoft.Extensions.Options": "3.1.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "3.1.2", "Nito.AsyncEx.Context": "5.0.0", "Nito.AsyncEx.Coordination": "5.0.0", "System.Collections.Immutable": "1.7.0", "System.ComponentModel.Annotations": "4.7.0", "System.Linq.Dynamic.Core": "1.0.19", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Core.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Core.dll": {"related": ".pdb"}}}, "Volo.Abp.Data/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Data.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Data.dll": {"related": ".pdb"}}}, "Volo.Abp.Ddd.Domain/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Auditing": "0.15.0", "Volo.Abp.Data": "0.15.0", "Volo.Abp.EventBus": "0.15.0", "Volo.Abp.Guids": "0.15.0", "Volo.Abp.MultiTenancy.Abstractions": "0.15.0", "Volo.Abp.ObjectMapping": "0.15.0", "Volo.Abp.Threading": "0.15.0", "Volo.Abp.Timing": "0.15.0", "Volo.Abp.Uow": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll": {"related": ".pdb"}}}, "Volo.Abp.Emailing/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Settings": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Emailing.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Emailing.dll": {"related": ".pdb"}}}, "Volo.Abp.EntityFrameworkCore/0.15.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "2.2.0", "Microsoft.EntityFrameworkCore.Relational": "2.2.0", "Volo.Abp.Ddd.Domain": "0.15.0", "Volo.Abp.Json": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.EntityFrameworkCore.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.EntityFrameworkCore.dll": {"related": ".pdb"}}}, "Volo.Abp.EntityFrameworkCore.MySQL/0.3.6": {"type": "package", "dependencies": {"MySql.Data.EntityFrameworkCore": "8.0.11", "Volo.Abp.EntityFrameworkCore": "0.3.6"}, "compile": {"lib/netstandard2.0/Volo.Abp.EntityFrameworkCore.MySQL.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.EntityFrameworkCore.MySQL.dll": {"related": ".pdb"}}}, "Volo.Abp.EventBus/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.EventBus.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.EventBus.dll": {"related": ".pdb"}}}, "Volo.Abp.FeatureManagement.Domain/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Caching": "0.15.0", "Volo.Abp.Ddd.Domain": "0.15.0", "Volo.Abp.FeatureManagement.Domain.Shared": "0.15.0", "Volo.Abp.Features": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.dll": {"related": ".pdb"}}}, "Volo.Abp.FeatureManagement.Domain.Shared/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Localization": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.FeatureManagement.EntityFrameworkCore/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.EntityFrameworkCore": "0.15.0", "Volo.Abp.FeatureManagement.Domain": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.EntityFrameworkCore.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.FeatureManagement.EntityFrameworkCore.dll": {"related": ".pdb"}}}, "Volo.Abp.Features/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Localization.Abstractions": "0.15.0", "Volo.Abp.MultiTenancy.Abstractions": "0.15.0", "Volo.Abp.Validation": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Features.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Features.dll": {"related": ".pdb"}}}, "Volo.Abp.Guids/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Guids.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Guids.dll": {"related": ".pdb"}}}, "Volo.Abp.Identity.Domain/0.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Identity": "2.1.0", "Volo.Abp.Ddd.Domain": "0.3.0", "Volo.Abp.Identity.Domain.Shared": "0.3.0", "Volo.Abp.PermissionManagement.Domain": "0.3.0", "Volo.Abp.Users.Domain": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Identity.Domain.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Identity.Domain.dll": {"related": ".pdb"}}}, "Volo.Abp.Identity.Domain.Shared/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Localization": "0.3.0", "Volo.Abp.Users.Domain.Shared": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.Identity.EntityFrameworkCore/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Identity.Domain": "0.3.0", "Volo.Abp.Users.EntityFrameworkCore": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Identity.EntityFrameworkCore.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Identity.EntityFrameworkCore.dll": {"related": ".pdb"}}}, "Volo.Abp.IdentityServer.Domain/0.6.0": {"type": "package", "dependencies": {"IdentityServer4": "2.2.0", "IdentityServer4.AspNetIdentity": "2.1.0", "Volo.Abp.AutoMapper": "0.6.0", "Volo.Abp.Identity.Domain": "0.6.0", "Volo.Abp.IdentityServer.Domain.Shared": "0.6.0", "Volo.Abp.Security": "0.6.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.dll": {"related": ".pdb"}}}, "Volo.Abp.IdentityServer.Domain.Shared/0.6.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.6.0", "Volo.Abp.Localization": "0.6.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.IdentityServer.EntityFrameworkCore/0.6.0": {"type": "package", "dependencies": {"Volo.Abp.EntityFrameworkCore": "0.6.0", "Volo.Abp.IdentityServer.Domain": "0.6.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.IdentityServer.EntityFrameworkCore.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.IdentityServer.EntityFrameworkCore.dll": {"related": ".pdb"}}}, "Volo.Abp.Json/0.15.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1", "Volo.Abp.Timing": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Json.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Json.dll": {"related": ".pdb"}}}, "Volo.Abp.Localization/0.3.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "11.0.2", "Volo.Abp.Core": "0.3.0", "Volo.Abp.VirtualFileSystem": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Localization.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Localization.dll": {"related": ".pdb"}}}, "Volo.Abp.Localization.Abstractions/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll": {"related": ".pdb"}}}, "Volo.Abp.MultiTenancy.Abstractions/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Data": "0.15.0", "Volo.Abp.Security": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"related": ".pdb"}}}, "Volo.Abp.ObjectExtending/2.4.0": {"type": "package", "dependencies": {"ConfigureAwait.Fody": "3.3.1", "Volo.Abp.Core": "2.4.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.ObjectExtending.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.ObjectExtending.dll": {"related": ".pdb"}}}, "Volo.Abp.ObjectMapping/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.ObjectMapping.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.ObjectMapping.dll": {"related": ".pdb"}}}, "Volo.Abp.PermissionManagement.Domain/0.14.0": {"type": "package", "dependencies": {"Volo.Abp.Authorization": "0.14.0", "Volo.Abp.Caching": "0.14.0", "Volo.Abp.Ddd.Domain": "0.14.0", "Volo.Abp.Json": "0.14.0", "Volo.Abp.PermissionManagement.Domain.Shared": "0.14.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.dll": {"related": ".pdb"}}}, "Volo.Abp.PermissionManagement.Domain.Identity/0.13.0": {"type": "package", "dependencies": {"Volo.Abp.Identity.Domain.Shared": "0.13.0", "Volo.Abp.PermissionManagement.Domain": "0.13.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Identity.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Identity.dll": {"related": ".pdb"}}}, "Volo.Abp.PermissionManagement.Domain.IdentityServer/0.14.0": {"type": "package", "dependencies": {"Volo.Abp.IdentityServer.Domain.Shared": "0.14.0", "Volo.Abp.PermissionManagement.Domain": "0.14.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.IdentityServer.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.IdentityServer.dll": {"related": ".pdb"}}}, "Volo.Abp.PermissionManagement.EntityFrameworkCore/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.EntityFrameworkCore": "0.3.0", "Volo.Abp.PermissionManagement.Domain": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.EntityFrameworkCore.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.PermissionManagement.EntityFrameworkCore.dll": {"related": ".pdb"}}}, "Volo.Abp.Security/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Security.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Security.dll": {"related": ".pdb"}}}, "Volo.Abp.Serialization/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Serialization.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Serialization.dll": {"related": ".pdb"}}}, "Volo.Abp.SettingManagement.Domain/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Ddd.Domain": "0.3.0", "Volo.Abp.SettingManagement.Domain.Shared": "0.3.0", "Volo.Abp.Settings": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.dll": {"related": ".pdb"}}}, "Volo.Abp.SettingManagement.Domain.Shared/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.SettingManagement.EntityFrameworkCore/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.EntityFrameworkCore": "0.3.0", "Volo.Abp.SettingManagement.Domain": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.SettingManagement.EntityFrameworkCore.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.SettingManagement.EntityFrameworkCore.dll": {"related": ".pdb"}}}, "Volo.Abp.Settings/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Settings.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Settings.dll": {"related": ".pdb"}}}, "Volo.Abp.TenantManagement.Domain/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.AutoMapper": "0.3.0", "Volo.Abp.Data": "0.3.0", "Volo.Abp.Ddd.Domain": "0.3.0", "Volo.Abp.MultiTenancy.Abstractions": "0.3.0", "Volo.Abp.TenantManagement.Domain.Shared": "0.3.0", "Volo.Abp.UI": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.TenantManagement.Domain.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.TenantManagement.Domain.dll": {"related": ".pdb"}}}, "Volo.Abp.TenantManagement.Domain.Shared/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Localization": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.TenantManagement.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.TenantManagement.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.TenantManagement.EntityFrameworkCore/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.EntityFrameworkCore": "0.3.0", "Volo.Abp.TenantManagement.Domain": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.TenantManagement.EntityFrameworkCore.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.TenantManagement.EntityFrameworkCore.dll": {"related": ".pdb"}}}, "Volo.Abp.Threading/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Threading.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Threading.dll": {"related": ".pdb"}}}, "Volo.Abp.Timing/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Timing.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Timing.dll": {"related": ".pdb"}}}, "Volo.Abp.UI/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Localization": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.UI.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.UI.dll": {"related": ".pdb"}}}, "Volo.Abp.Uow/0.15.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.15.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Uow.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Uow.dll": {"related": ".pdb"}}}, "Volo.Abp.Users.Abstractions/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.MultiTenancy.Abstractions": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Users.Abstractions.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Users.Abstractions.dll": {"related": ".pdb"}}}, "Volo.Abp.Users.Domain/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Ddd.Domain": "0.3.0", "Volo.Abp.Users.Abstractions": "0.3.0", "Volo.Abp.Users.Domain.Shared": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Users.Domain.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Users.Domain.dll": {"related": ".pdb"}}}, "Volo.Abp.Users.Domain.Shared/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.dll": {"related": ".pdb"}}}, "Volo.Abp.Users.EntityFrameworkCore/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.EntityFrameworkCore": "0.3.0", "Volo.Abp.Users.Domain": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Users.EntityFrameworkCore.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Users.EntityFrameworkCore.dll": {"related": ".pdb"}}}, "Volo.Abp.Validation/0.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Validation.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Validation.dll": {"related": ".pdb"}}}, "Volo.Abp.VirtualFileSystem/0.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Composite": "2.1.0", "Microsoft.Extensions.FileProviders.Physical": "2.1.0", "Volo.Abp.Core": "0.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll": {"related": ".pdb"}}}, "TSZ.ServiceBase.FileStorageCenter.Domain/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"TSZ.ServiceBase.FileStorageCenter.Domain.Shared": "1.0.0", "Volo.Abp.AuditLogging.Domain": "(, )", "Volo.Abp.BackgroundJobs.Domain": "(, )", "Volo.Abp.Emailing": "(, )", "Volo.Abp.FeatureManagement.Domain": "(, )", "Volo.Abp.Identity.Domain": "(, )", "Volo.Abp.IdentityServer.Domain": "(, )", "Volo.Abp.PermissionManagement.Domain.Identity": "(, )", "Volo.Abp.PermissionManagement.Domain.IdentityServer": "(, )", "Volo.Abp.SettingManagement.Domain": "(, )", "Volo.Abp.TenantManagement.Domain": "(, )"}, "compile": {"bin/placeholder/TSZ.ServiceBase.FileStorageCenter.Domain.dll": {}}, "runtime": {"bin/placeholder/TSZ.ServiceBase.FileStorageCenter.Domain.dll": {}}}, "TSZ.ServiceBase.FileStorageCenter.Domain.Shared/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "(, )", "Volo.Abp.AuditLogging.Domain.Shared": "(, )", "Volo.Abp.BackgroundJobs.Domain.Shared": "(, )", "Volo.Abp.FeatureManagement.Domain.Shared": "(, )", "Volo.Abp.Identity.Domain.Shared": "(, )", "Volo.Abp.IdentityServer.Domain.Shared": "(, )", "Volo.Abp.Localization": "(, )", "Volo.Abp.ObjectExtending": "(, )", "Volo.Abp.PermissionManagement.Domain.Shared": "(, )", "Volo.Abp.SettingManagement.Domain.Shared": "(, )", "Volo.Abp.TenantManagement.Domain.Shared": "(, )", "Volo.Abp.Validation": "(, )"}, "compile": {"bin/placeholder/TSZ.ServiceBase.FileStorageCenter.Domain.Shared.dll": {}}, "runtime": {"bin/placeholder/TSZ.ServiceBase.FileStorageCenter.Domain.Shared.dll": {}}}}}, "libraries": {"AutoMapper/6.2.2": {"sha512": "eGKcnJdoF5rYMWPUaSPtlWdvua9xw4hnQN5kM/X4AXLGKghrtPIaMv+RYoA3sj0kyZ3X6wZ1Qj67F+u0pCCPaw==", "type": "package", "path": "automapper/6.2.2", "files": [".nupkg.metadata", ".signature.p7s", "automapper.6.2.2.nupkg.sha512", "automapper.nuspec", "lib/net40/AutoMapper.dll", "lib/net40/AutoMapper.xml", "lib/net45/AutoMapper.dll", "lib/net45/AutoMapper.xml", "lib/netstandard1.1/AutoMapper.dll", "lib/netstandard1.1/AutoMapper.xml", "lib/netstandard1.3/AutoMapper.dll", "lib/netstandard1.3/AutoMapper.xml"]}, "ConfigureAwait.Fody/3.3.1": {"sha512": "R9PQYf0AT4RBZcUXm22xWkCpSmNHdTzQ0dOyLIsxIK6dwXH4S9pY/rZdXU/63i8vZvSzZ99sB1kP7xer8MCe6w==", "type": "package", "path": "configureawait.fody/3.3.1", "files": [".nupkg.metadata", ".signature.p7s", "build/ConfigureAwait.Fody.props", "configureawait.fody.3.3.1.nupkg.sha512", "configureawait.fody.nuspec", "lib/net452/ConfigureAwait.dll", "lib/net452/ConfigureAwait.xml", "lib/netstandard2.0/ConfigureAwait.dll", "lib/netstandard2.0/ConfigureAwait.xml", "weaver/ConfigureAwait.Fody.dll", "weaver/ConfigureAwait.Fody.xcf"]}, "Fody/6.0.2": {"sha512": "Oq9dxiHWkw/tPKu9LSmfp6uuCNDZLDkxwHB0sJuwyQRSmvFSB3Ab54WgCQWIsGDO9Z+va9expamqkKpFfdd1sQ==", "type": "package", "path": "fody/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "build/Fody.targets", "fody.6.0.2.nupkg.sha512", "fody.nuspec", "netclassictask/Fody.dll", "netclassictask/FodyCommon.dll", "netclassictask/FodyHelpers.dll", "netclassictask/FodyIsolated.dll", "netclassictask/Mono.Cecil.Pdb.dll", "netclassictask/Mono.Cecil.Pdb.pdb", "netclassictask/Mono.Cecil.Rocks.dll", "netclassictask/Mono.Cecil.Rocks.pdb", "netclassictask/Mono.Cecil.dll", "netclassictask/Mono.Cecil.pdb", "netstandardtask/Fody.dll", "netstandardtask/FodyCommon.dll", "netstandardtask/FodyHelpers.dll", "netstandardtask/FodyIsolated.dll", "netstandardtask/Mono.Cecil.Pdb.dll", "netstandardtask/Mono.Cecil.Pdb.pdb", "netstandardtask/Mono.Cecil.Rocks.dll", "netstandardtask/Mono.Cecil.Rocks.pdb", "netstandardtask/Mono.Cecil.dll", "netstandardtask/Mono.Cecil.pdb"]}, "Google.Protobuf/3.5.1": {"sha512": "f2k1VNaB9bfvEsvARzzEL1TZiIpL33KKK3JMH7UANlPlJVptuvsk4qpBZEnz0pORWZOdUHlVwMQuUzFqjJYCxA==", "type": "package", "path": "google.protobuf/3.5.1", "files": [".nupkg.metadata", ".signature.p7s", "google.protobuf.3.5.1.nupkg.sha512", "google.protobuf.nuspec", "lib/net45/Google.Protobuf.dll", "lib/net45/Google.Protobuf.xml", "lib/netstandard1.0/Google.Protobuf.dll", "lib/netstandard1.0/Google.Protobuf.xml"]}, "IdentityModel/3.6.1": {"sha512": "JvpnyoP49cF16sImPcdM0A7keVGs+mpG7KCnJ3c1zOwsxXexhN/hqkZua6Cr/9KZcVw6/wsRQfEgY3QMsjsQpw==", "type": "package", "path": "identitymodel/3.6.1", "files": [".nupkg.metadata", ".signature.p7s", "identitymodel.3.6.1.nupkg.sha512", "identitymodel.nuspec", "lib/net452/IdentityModel.dll", "lib/net452/IdentityModel.xml", "lib/net461/IdentityModel.dll", "lib/net461/IdentityModel.xml", "lib/netstandard1.4/IdentityModel.dll", "lib/netstandard1.4/IdentityModel.xml", "lib/netstandard2.0/IdentityModel.dll", "lib/netstandard2.0/IdentityModel.xml"]}, "IdentityServer4/2.2.0": {"sha512": "diozvMQnHiZH4EwcG3X1Si/AIq1yN7hLuQ0Dv3lr1uCuUBRYJV2HUEICnFv4ZLWVzQePI0Q1elC8cT7X8PHssg==", "type": "package", "path": "identityserver4/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "identityserver4.2.2.0.nupkg.sha512", "identityserver4.nuspec", "lib/netstandard2.0/IdentityServer4.dll", "lib/netstandard2.0/IdentityServer4.pdb", "lib/netstandard2.0/IdentityServer4.xml"]}, "IdentityServer4.AspNetIdentity/2.1.0": {"sha512": "r1QXwLW11a4+BxTy/2GUFzAYLAulKQhMSDs/fFCai6r1oH0btv6Dw7lvDNyh8wn0oeonMQvxxzJKzLbMqYZcNw==", "type": "package", "path": "identityserver4.aspnetidentity/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "identityserver4.aspnetidentity.2.1.0.nupkg.sha512", "identityserver4.aspnetidentity.nuspec", "lib/netstandard2.0/IdentityServer4.AspNetIdentity.dll", "lib/netstandard2.0/IdentityServer4.AspNetIdentity.pdb", "lib/netstandard2.0/IdentityServer4.AspNetIdentity.xml"]}, "JetBrains.Annotations/2019.1.3": {"sha512": "E0x48BwZJKoNMNCekWGKsV4saQS89lf58ydT2szseV44CMYIbaHXjc7+305WLw6up3ibZN9yH6QdGSZo5tQhLg==", "type": "package", "path": "jetbrains.annotations/2019.1.3", "files": [".nupkg.metadata", ".signature.p7s", "jetbrains.annotations.2019.1.3.nupkg.sha512", "jetbrains.annotations.nuspec", "lib/net20/JetBrains.Annotations.dll", "lib/net20/JetBrains.Annotations.xml", "lib/netstandard1.0/JetBrains.Annotations.deps.json", "lib/netstandard1.0/JetBrains.Annotations.dll", "lib/netstandard1.0/JetBrains.Annotations.xml", "lib/netstandard2.0/JetBrains.Annotations.deps.json", "lib/netstandard2.0/JetBrains.Annotations.dll", "lib/netstandard2.0/JetBrains.Annotations.xml", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.dll", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.xml"]}, "Microsoft.AspNetCore.Authentication/2.1.0": {"sha512": "uYRxYAmYL0gEBihFo2cJ1HggHKs9XwhPTQglH5FyplqBQLFy2Oy1VFyTDlmLAue4chj+1qKt0Ida/J1crtRiMA==", "type": "package", "path": "microsoft.aspnetcore.authentication/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.xml", "microsoft.aspnetcore.authentication.2.1.0.nupkg.sha512", "microsoft.aspnetcore.authentication.nuspec"]}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.0": {"sha512": "7hfl2DQoATexr0OVw8PwJSNqnu9gsbSkuHkwmHdss5xXCuY2nIfsTjj2NoKeGtp6N94ECioAP78FUfFOMj+TTg==", "type": "package", "path": "microsoft.aspnetcore.authentication.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.xml", "microsoft.aspnetcore.authentication.abstractions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.authentication.abstractions.nuspec"]}, "Microsoft.AspNetCore.Authentication.Cookies/2.1.0": {"sha512": "Fo7DLSWI3RywisVegE30LmgreLU0/mo6rn+RO/4UIlckqZJAmxkzSufzYxJd0bVIQu28ytfEYh6lPxLJRcTzZQ==", "type": "package", "path": "microsoft.aspnetcore.authentication.cookies/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Cookies.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Cookies.xml", "microsoft.aspnetcore.authentication.cookies.2.1.0.nupkg.sha512", "microsoft.aspnetcore.authentication.cookies.nuspec"]}, "Microsoft.AspNetCore.Authentication.Core/2.1.0": {"sha512": "NKbmBzPW2zTaZLNKkCIL7LMpr4XfXVOPJ5SNzikTe2PX3juLkupb/5oTF45wiw5srUbU6QD0cY9u3jgYUELwnQ==", "type": "package", "path": "microsoft.aspnetcore.authentication.core/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.xml", "microsoft.aspnetcore.authentication.core.2.1.0.nupkg.sha512", "microsoft.aspnetcore.authentication.core.nuspec"]}, "Microsoft.AspNetCore.Authentication.OAuth/2.0.3": {"sha512": "cuQYTKA/u5/uY5Wxu8OyLRUAt3U7kGyBmHwHvWz83vseBsnvso+qp+KX9syr/5PfkEvzub1RCvctB2NCRz5vNQ==", "type": "package", "path": "microsoft.aspnetcore.authentication.oauth/2.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.OAuth.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.OAuth.xml", "microsoft.aspnetcore.authentication.oauth.2.0.3.nupkg.sha512", "microsoft.aspnetcore.authentication.oauth.nuspec"]}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/2.0.3": {"sha512": "2gRCExy0c2jrrsbwbjEeqK3o0ZEaVOxl8u9X+43GbWG3UDh4Zt8agGu+PhMxUO05j4Z2u5RBZVYHIGoZnuniMA==", "type": "package", "path": "microsoft.aspnetcore.authentication.openidconnect/2.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.xml", "microsoft.aspnetcore.authentication.openidconnect.2.0.3.nupkg.sha512", "microsoft.aspnetcore.authentication.openidconnect.nuspec"]}, "Microsoft.AspNetCore.Authorization/2.2.0": {"sha512": "/L0W8H3jMYWyaeA9gBJqS/tSWBegP9aaTM0mjRhxTttBY9z4RVDRYJ2CwPAmAXIuPr3r1sOw+CS8jFVRGHRezQ==", "type": "package", "path": "microsoft.aspnetcore.authorization/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Cors/2.0.2": {"sha512": "+mmN69VlbJL4q82C5wKCMdSnxjk4VfcCysDcLIXmNYloI9PY1VqOcHD1A3E6EaPB0ncEb4J+Fg71XO6HToIl7w==", "type": "package", "path": "microsoft.aspnetcore.cors/2.0.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Cors.xml", "microsoft.aspnetcore.cors.2.0.2.nupkg.sha512", "microsoft.aspnetcore.cors.nuspec"]}, "Microsoft.AspNetCore.Cryptography.Internal/2.1.0": {"sha512": "oqKoj5d+zR7/R2T03jq69fJ5w48dJ3Yw+XO3ORJGIV7Vd4eJhwvAOpEQKC3vWyQIKZWEkndIxaWMbfODJY/vsQ==", "type": "package", "path": "microsoft.aspnetcore.cryptography.internal/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.xml", "microsoft.aspnetcore.cryptography.internal.2.1.0.nupkg.sha512", "microsoft.aspnetcore.cryptography.internal.nuspec"]}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/2.1.0": {"sha512": "13LH47Gj9SAY8BReIypBOQKT1Imlt1h11mbhk5rtI0s7R0jti7pjxZUKZEiVK8o9NUAGfL0yF6A2PXLRImBZ+w==", "type": "package", "path": "microsoft.aspnetcore.cryptography.keyderivation/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll", "lib/netcoreapp2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.xml", "microsoft.aspnetcore.cryptography.keyderivation.2.1.0.nupkg.sha512", "microsoft.aspnetcore.cryptography.keyderivation.nuspec"]}, "Microsoft.AspNetCore.DataProtection/2.1.0": {"sha512": "G+UoMHL0xiyFh30wkL7Bv/XL6eugTAKYhLPS53k1/Me1bYRwOOw+8VL/q0ppq3/yMzpHX+MkExaCTDlYl48FgA==", "type": "package", "path": "microsoft.aspnetcore.dataprotection/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.xml", "microsoft.aspnetcore.dataprotection.2.1.0.nupkg.sha512", "microsoft.aspnetcore.dataprotection.nuspec"]}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.1.0": {"sha512": "2+HVDhUqrnV9+EJNEewSy+Gk4hOVPzLPMpFDZI7kuH7NWxtbNkI6A6gT5lO2/kEPMyM8/iLWtohbOwjpC9rHVw==", "type": "package", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.xml", "microsoft.aspnetcore.dataprotection.abstractions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.dataprotection.abstractions.nuspec"]}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.0": {"sha512": "1TQgBfd/NPZLR2o/h6l5Cml2ZCF5hsyV4h9WEwWwAIavrbdTnaNozGGcTOd4AOgQvogMM9UM1ajflm9Cwd0jLQ==", "type": "package", "path": "microsoft.aspnetcore.hosting.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.xml", "microsoft.aspnetcore.hosting.abstractions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.hosting.abstractions.nuspec"]}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.0": {"sha512": "YTKMi2vHX6P+WHEVpW/DS+eFHnwivCSMklkyamcK1ETtc/4j8H3VR0kgW8XIBqukNxhD8k5wYt22P7PhrWSXjQ==", "type": "package", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.xml", "microsoft.aspnetcore.hosting.server.abstractions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.hosting.server.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http/2.1.0": {"sha512": "eAPryjDRH41EYY2sOMHCu+tHXLI6PUN1AsOPKst6GbiIoMi8wJCiPcE4h9418tKje1oUzmMc2Iz8fFPPVamfaw==", "type": "package", "path": "microsoft.aspnetcore.http/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.xml", "microsoft.aspnetcore.http.2.1.0.nupkg.sha512", "microsoft.aspnetcore.http.nuspec"]}, "Microsoft.AspNetCore.Http.Abstractions/2.1.0": {"sha512": "vbFDyKsSYBnxl3+RABtN79b0vsTcG66fDY8vD6Nqvu9uLtSej70Q5NcbGlnN6bJpZci5orSdgFTHMhBywivDPg==", "type": "package", "path": "microsoft.aspnetcore.http.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.xml", "microsoft.aspnetcore.http.abstractions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.http.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http.Extensions/2.1.0": {"sha512": "M8Gk5qrUu5nFV7yE3SZgATt/5B1a5Qs8ZnXXeO/Pqu68CEiBHJWc10sdGdO5guc3zOFdm7H966mVnpZtEX4vSA==", "type": "package", "path": "microsoft.aspnetcore.http.extensions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.xml", "microsoft.aspnetcore.http.extensions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.http.extensions.nuspec"]}, "Microsoft.AspNetCore.Http.Features/2.1.0": {"sha512": "UmkUePxRjsQW0j5euFFscBwjvTu25b8+qIK/2fI3GvcqQ+mkwgbWNAT8b/Gkoei1m2bTWC07lSdutuRDPPLcJA==", "type": "package", "path": "microsoft.aspnetcore.http.features/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.xml", "microsoft.aspnetcore.http.features.2.1.0.nupkg.sha512", "microsoft.aspnetcore.http.features.nuspec"]}, "Microsoft.AspNetCore.Identity/2.1.0": {"sha512": "Kfmeba2ruAJT0K9Xcpw0GXh1nmEO9K3a6iIuQle9YtrnHRayc+m4D/OvfzzhCh1GYqgX2vJU/s7b+u6glDuqAA==", "type": "package", "path": "microsoft.aspnetcore.identity/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Identity.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Identity.xml", "microsoft.aspnetcore.identity.2.1.0.nupkg.sha512", "microsoft.aspnetcore.identity.nuspec"]}, "Microsoft.AspNetCore.WebUtilities/2.1.0": {"sha512": "xBy8JGXQ3tVSYzLl/LtN3c9EeB75khFSB2Kw2HWmF+McU0Ltva7R4JBRH0Rb4LgkcjYyyJdf+09PZalQFwsT+Q==", "type": "package", "path": "microsoft.aspnetcore.webutilities/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll", "lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.xml", "microsoft.aspnetcore.webutilities.2.1.0.nupkg.sha512", "microsoft.aspnetcore.webutilities.nuspec"]}, "Microsoft.CSharp/4.3.0": {"sha512": "P+MBhIM0YX+JqROuf7i306ZLJEjQYA9uUyRDE+OqwUI5sh41e2ZbPQV3LfAPh+29cmceE1pUffXsGfR4eMY3KA==", "type": "package", "path": "microsoft.csharp/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.3.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "Microsoft.EntityFrameworkCore/2.2.0": {"sha512": "xfDHe+J94oz2d+ESDU8u+96iSfsiPwpgYGPRSp/bato0Ekjz5kYs61u9mS0GN5t8n/wxc5P3uEJm1x7TfROxhQ==", "type": "package", "path": "microsoft.entityframeworkcore/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.dll", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.2.2.0.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/2.2.0": {"sha512": "/js/H09B8MQGoPDFzJoaAealyL66g4vKf7DVcdBYcxgKztkttjZbRzSWKF9PZZFyfBl9Ia/BiStM70t7kjgMpg==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.2.2.0.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/2.2.0": {"sha512": "/1rw3toRCal80Ch51lIjuf/0WfW9ZGB4eRY1y6GOcOoOvnMXXXt+4xMRw/0k0kIwHsWUNXUpw73jf40/Pe+ZYA==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "microsoft.entityframeworkcore.analyzers.2.2.0.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/2.2.0": {"sha512": "CRJHHp/GqmXByeeODgbDgMDWId9kKT9TDRzHF8voWHFNTajBQl0U4jaMrHxjIP6hJ2olF8n+5GYmQv+v3bUVKQ==", "type": "package", "path": "microsoft.entityframeworkcore.relational/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.2.2.0.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/2.2.0": {"sha512": "spsJkYo8gGJapaxTSQFN/wqA+ghpJMLwB4ZyTB+fSdpd7AmMFP/YSpIcGmczcw4KggpxLGhLk7lCkSIlgvHaqQ==", "type": "package", "path": "microsoft.extensions.caching.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.2.2.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec"]}, "Microsoft.Extensions.Caching.Memory/2.2.0": {"sha512": "yFs44RzB2Pzfoj4uk+mEz3MTTQKyeWb8gDhv5GyVPfHnLv0eQhGwzbw/5WpxAcVyOgG/H3/0ULY6g0/7/B+r7w==", "type": "package", "path": "microsoft.extensions.caching.memory/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.2.2.0.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec"]}, "Microsoft.Extensions.Configuration/3.1.2": {"sha512": "BxwRSBab309SYMCDCFyB6eSc7FnX5m9kOJQHw2IQIyb5PEtpfslhscTw63Gwhl3dPnaM1VGFXIyI0BVgpiLgOw==", "type": "package", "path": "microsoft.extensions.configuration/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.3.1.2.nupkg.sha512", "microsoft.extensions.configuration.nuspec"]}, "Microsoft.Extensions.Configuration.Abstractions/3.1.2": {"sha512": "xmfdVdazTslWJ8od7uNS9QSPqn1wBC84RLprPrFS20EdAqd3lV0g0IZAitYbCiiICpjktnhzbUb85aLHNZ3RQw==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.3.1.2.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec"]}, "Microsoft.Extensions.Configuration.Binder/3.1.2": {"sha512": "IWrc9/voGki2pc5g8bRXIqs+P50tXOjNf47qgFKSu/pL50InRuXxh/nj5AG9Po8YRpvT/bYIUk3XQqHH7yUg5w==", "type": "package", "path": "microsoft.extensions.configuration.binder/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.3.1.2.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec"]}, "Microsoft.Extensions.Configuration.CommandLine/3.1.2": {"sha512": "voJoqXRGnfB4nw3LRL6QY/rnYdaZA2vFCMbRzPP2iE13XbZciJhGR0fvTsDKyFA9VfQJzPgIj+F/0S0Zqdxt4w==", "type": "package", "path": "microsoft.extensions.configuration.commandline/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.3.1.2.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/3.1.2": {"sha512": "AdpldFyx0PlwbgUatdj89jC/n5n2dqXP865NwM77bu9LcnEmWX37QTSAKeZT5a13c6G5MQ1v4lAGz2a9wpPf/g==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.3.1.2.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec"]}, "Microsoft.Extensions.Configuration.FileExtensions/3.1.2": {"sha512": "itZcJUf2IRa4e4NFTQgR4JUmwndEU5O0isQsKkZXHiHXwExgLkX9D09R7YIK272w3jpKaYw/DejntAC7zzsNWg==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.3.1.2.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec"]}, "Microsoft.Extensions.Configuration.Json/3.1.2": {"sha512": "AQ64UCqGXP2UTfkVE1fdUJdlKEEiFZIOXpt6lkIz+tunuJWh1m+/eIppY+ITgjoKsfFc2W8ldNonIntHx5ybNQ==", "type": "package", "path": "microsoft.extensions.configuration.json/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.3.1.2.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec"]}, "Microsoft.Extensions.Configuration.UserSecrets/3.1.2": {"sha512": "1YOSOCsOUPVbcTDU+bifeT1z5dtMdwaZywWdKYocDBHwoILmxRsIKYS8CWVYPIggliHPEwjonNZfpdIktJkNiw==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "build/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.3.1.2.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec"]}, "Microsoft.Extensions.DependencyInjection/3.1.2": {"sha512": "e+F6/wjQPOFHB/sGWTAqC8FX/C6+J<PERSON><PERSON>WLpryXTAQYIS3tr+17lByADdP9Y6RtxfJ4kW/IPrU6RuxTNZNdAQz1A==", "type": "package", "path": "microsoft.extensions.dependencyinjection/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net461/Microsoft.Extensions.DependencyInjection.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.xml", "lib/netcoreapp3.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netcoreapp3.1/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.3.1.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.2": {"sha512": "/CZzCSCIm/3FFoXHfUpsfov/Elo268dcvlz/MMINT0vPgphqg2pAgdEn/EjCDyoAT3NAmsRmjfGwBumC1uYJtA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.3.1.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec"]}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.2": {"sha512": "O9+N6KuA7kiPIYpdgRFFveKRyI3X2hLgdqdEwQki0MOA5XtCVOkxz8O+6CK1+b1a7Y1TildGfx3i+h/652vyHg==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.3.1.2.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec"]}, "Microsoft.Extensions.FileProviders.Composite/2.1.0": {"sha512": "vc2oy4OlHaTH3hMZlMGymk4vzi93Nz3T1T+kMHBpoq9Ka287DHP8vZxK9MhgTsnNYP7S4hrqiUxsc5wEPyDYGg==", "type": "package", "path": "microsoft.extensions.fileproviders.composite/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.xml", "microsoft.extensions.fileproviders.composite.2.1.0.nupkg.sha512", "microsoft.extensions.fileproviders.composite.nuspec"]}, "Microsoft.Extensions.FileProviders.Embedded/1.0.0": {"sha512": "mMCI3/BLXAyBCDneqOI4ohETd0IXjbXZdoiCm1dYdnOdV193ByEOCFQ6/Vn9RVdU5UlC4Nn1P4J5Df7pXG/vGg==", "type": "package", "path": "microsoft.extensions.fileproviders.embedded/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard1.0/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/netstandard1.0/Microsoft.Extensions.FileProviders.Embedded.xml", "microsoft.extensions.fileproviders.embedded.1.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.embedded.nuspec"]}, "Microsoft.Extensions.FileProviders.Physical/3.1.2": {"sha512": "lAbbwKapBfwGLVcfNL7TG4o7zRqLOiVY7/ylUKgnh2D9TotJ2riXzNTmQldksIYrmcJcNrq/WBalTpawSSAkJg==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.3.1.2.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec"]}, "Microsoft.Extensions.FileSystemGlobbing/3.1.2": {"sha512": "/EgWQ25z1RZgzAT6JSOJiuQ/PFm53Kl1H3kzAgs5JIh52UaD1RmxW1znv5VbQlTfgLzRSeQZ3aPPA9SNakuSzw==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.3.1.2.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec"]}, "Microsoft.Extensions.Hosting.Abstractions/3.1.2": {"sha512": "oafEsTwy1ed4zycyjzgFet58IW3I/aC1uUJTWpFAs3mjkQzW52LqVlE/9AAW2IVk4q8EPw+GPsiFB17qYksNXQ==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.3.1.2.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec"]}, "Microsoft.Extensions.Identity.Core/2.1.0": {"sha512": "ANFe9HR9thO9CU9x8mc7h1uw75Aqz5uDe7xgGM6t2iBITEidLNhULzqPidI0xV2Mg54pdPMA8/vtS5FRLCQWgQ==", "type": "package", "path": "microsoft.extensions.identity.core/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Identity.Core.dll", "lib/netstandard2.0/Microsoft.Extensions.Identity.Core.xml", "microsoft.extensions.identity.core.2.1.0.nupkg.sha512", "microsoft.extensions.identity.core.nuspec"]}, "Microsoft.Extensions.Localization/3.1.2": {"sha512": "s4y5gt/N1rcNnTXSPd5g+Z6EyjCKzsXmQcFfP7s6GKXDstOS+KGoCQEnQCdlGlz8Jin/v8Ep+40yA1ngvNFvZw==", "type": "package", "path": "microsoft.extensions.localization/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Localization.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Localization.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.xml", "microsoft.extensions.localization.3.1.2.nupkg.sha512", "microsoft.extensions.localization.nuspec"]}, "Microsoft.Extensions.Localization.Abstractions/3.1.2": {"sha512": "IWbB3w1ITn2KwQcVZYSoHzNGjEqObJGnwPZS2O6BE9SbkaHh7PLatyM78LjIIgyuEg/m1HP3t/GuRCUH15CliQ==", "type": "package", "path": "microsoft.extensions.localization.abstractions/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Localization.Abstractions.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Localization.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.xml", "microsoft.extensions.localization.abstractions.3.1.2.nupkg.sha512", "microsoft.extensions.localization.abstractions.nuspec"]}, "Microsoft.Extensions.Logging/3.1.2": {"sha512": "AIIRgKamzEqJNLZsHd37VogFX9YpxgrBmf/b3dznD7S0qjxWQnAs498ulLV1n6AKJ8XVjTCBNzsvQiSwCa7dIw==", "type": "package", "path": "microsoft.extensions.logging/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Logging.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.3.1.2.nupkg.sha512", "microsoft.extensions.logging.nuspec"]}, "Microsoft.Extensions.Logging.Abstractions/3.1.2": {"sha512": "cIXPw7VVX3fON4uuHwJFmCi0qDl8uY75xZMKB2oM3In0ZDEB1Ee+p9Ti1DSw92AwRtJ2Zh+QG1joTBednJMzvA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.3.1.2.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec"]}, "Microsoft.Extensions.ObjectPool/2.1.0": {"sha512": "tIbO45cohqexTJPXBubpwluycDT+6OWy2m7PukG37XMrtQ6Zv4AnoLrgUTaCmpWihSs5RZHKvThiAJFcBlR3AA==", "type": "package", "path": "microsoft.extensions.objectpool/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.2.1.0.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.Extensions.Options/3.1.2": {"sha512": "6F4anwt9yMlnQckac2etjrasRFyqZNIp46p+i9qVps0DXNsOLZIKRkqq4AY4FlxXxKeGkEJC7M77RQEkvd3p8Q==", "type": "package", "path": "microsoft.extensions.options/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Options.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.3.1.2.nupkg.sha512", "microsoft.extensions.options.nuspec"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/3.1.2": {"sha512": "NJRuISEgTUh3/ehm0mwGx1FhepKQuUxfMm0BKJ0b8UNABuDaXFLtlV/5Bd9hT5vmeZTGGB4hvM02uRaCiSACNw==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.3.1.2.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec"]}, "Microsoft.Extensions.Primitives/3.1.2": {"sha512": "WGtoFWY9yc9HGMG6ObDNQPz9dBP+xz/GqFe2dKjdE/cSdXFEKxCFTyYCzL/e8kxVkc/Bq9qjOsXRWydvn0g9Uw==", "type": "package", "path": "microsoft.extensions.primitives/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netcoreapp3.1/Microsoft.Extensions.Primitives.dll", "lib/netcoreapp3.1/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.3.1.2.nupkg.sha512", "microsoft.extensions.primitives.nuspec"]}, "Microsoft.Extensions.WebEncoders/2.1.0": {"sha512": "YwzwLadahZiEbqbDoAlKhAq/szBL05ZmIIlrfHjLsF9M3zppmWRKAOGjFalmwONxbZMl3OHUoAiPKShtieV0KA==", "type": "package", "path": "microsoft.extensions.webencoders/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll", "lib/netstandard2.0/Microsoft.Extensions.WebEncoders.xml", "microsoft.extensions.webencoders.2.1.0.nupkg.sha512", "microsoft.extensions.webencoders.nuspec"]}, "Microsoft.IdentityModel.Logging/5.2.1": {"sha512": "kbmvhMYu5OZXWN3toFXhrj3bSN7B+ZzNWzAZQ4Ofp5x2srk9ZCYFljETGS5faxzPwGd5+7W4WZlAfOI7QvzhlA==", "type": "package", "path": "microsoft.identitymodel.logging/5.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Logging.dll", "lib/net45/Microsoft.IdentityModel.Logging.xml", "lib/net451/Microsoft.IdentityModel.Logging.dll", "lib/net451/Microsoft.IdentityModel.Logging.xml", "lib/netstandard1.4/Microsoft.IdentityModel.Logging.dll", "lib/netstandard1.4/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.5.2.1.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/2.1.4": {"sha512": "9aefRN9sL8XZo90Aix88IHHpAvfBl6UOiYpcKHiXbCYE2nB+zA3B8dZdNMOUH4pqXdnpYrHRDQZ2k7A4/CUgTQ==", "type": "package", "path": "microsoft.identitymodel.protocols/2.1.4", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.dll", "lib/net45/Microsoft.IdentityModel.Protocols.xml", "lib/net451/Microsoft.IdentityModel.Protocols.dll", "lib/net451/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard1.4/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard1.4/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.2.1.4.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/2.1.4": {"sha512": "LF8JcG9BqGRwVjhu/IebuZQer6TJGDv2uxNnmg2Zkzh/d+MIC1ShsC1U3U7pVaw03SKyXmCgYm+JG0WM0mcOUw==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/2.1.4", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net451/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net451/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard1.4/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard1.4/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.2.1.4.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/5.2.1": {"sha512": "wVWYpXfE6gpkSrIRNo5TDVC7ss6KPTTrmT6x8ysHiZRIMSRAZ8TyHEAmpdATBBPNRmnlevyAs4CK6nPfDpCTqw==", "type": "package", "path": "microsoft.identitymodel.tokens/5.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Tokens.dll", "lib/net45/Microsoft.IdentityModel.Tokens.xml", "lib/net451/Microsoft.IdentityModel.Tokens.dll", "lib/net451/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard1.4/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard1.4/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.5.2.1.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.Net.Http.Headers/2.1.0": {"sha512": "c08F7C7BGgmjrq9cr7382pBRhcimBx24YOv4M4gtzMIuVKmxGoRr5r9A2Hke9v7Nx7zKKCysk6XpuZasZX4oeg==", "type": "package", "path": "microsoft.net.http.headers/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Net.Http.Headers.dll", "lib/netstandard2.0/Microsoft.Net.Http.Headers.xml", "microsoft.net.http.headers.2.1.0.nupkg.sha512", "microsoft.net.http.headers.nuspec"]}, "Microsoft.NETCore.Platforms/2.0.0": {"sha512": "VdLJOCXhZaEMY7Hm2GKiULmn7IEPFE4XC5LPSfBVCUIA8YLZVh846gtfBJalsPQF2PlzdD7ecX7DZEulJ402ZQ==", "type": "package", "path": "microsoft.netcore.platforms/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.2.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.Win32.Primitives/4.3.0": {"sha512": "9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "type": "package", "path": "microsoft.win32.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/Microsoft.Win32.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.win32.primitives.4.3.0.nupkg.sha512", "microsoft.win32.primitives.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/de/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/es/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/it/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "Microsoft.Win32.Registry/4.5.0": {"sha512": "+FWlwd//+Tt56316p00hVePBCouXyEzT86Jb3+AuRotTND0IYn0OO3obs1gnQEs/txEnt+rF2JBGLItTG+Be6A==", "type": "package", "path": "microsoft.win32.registry/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "microsoft.win32.registry.4.5.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "useSharedDesignerContext.txt", "version.txt"]}, "MySql.Data/8.0.11": {"sha512": "P5zKcnM2SVtdHD6gfReymJguIYLhNL5Og8Qm5TC9MEsSupsAYrRu4AB4nsbalrjqqv8VHBqVREWsXg9lY+q03A==", "type": "package", "path": "mysql.data/8.0.11", "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/MySql.Data.dll", "lib/net452/MySql.Data.xml", "lib/netcoreapp2.0/MySql.Data.dll", "lib/netcoreapp2.0/MySql.Data.xml", "lib/netstandard1.6/MySql.Data.dll", "lib/netstandard1.6/MySql.Data.xml", "lib/netstandard2.0/MySql.Data.dll", "lib/netstandard2.0/MySql.Data.xml", "mysql.data.8.0.11.nupkg.sha512", "mysql.data.nuspec"]}, "MySql.Data.EntityFrameworkCore/8.0.11": {"sha512": "5YHuc4dli0/D8glw9M+IXZpeEBpnybYLkz6JNxzsVFGJKfuhWlOwa4E7SWQs0lhmyheVGEpx02BgGYVBFaXs2A==", "type": "package", "path": "mysql.data.entityframeworkcore/8.0.11", "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/MySql.Data.EntityFrameworkCore.dll", "lib/net452/MySql.Data.EntityFrameworkCore.xml", "lib/netstandard1.6/MySql.Data.EntityFrameworkCore.dll", "lib/netstandard1.6/MySql.Data.EntityFrameworkCore.xml", "lib/netstandard2.0/MySql.Data.EntityFrameworkCore.dll", "lib/netstandard2.0/MySql.Data.EntityFrameworkCore.xml", "mysql.data.entityframeworkcore.8.0.11.nupkg.sha512", "mysql.data.entityframeworkcore.nuspec"]}, "NETStandard.Library/1.6.1": {"sha512": "WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "type": "package", "path": "netstandard.library/1.6.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "netstandard.library.1.6.1.nupkg.sha512", "netstandard.library.nuspec"]}, "Newtonsoft.Json/12.0.1": {"sha512": "pBR3wCgYWZGiaZDYP+HHYnalVnPJlpP1q55qvVb+adrDHmFMDc1NAKio61xTwftK3Pw5h7TZJPJEEVMd6ty8rg==", "type": "package", "path": "newtonsoft.json/12.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.pdb", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.pdb", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.pdb", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.pdb", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.pdb", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.pdb", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.pdb", "lib/netstandard2.0/Newtonsoft.Json.xml", "lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.dll", "lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.pdb", "lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.xml", "lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.dll", "lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.pdb", "lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.xml", "newtonsoft.json.12.0.1.nupkg.sha512", "newtonsoft.json.nuspec"]}, "Nito.AsyncEx.Context/5.0.0": {"sha512": "Qnth1Ye+QSLg8P3fSFYzk7ue6oUUHQcKpLitgAig8xRFqTK5W1KTlfxF/Z8Eo0BuqZ17a5fAGtXrdKJsLqivZw==", "type": "package", "path": "nito.asyncex.context/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard1.3/Nito.AsyncEx.Context.dll", "lib/netstandard1.3/Nito.AsyncEx.Context.xml", "lib/netstandard2.0/Nito.AsyncEx.Context.dll", "lib/netstandard2.0/Nito.AsyncEx.Context.xml", "nito.asyncex.context.5.0.0.nupkg.sha512", "nito.asyncex.context.nuspec"]}, "Nito.AsyncEx.Coordination/5.0.0": {"sha512": "kjauyO8UMo/FGZO/M8TdjXB8ZlBPFOiRN8yakThaGQbYOywazQ0kGZ39SNr2gNNzsTxbZOUudBMYNo+IrtscbA==", "type": "package", "path": "nito.asyncex.coordination/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard1.3/Nito.AsyncEx.Coordination.dll", "lib/netstandard1.3/Nito.AsyncEx.Coordination.xml", "lib/netstandard2.0/Nito.AsyncEx.Coordination.dll", "lib/netstandard2.0/Nito.AsyncEx.Coordination.xml", "nito.asyncex.coordination.5.0.0.nupkg.sha512", "nito.asyncex.coordination.nuspec"]}, "Nito.AsyncEx.Tasks/5.0.0": {"sha512": "ZtvotignafOLteP4oEjVcF3k2L8h73QUCaFpVKWbU+EOlW/I+JGkpMoXIl0rlwPcDmR84RxzggLRUNMaWlOosA==", "type": "package", "path": "nito.asyncex.tasks/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard1.3/Nito.AsyncEx.Tasks.dll", "lib/netstandard1.3/Nito.AsyncEx.Tasks.xml", "lib/netstandard2.0/Nito.AsyncEx.Tasks.dll", "lib/netstandard2.0/Nito.AsyncEx.Tasks.xml", "nito.asyncex.tasks.5.0.0.nupkg.sha512", "nito.asyncex.tasks.nuspec"]}, "Nito.Collections.Deque/1.0.4": {"sha512": "yGDKqCQ61i97MyfEUYG6+ln5vxpx11uA5M9+VV9B7stticbFm19YMI/G9w4AFYVBj5PbPi138P8IovkMFAL0Aw==", "type": "package", "path": "nito.collections.deque/1.0.4", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard1.0/Nito.Collections.Deque.dll", "lib/netstandard1.0/Nito.Collections.Deque.xml", "lib/netstandard2.0/Nito.Collections.Deque.dll", "lib/netstandard2.0/Nito.Collections.Deque.xml", "nito.collections.deque.1.0.4.nupkg.sha512", "nito.collections.deque.nuspec"]}, "Nito.Disposables/2.0.0": {"sha512": "ExJl/jTjegSLHGcwnmaYaI5xIlrefAsVdeLft7VLtXI2+W5irihiu36LizWvlaUpzY1/llo+YSh09uSHMu2VFw==", "type": "package", "path": "nito.disposables/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard1.0/Nito.Disposables.dll", "lib/netstandard1.0/Nito.Disposables.pdb", "lib/netstandard1.0/Nito.Disposables.xml", "lib/netstandard2.0/Nito.Disposables.dll", "lib/netstandard2.0/Nito.Disposables.pdb", "lib/netstandard2.0/Nito.Disposables.xml", "nito.disposables.2.0.0.nupkg.sha512", "nito.disposables.nuspec"]}, "Remotion.Linq/2.2.0": {"sha512": "fK/76UmpC0FXBlGDFVPLJHQlDLYnGC+XY3eoDgCgbtrhi0vzbXDQ3n/IYHhqSKqXQfGw/u04A1drWs7rFVkRjw==", "type": "package", "path": "remotion.linq/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/Remotion.Linq.XML", "lib/net35/Remotion.Linq.dll", "lib/net40/Remotion.Linq.XML", "lib/net40/Remotion.Linq.dll", "lib/net45/Remotion.Linq.XML", "lib/net45/Remotion.Linq.dll", "lib/netstandard1.0/Remotion.Linq.dll", "lib/netstandard1.0/Remotion.Linq.xml", "lib/portable-net45+win+wpa81+wp80/Remotion.Linq.dll", "lib/portable-net45+win+wpa81+wp80/Remotion.Linq.xml", "remotion.linq.2.2.0.nupkg.sha512", "remotion.linq.nuspec"]}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "type": "package", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/debian.8-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "type": "package", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/fedora.23-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "type": "package", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/fedora.24-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.native.System/4.3.0": {"sha512": "c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "type": "package", "path": "runtime.native.system/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.4.3.0.nupkg.sha512", "runtime.native.system.nuspec"]}, "runtime.native.System.IO.Compression/4.3.0": {"sha512": "INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "type": "package", "path": "runtime.native.system.io.compression/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.io.compression.4.3.0.nupkg.sha512", "runtime.native.system.io.compression.nuspec"]}, "runtime.native.System.Net.Http/4.3.0": {"sha512": "ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "type": "package", "path": "runtime.native.system.net.http/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.net.http.4.3.0.nupkg.sha512", "runtime.native.system.net.http.nuspec"]}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"sha512": "DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "type": "package", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "runtime.native.system.security.cryptography.apple.nuspec"]}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "type": "package", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.native.system.security.cryptography.openssl.nuspec"]}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "type": "package", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/opensuse.13.2-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "type": "package", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/opensuse.42.1-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"sha512": "kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "type": "package", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.nuspec", "runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.Apple.dylib"]}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "type": "package", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.OpenSsl.dylib"]}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "type": "package", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/rhel.7-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "type": "package", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/ubuntu.14.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "type": "package", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/ubuntu.16.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "type": "package", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/ubuntu.16.10-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "System.AppContext/4.3.0": {"sha512": "fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "type": "package", "path": "system.appcontext/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.AppContext.dll", "lib/net463/System.AppContext.dll", "lib/netcore50/System.AppContext.dll", "lib/netstandard1.6/System.AppContext.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.AppContext.dll", "ref/net463/System.AppContext.dll", "ref/netstandard/_._", "ref/netstandard1.3/System.AppContext.dll", "ref/netstandard1.3/System.AppContext.xml", "ref/netstandard1.3/de/System.AppContext.xml", "ref/netstandard1.3/es/System.AppContext.xml", "ref/netstandard1.3/fr/System.AppContext.xml", "ref/netstandard1.3/it/System.AppContext.xml", "ref/netstandard1.3/ja/System.AppContext.xml", "ref/netstandard1.3/ko/System.AppContext.xml", "ref/netstandard1.3/ru/System.AppContext.xml", "ref/netstandard1.3/zh-hans/System.AppContext.xml", "ref/netstandard1.3/zh-hant/System.AppContext.xml", "ref/netstandard1.6/System.AppContext.dll", "ref/netstandard1.6/System.AppContext.xml", "ref/netstandard1.6/de/System.AppContext.xml", "ref/netstandard1.6/es/System.AppContext.xml", "ref/netstandard1.6/fr/System.AppContext.xml", "ref/netstandard1.6/it/System.AppContext.xml", "ref/netstandard1.6/ja/System.AppContext.xml", "ref/netstandard1.6/ko/System.AppContext.xml", "ref/netstandard1.6/ru/System.AppContext.xml", "ref/netstandard1.6/zh-hans/System.AppContext.xml", "ref/netstandard1.6/zh-hant/System.AppContext.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.AppContext.dll", "system.appcontext.4.3.0.nupkg.sha512", "system.appcontext.nuspec"]}, "System.Buffers/4.5.0": {"sha512": "pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "type": "package", "path": "system.buffers/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.0.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.Concurrent/4.3.0": {"sha512": "ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "type": "package", "path": "system.collections.concurrent/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Collections.Concurrent.dll", "lib/netstandard1.3/System.Collections.Concurrent.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.Concurrent.dll", "ref/netcore50/System.Collections.Concurrent.xml", "ref/netcore50/de/System.Collections.Concurrent.xml", "ref/netcore50/es/System.Collections.Concurrent.xml", "ref/netcore50/fr/System.Collections.Concurrent.xml", "ref/netcore50/it/System.Collections.Concurrent.xml", "ref/netcore50/ja/System.Collections.Concurrent.xml", "ref/netcore50/ko/System.Collections.Concurrent.xml", "ref/netcore50/ru/System.Collections.Concurrent.xml", "ref/netcore50/zh-hans/System.Collections.Concurrent.xml", "ref/netcore50/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.1/System.Collections.Concurrent.dll", "ref/netstandard1.1/System.Collections.Concurrent.xml", "ref/netstandard1.1/de/System.Collections.Concurrent.xml", "ref/netstandard1.1/es/System.Collections.Concurrent.xml", "ref/netstandard1.1/fr/System.Collections.Concurrent.xml", "ref/netstandard1.1/it/System.Collections.Concurrent.xml", "ref/netstandard1.1/ja/System.Collections.Concurrent.xml", "ref/netstandard1.1/ko/System.Collections.Concurrent.xml", "ref/netstandard1.1/ru/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.3/System.Collections.Concurrent.dll", "ref/netstandard1.3/System.Collections.Concurrent.xml", "ref/netstandard1.3/de/System.Collections.Concurrent.xml", "ref/netstandard1.3/es/System.Collections.Concurrent.xml", "ref/netstandard1.3/fr/System.Collections.Concurrent.xml", "ref/netstandard1.3/it/System.Collections.Concurrent.xml", "ref/netstandard1.3/ja/System.Collections.Concurrent.xml", "ref/netstandard1.3/ko/System.Collections.Concurrent.xml", "ref/netstandard1.3/ru/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hant/System.Collections.Concurrent.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.concurrent.4.3.0.nupkg.sha512", "system.collections.concurrent.nuspec"]}, "System.Collections.Immutable/1.7.0": {"sha512": "RVSM6wZUo6L2y6P3vN6gjUtyJ2IF2RVtrepF3J7nrDKfFQd5u/SnSUFclchYQis8/k5scHy9E+fVeKVQLnnkzw==", "type": "package", "path": "system.collections.immutable/1.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/System.Collections.Immutable.dll", "lib/netstandard1.0/System.Collections.Immutable.xml", "lib/netstandard1.3/System.Collections.Immutable.dll", "lib/netstandard1.3/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.dll", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.xml", "system.collections.immutable.1.7.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections.NonGeneric/4.3.0": {"sha512": "prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "type": "package", "path": "system.collections.nongeneric/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Collections.NonGeneric.dll", "lib/netstandard1.3/System.Collections.NonGeneric.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.xml", "ref/netstandard1.3/de/System.Collections.NonGeneric.xml", "ref/netstandard1.3/es/System.Collections.NonGeneric.xml", "ref/netstandard1.3/fr/System.Collections.NonGeneric.xml", "ref/netstandard1.3/it/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ja/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ko/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ru/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hans/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hant/System.Collections.NonGeneric.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.nongeneric.4.3.0.nupkg.sha512", "system.collections.nongeneric.nuspec"]}, "System.Collections.Specialized/4.3.0": {"sha512": "Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "type": "package", "path": "system.collections.specialized/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Collections.Specialized.dll", "lib/netstandard1.3/System.Collections.Specialized.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Collections.Specialized.dll", "ref/netstandard1.3/System.Collections.Specialized.dll", "ref/netstandard1.3/System.Collections.Specialized.xml", "ref/netstandard1.3/de/System.Collections.Specialized.xml", "ref/netstandard1.3/es/System.Collections.Specialized.xml", "ref/netstandard1.3/fr/System.Collections.Specialized.xml", "ref/netstandard1.3/it/System.Collections.Specialized.xml", "ref/netstandard1.3/ja/System.Collections.Specialized.xml", "ref/netstandard1.3/ko/System.Collections.Specialized.xml", "ref/netstandard1.3/ru/System.Collections.Specialized.xml", "ref/netstandard1.3/zh-hans/System.Collections.Specialized.xml", "ref/netstandard1.3/zh-hant/System.Collections.Specialized.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.specialized.4.3.0.nupkg.sha512", "system.collections.specialized.nuspec"]}, "System.ComponentModel/4.3.0": {"sha512": "VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "type": "package", "path": "system.componentmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ComponentModel.dll", "lib/netstandard1.3/System.ComponentModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ComponentModel.dll", "ref/netcore50/System.ComponentModel.xml", "ref/netcore50/de/System.ComponentModel.xml", "ref/netcore50/es/System.ComponentModel.xml", "ref/netcore50/fr/System.ComponentModel.xml", "ref/netcore50/it/System.ComponentModel.xml", "ref/netcore50/ja/System.ComponentModel.xml", "ref/netcore50/ko/System.ComponentModel.xml", "ref/netcore50/ru/System.ComponentModel.xml", "ref/netcore50/zh-hans/System.ComponentModel.xml", "ref/netcore50/zh-hant/System.ComponentModel.xml", "ref/netstandard1.0/System.ComponentModel.dll", "ref/netstandard1.0/System.ComponentModel.xml", "ref/netstandard1.0/de/System.ComponentModel.xml", "ref/netstandard1.0/es/System.ComponentModel.xml", "ref/netstandard1.0/fr/System.ComponentModel.xml", "ref/netstandard1.0/it/System.ComponentModel.xml", "ref/netstandard1.0/ja/System.ComponentModel.xml", "ref/netstandard1.0/ko/System.ComponentModel.xml", "ref/netstandard1.0/ru/System.ComponentModel.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.4.3.0.nupkg.sha512", "system.componentmodel.nuspec"]}, "System.ComponentModel.Annotations/4.7.0": {"sha512": "0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "type": "package", "path": "system.componentmodel.annotations/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.4.7.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Primitives/4.3.0": {"sha512": "j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "type": "package", "path": "system.componentmodel.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.ComponentModel.Primitives.dll", "lib/netstandard1.0/System.ComponentModel.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.ComponentModel.Primitives.dll", "ref/netstandard1.0/System.ComponentModel.Primitives.dll", "ref/netstandard1.0/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/de/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/es/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/fr/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/it/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ja/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ko/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ru/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.primitives.4.3.0.nupkg.sha512", "system.componentmodel.primitives.nuspec"]}, "System.ComponentModel.TypeConverter/4.3.0": {"sha512": "16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "type": "package", "path": "system.componentmodel.typeconverter/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.ComponentModel.TypeConverter.dll", "lib/net462/System.ComponentModel.TypeConverter.dll", "lib/netstandard1.0/System.ComponentModel.TypeConverter.dll", "lib/netstandard1.5/System.ComponentModel.TypeConverter.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.ComponentModel.TypeConverter.dll", "ref/net462/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.0/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.0/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/de/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/es/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/fr/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/it/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ja/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ko/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ru/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.5/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/de/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/es/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/fr/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/it/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ja/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ko/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ru/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/zh-hans/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/zh-hant/System.ComponentModel.TypeConverter.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "system.componentmodel.typeconverter.nuspec"]}, "System.Configuration.ConfigurationManager/4.4.0": {"sha512": "gWwQv/Ug1qWJmHCmN17nAbxJYmQBM/E94QxKLksvUiiKB1Ld3Sc/eK1lgmbSjDFxkQhVuayI/cGFZhpBSodLrg==", "type": "package", "path": "system.configuration.configurationmanager/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "ref/net461/System.Configuration.ConfigurationManager.dll", "ref/net461/System.Configuration.ConfigurationManager.xml", "ref/netstandard2.0/System.Configuration.ConfigurationManager.dll", "ref/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.4.4.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Console/4.3.0": {"sha512": "DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "type": "package", "path": "system.console/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Console.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Console.dll", "ref/netstandard1.3/System.Console.dll", "ref/netstandard1.3/System.Console.xml", "ref/netstandard1.3/de/System.Console.xml", "ref/netstandard1.3/es/System.Console.xml", "ref/netstandard1.3/fr/System.Console.xml", "ref/netstandard1.3/it/System.Console.xml", "ref/netstandard1.3/ja/System.Console.xml", "ref/netstandard1.3/ko/System.Console.xml", "ref/netstandard1.3/ru/System.Console.xml", "ref/netstandard1.3/zh-hans/System.Console.xml", "ref/netstandard1.3/zh-hant/System.Console.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.console.4.3.0.nupkg.sha512", "system.console.nuspec"]}, "System.Diagnostics.Contracts/4.3.0": {"sha512": "eelRRbnm+OloiQvp9CXS0ixjNQldjjkHO4iIkR5XH2VIP8sUB/SIpa1TdUW6/+HDcQ+MlhP3pNa1u5SbzYuWGA==", "type": "package", "path": "system.diagnostics.contracts/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Diagnostics.Contracts.dll", "lib/netstandard1.0/System.Diagnostics.Contracts.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Contracts.dll", "ref/netcore50/System.Diagnostics.Contracts.xml", "ref/netcore50/de/System.Diagnostics.Contracts.xml", "ref/netcore50/es/System.Diagnostics.Contracts.xml", "ref/netcore50/fr/System.Diagnostics.Contracts.xml", "ref/netcore50/it/System.Diagnostics.Contracts.xml", "ref/netcore50/ja/System.Diagnostics.Contracts.xml", "ref/netcore50/ko/System.Diagnostics.Contracts.xml", "ref/netcore50/ru/System.Diagnostics.Contracts.xml", "ref/netcore50/zh-hans/System.Diagnostics.Contracts.xml", "ref/netcore50/zh-hant/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/System.Diagnostics.Contracts.dll", "ref/netstandard1.0/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/de/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/es/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/fr/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/it/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/ja/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/ko/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/ru/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Contracts.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Diagnostics.Contracts.dll", "system.diagnostics.contracts.4.3.0.nupkg.sha512", "system.diagnostics.contracts.nuspec"]}, "System.Diagnostics.Debug/4.3.0": {"sha512": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "type": "package", "path": "system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.3.0.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Diagnostics.DiagnosticSource/4.5.0": {"sha512": "eIHRELiYDQvsMToML81QFkXEEYXUSUT2F28t1SGrevWqP+epFdw80SyAXIKTXOHrIEXReFOEnEr7XlGiC2GgOg==", "type": "package", "path": "system.diagnostics.diagnosticsource/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Diagnostics.DiagnosticSource.dll", "lib/net45/System.Diagnostics.DiagnosticSource.xml", "lib/net46/System.Diagnostics.DiagnosticSource.dll", "lib/net46/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.4.5.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.Tools/4.3.0": {"sha512": "UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "type": "package", "path": "system.diagnostics.tools/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Tools.dll", "ref/netcore50/System.Diagnostics.Tools.xml", "ref/netcore50/de/System.Diagnostics.Tools.xml", "ref/netcore50/es/System.Diagnostics.Tools.xml", "ref/netcore50/fr/System.Diagnostics.Tools.xml", "ref/netcore50/it/System.Diagnostics.Tools.xml", "ref/netcore50/ja/System.Diagnostics.Tools.xml", "ref/netcore50/ko/System.Diagnostics.Tools.xml", "ref/netcore50/ru/System.Diagnostics.Tools.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tools.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tools.xml", "ref/netstandard1.0/System.Diagnostics.Tools.dll", "ref/netstandard1.0/System.Diagnostics.Tools.xml", "ref/netstandard1.0/de/System.Diagnostics.Tools.xml", "ref/netstandard1.0/es/System.Diagnostics.Tools.xml", "ref/netstandard1.0/fr/System.Diagnostics.Tools.xml", "ref/netstandard1.0/it/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ja/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ko/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ru/System.Diagnostics.Tools.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Tools.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Tools.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.tools.4.3.0.nupkg.sha512", "system.diagnostics.tools.nuspec"]}, "System.Diagnostics.Tracing/4.3.0": {"sha512": "rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "type": "package", "path": "system.diagnostics.tracing/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Diagnostics.Tracing.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.xml", "ref/netcore50/de/System.Diagnostics.Tracing.xml", "ref/netcore50/es/System.Diagnostics.Tracing.xml", "ref/netcore50/fr/System.Diagnostics.Tracing.xml", "ref/netcore50/it/System.Diagnostics.Tracing.xml", "ref/netcore50/ja/System.Diagnostics.Tracing.xml", "ref/netcore50/ko/System.Diagnostics.Tracing.xml", "ref/netcore50/ru/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/System.Diagnostics.Tracing.dll", "ref/netstandard1.1/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/System.Diagnostics.Tracing.dll", "ref/netstandard1.2/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/System.Diagnostics.Tracing.dll", "ref/netstandard1.3/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/System.Diagnostics.Tracing.dll", "ref/netstandard1.5/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hant/System.Diagnostics.Tracing.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.tracing.4.3.0.nupkg.sha512", "system.diagnostics.tracing.nuspec"]}, "System.Dynamic.Runtime/4.3.0": {"sha512": "SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "type": "package", "path": "system.dynamic.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Dynamic.Runtime.dll", "lib/netstandard1.3/System.Dynamic.Runtime.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Dynamic.Runtime.dll", "ref/netcore50/System.Dynamic.Runtime.xml", "ref/netcore50/de/System.Dynamic.Runtime.xml", "ref/netcore50/es/System.Dynamic.Runtime.xml", "ref/netcore50/fr/System.Dynamic.Runtime.xml", "ref/netcore50/it/System.Dynamic.Runtime.xml", "ref/netcore50/ja/System.Dynamic.Runtime.xml", "ref/netcore50/ko/System.Dynamic.Runtime.xml", "ref/netcore50/ru/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hans/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.0/System.Dynamic.Runtime.dll", "ref/netstandard1.0/System.Dynamic.Runtime.xml", "ref/netstandard1.0/de/System.Dynamic.Runtime.xml", "ref/netstandard1.0/es/System.Dynamic.Runtime.xml", "ref/netstandard1.0/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.0/it/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.3/System.Dynamic.Runtime.dll", "ref/netstandard1.3/System.Dynamic.Runtime.xml", "ref/netstandard1.3/de/System.Dynamic.Runtime.xml", "ref/netstandard1.3/es/System.Dynamic.Runtime.xml", "ref/netstandard1.3/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.3/it/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Dynamic.Runtime.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Dynamic.Runtime.dll", "system.dynamic.runtime.4.3.0.nupkg.sha512", "system.dynamic.runtime.nuspec"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.Globalization.Calendars/4.3.0": {"sha512": "GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "type": "package", "path": "system.globalization.calendars/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Globalization.Calendars.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Globalization.Calendars.dll", "ref/netstandard1.3/System.Globalization.Calendars.dll", "ref/netstandard1.3/System.Globalization.Calendars.xml", "ref/netstandard1.3/de/System.Globalization.Calendars.xml", "ref/netstandard1.3/es/System.Globalization.Calendars.xml", "ref/netstandard1.3/fr/System.Globalization.Calendars.xml", "ref/netstandard1.3/it/System.Globalization.Calendars.xml", "ref/netstandard1.3/ja/System.Globalization.Calendars.xml", "ref/netstandard1.3/ko/System.Globalization.Calendars.xml", "ref/netstandard1.3/ru/System.Globalization.Calendars.xml", "ref/netstandard1.3/zh-hans/System.Globalization.Calendars.xml", "ref/netstandard1.3/zh-hant/System.Globalization.Calendars.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.calendars.4.3.0.nupkg.sha512", "system.globalization.calendars.nuspec"]}, "System.Globalization.Extensions/4.3.0": {"sha512": "FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "type": "package", "path": "system.globalization.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Globalization.Extensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.xml", "ref/netstandard1.3/de/System.Globalization.Extensions.xml", "ref/netstandard1.3/es/System.Globalization.Extensions.xml", "ref/netstandard1.3/fr/System.Globalization.Extensions.xml", "ref/netstandard1.3/it/System.Globalization.Extensions.xml", "ref/netstandard1.3/ja/System.Globalization.Extensions.xml", "ref/netstandard1.3/ko/System.Globalization.Extensions.xml", "ref/netstandard1.3/ru/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Globalization.Extensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll", "runtimes/win/lib/net46/System.Globalization.Extensions.dll", "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll", "system.globalization.extensions.4.3.0.nupkg.sha512", "system.globalization.extensions.nuspec"]}, "System.IdentityModel.Tokens.Jwt/5.2.1": {"sha512": "QwALOmIQnwYXO7SZuzHvp+aF9+E8kouJl2JDHe9hyV/Mqzl2KhOwZMlN+mhyIYo5ggHcIVa/LN7E46WD26OEEg==", "type": "package", "path": "system.identitymodel.tokens.jwt/5.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.IdentityModel.Tokens.Jwt.dll", "lib/net45/System.IdentityModel.Tokens.Jwt.xml", "lib/net451/System.IdentityModel.Tokens.Jwt.dll", "lib/net451/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard1.4/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard1.4/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.5.2.1.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.Interactive.Async/3.2.0": {"sha512": "C07p0dAA5lGqYUPiPCK3paR709gqS4aMDDsje0v0pvffwzLaxmsn5YQTfZbyNG5qrudPx+BCxTqISnncQ3wIoQ==", "type": "package", "path": "system.interactive.async/3.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.Interactive.Async.dll", "lib/net45/System.Interactive.Async.xml", "lib/net46/System.Interactive.Async.dll", "lib/net46/System.Interactive.Async.xml", "lib/netstandard1.0/System.Interactive.Async.dll", "lib/netstandard1.0/System.Interactive.Async.xml", "lib/netstandard1.3/System.Interactive.Async.dll", "lib/netstandard1.3/System.Interactive.Async.xml", "lib/netstandard2.0/System.Interactive.Async.dll", "lib/netstandard2.0/System.Interactive.Async.xml", "system.interactive.async.3.2.0.nupkg.sha512", "system.interactive.async.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.Compression/4.3.0": {"sha512": "YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "type": "package", "path": "system.io.compression/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.IO.Compression.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.xml", "ref/netcore50/de/System.IO.Compression.xml", "ref/netcore50/es/System.IO.Compression.xml", "ref/netcore50/fr/System.IO.Compression.xml", "ref/netcore50/it/System.IO.Compression.xml", "ref/netcore50/ja/System.IO.Compression.xml", "ref/netcore50/ko/System.IO.Compression.xml", "ref/netcore50/ru/System.IO.Compression.xml", "ref/netcore50/zh-hans/System.IO.Compression.xml", "ref/netcore50/zh-hant/System.IO.Compression.xml", "ref/netstandard1.1/System.IO.Compression.dll", "ref/netstandard1.1/System.IO.Compression.xml", "ref/netstandard1.1/de/System.IO.Compression.xml", "ref/netstandard1.1/es/System.IO.Compression.xml", "ref/netstandard1.1/fr/System.IO.Compression.xml", "ref/netstandard1.1/it/System.IO.Compression.xml", "ref/netstandard1.1/ja/System.IO.Compression.xml", "ref/netstandard1.1/ko/System.IO.Compression.xml", "ref/netstandard1.1/ru/System.IO.Compression.xml", "ref/netstandard1.1/zh-hans/System.IO.Compression.xml", "ref/netstandard1.1/zh-hant/System.IO.Compression.xml", "ref/netstandard1.3/System.IO.Compression.dll", "ref/netstandard1.3/System.IO.Compression.xml", "ref/netstandard1.3/de/System.IO.Compression.xml", "ref/netstandard1.3/es/System.IO.Compression.xml", "ref/netstandard1.3/fr/System.IO.Compression.xml", "ref/netstandard1.3/it/System.IO.Compression.xml", "ref/netstandard1.3/ja/System.IO.Compression.xml", "ref/netstandard1.3/ko/System.IO.Compression.xml", "ref/netstandard1.3/ru/System.IO.Compression.xml", "ref/netstandard1.3/zh-hans/System.IO.Compression.xml", "ref/netstandard1.3/zh-hant/System.IO.Compression.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll", "runtimes/win/lib/net46/System.IO.Compression.dll", "runtimes/win/lib/netstandard1.3/System.IO.Compression.dll", "system.io.compression.4.3.0.nupkg.sha512", "system.io.compression.nuspec"]}, "System.IO.Compression.ZipFile/4.3.0": {"sha512": "G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "type": "package", "path": "system.io.compression.zipfile/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.Compression.ZipFile.dll", "lib/netstandard1.3/System.IO.Compression.ZipFile.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.Compression.ZipFile.dll", "ref/netstandard1.3/System.IO.Compression.ZipFile.dll", "ref/netstandard1.3/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/de/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/es/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/fr/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/it/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ja/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ko/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ru/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/zh-hans/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/zh-hant/System.IO.Compression.ZipFile.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.compression.zipfile.4.3.0.nupkg.sha512", "system.io.compression.zipfile.nuspec"]}, "System.IO.FileSystem/4.3.0": {"sha512": "3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "type": "package", "path": "system.io.filesystem/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.xml", "ref/netstandard1.3/de/System.IO.FileSystem.xml", "ref/netstandard1.3/es/System.IO.FileSystem.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.xml", "ref/netstandard1.3/it/System.IO.FileSystem.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.4.3.0.nupkg.sha512", "system.io.filesystem.nuspec"]}, "System.IO.FileSystem.Primitives/4.3.0": {"sha512": "6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "type": "package", "path": "system.io.filesystem.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.Primitives.dll", "lib/netstandard1.3/System.IO.FileSystem.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/de/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/es/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/it/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.primitives.4.3.0.nupkg.sha512", "system.io.filesystem.primitives.nuspec"]}, "System.Linq/4.3.0": {"sha512": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "type": "package", "path": "system.linq/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.3.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Linq.Dynamic.Core/1.0.19": {"sha512": "GFYslLz/1ZZ0+gbqYED2zlD0H95BIK4hOpIcEEEfTDDXAcKE5vpXQQseOGduNVjcJZOF3Wx+4npa2EjdFpuDgA==", "type": "package", "path": "system.linq.dynamic.core/1.0.19", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/System.Linq.Dynamic.Core.dll", "lib/net35/System.Linq.Dynamic.Core.pdb", "lib/net35/System.Linq.Dynamic.Core.xml", "lib/net40/System.Linq.Dynamic.Core.dll", "lib/net40/System.Linq.Dynamic.Core.pdb", "lib/net40/System.Linq.Dynamic.Core.xml", "lib/net45/System.Linq.Dynamic.Core.dll", "lib/net45/System.Linq.Dynamic.Core.pdb", "lib/net45/System.Linq.Dynamic.Core.xml", "lib/net46/System.Linq.Dynamic.Core.dll", "lib/net46/System.Linq.Dynamic.Core.pdb", "lib/net46/System.Linq.Dynamic.Core.xml", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.xml", "lib/netstandard1.3/System.Linq.Dynamic.Core.dll", "lib/netstandard1.3/System.Linq.Dynamic.Core.pdb", "lib/netstandard1.3/System.Linq.Dynamic.Core.xml", "lib/netstandard2.0/System.Linq.Dynamic.Core.dll", "lib/netstandard2.0/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.0/System.Linq.Dynamic.Core.xml", "lib/uap10.0/System.Linq.Dynamic.Core.dll", "lib/uap10.0/System.Linq.Dynamic.Core.pdb", "lib/uap10.0/System.Linq.Dynamic.Core.pri", "lib/uap10.0/System.Linq.Dynamic.Core.xml", "system.linq.dynamic.core.1.0.19.nupkg.sha512", "system.linq.dynamic.core.nuspec"]}, "System.Linq.Expressions/4.3.0": {"sha512": "PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "type": "package", "path": "system.linq.expressions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.Expressions.dll", "lib/netcore50/System.Linq.Expressions.dll", "lib/netstandard1.6/System.Linq.Expressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.xml", "ref/netcore50/de/System.Linq.Expressions.xml", "ref/netcore50/es/System.Linq.Expressions.xml", "ref/netcore50/fr/System.Linq.Expressions.xml", "ref/netcore50/it/System.Linq.Expressions.xml", "ref/netcore50/ja/System.Linq.Expressions.xml", "ref/netcore50/ko/System.Linq.Expressions.xml", "ref/netcore50/ru/System.Linq.Expressions.xml", "ref/netcore50/zh-hans/System.Linq.Expressions.xml", "ref/netcore50/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.0/System.Linq.Expressions.dll", "ref/netstandard1.0/System.Linq.Expressions.xml", "ref/netstandard1.0/de/System.Linq.Expressions.xml", "ref/netstandard1.0/es/System.Linq.Expressions.xml", "ref/netstandard1.0/fr/System.Linq.Expressions.xml", "ref/netstandard1.0/it/System.Linq.Expressions.xml", "ref/netstandard1.0/ja/System.Linq.Expressions.xml", "ref/netstandard1.0/ko/System.Linq.Expressions.xml", "ref/netstandard1.0/ru/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.3/System.Linq.Expressions.dll", "ref/netstandard1.3/System.Linq.Expressions.xml", "ref/netstandard1.3/de/System.Linq.Expressions.xml", "ref/netstandard1.3/es/System.Linq.Expressions.xml", "ref/netstandard1.3/fr/System.Linq.Expressions.xml", "ref/netstandard1.3/it/System.Linq.Expressions.xml", "ref/netstandard1.3/ja/System.Linq.Expressions.xml", "ref/netstandard1.3/ko/System.Linq.Expressions.xml", "ref/netstandard1.3/ru/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.6/System.Linq.Expressions.dll", "ref/netstandard1.6/System.Linq.Expressions.xml", "ref/netstandard1.6/de/System.Linq.Expressions.xml", "ref/netstandard1.6/es/System.Linq.Expressions.xml", "ref/netstandard1.6/fr/System.Linq.Expressions.xml", "ref/netstandard1.6/it/System.Linq.Expressions.xml", "ref/netstandard1.6/ja/System.Linq.Expressions.xml", "ref/netstandard1.6/ko/System.Linq.Expressions.xml", "ref/netstandard1.6/ru/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hant/System.Linq.Expressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Linq.Expressions.dll", "system.linq.expressions.4.3.0.nupkg.sha512", "system.linq.expressions.nuspec"]}, "System.Linq.Queryable/4.3.0": {"sha512": "In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "type": "package", "path": "system.linq.queryable/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Linq.Queryable.dll", "lib/netstandard1.3/System.Linq.Queryable.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/net45/_._", "ref/netcore50/System.Linq.Queryable.dll", "ref/netcore50/System.Linq.Queryable.xml", "ref/netcore50/de/System.Linq.Queryable.xml", "ref/netcore50/es/System.Linq.Queryable.xml", "ref/netcore50/fr/System.Linq.Queryable.xml", "ref/netcore50/it/System.Linq.Queryable.xml", "ref/netcore50/ja/System.Linq.Queryable.xml", "ref/netcore50/ko/System.Linq.Queryable.xml", "ref/netcore50/ru/System.Linq.Queryable.xml", "ref/netcore50/zh-hans/System.Linq.Queryable.xml", "ref/netcore50/zh-hant/System.Linq.Queryable.xml", "ref/netstandard1.0/System.Linq.Queryable.dll", "ref/netstandard1.0/System.Linq.Queryable.xml", "ref/netstandard1.0/de/System.Linq.Queryable.xml", "ref/netstandard1.0/es/System.Linq.Queryable.xml", "ref/netstandard1.0/fr/System.Linq.Queryable.xml", "ref/netstandard1.0/it/System.Linq.Queryable.xml", "ref/netstandard1.0/ja/System.Linq.Queryable.xml", "ref/netstandard1.0/ko/System.Linq.Queryable.xml", "ref/netstandard1.0/ru/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hans/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hant/System.Linq.Queryable.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.queryable.4.3.0.nupkg.sha512", "system.linq.queryable.nuspec"]}, "System.Net.Http/4.3.0": {"sha512": "sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "type": "package", "path": "system.net.http/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/Xamarinmac20/_._", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/net46/System.Net.Http.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/Xamarinmac20/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/net45/_._", "ref/net46/System.Net.Http.dll", "ref/net46/System.Net.Http.xml", "ref/net46/de/System.Net.Http.xml", "ref/net46/es/System.Net.Http.xml", "ref/net46/fr/System.Net.Http.xml", "ref/net46/it/System.Net.Http.xml", "ref/net46/ja/System.Net.Http.xml", "ref/net46/ko/System.Net.Http.xml", "ref/net46/ru/System.Net.Http.xml", "ref/net46/zh-hans/System.Net.Http.xml", "ref/net46/zh-hant/System.Net.Http.xml", "ref/netcore50/System.Net.Http.dll", "ref/netcore50/System.Net.Http.xml", "ref/netcore50/de/System.Net.Http.xml", "ref/netcore50/es/System.Net.Http.xml", "ref/netcore50/fr/System.Net.Http.xml", "ref/netcore50/it/System.Net.Http.xml", "ref/netcore50/ja/System.Net.Http.xml", "ref/netcore50/ko/System.Net.Http.xml", "ref/netcore50/ru/System.Net.Http.xml", "ref/netcore50/zh-hans/System.Net.Http.xml", "ref/netcore50/zh-hant/System.Net.Http.xml", "ref/netstandard1.1/System.Net.Http.dll", "ref/netstandard1.1/System.Net.Http.xml", "ref/netstandard1.1/de/System.Net.Http.xml", "ref/netstandard1.1/es/System.Net.Http.xml", "ref/netstandard1.1/fr/System.Net.Http.xml", "ref/netstandard1.1/it/System.Net.Http.xml", "ref/netstandard1.1/ja/System.Net.Http.xml", "ref/netstandard1.1/ko/System.Net.Http.xml", "ref/netstandard1.1/ru/System.Net.Http.xml", "ref/netstandard1.1/zh-hans/System.Net.Http.xml", "ref/netstandard1.1/zh-hant/System.Net.Http.xml", "ref/netstandard1.3/System.Net.Http.dll", "ref/netstandard1.3/System.Net.Http.xml", "ref/netstandard1.3/de/System.Net.Http.xml", "ref/netstandard1.3/es/System.Net.Http.xml", "ref/netstandard1.3/fr/System.Net.Http.xml", "ref/netstandard1.3/it/System.Net.Http.xml", "ref/netstandard1.3/ja/System.Net.Http.xml", "ref/netstandard1.3/ko/System.Net.Http.xml", "ref/netstandard1.3/ru/System.Net.Http.xml", "ref/netstandard1.3/zh-hans/System.Net.Http.xml", "ref/netstandard1.3/zh-hant/System.Net.Http.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Net.Http.dll", "runtimes/win/lib/net46/System.Net.Http.dll", "runtimes/win/lib/netcore50/System.Net.Http.dll", "runtimes/win/lib/netstandard1.3/System.Net.Http.dll", "system.net.http.4.3.0.nupkg.sha512", "system.net.http.nuspec"]}, "System.Net.Primitives/4.3.0": {"sha512": "qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "type": "package", "path": "system.net.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Net.Primitives.dll", "ref/netcore50/System.Net.Primitives.xml", "ref/netcore50/de/System.Net.Primitives.xml", "ref/netcore50/es/System.Net.Primitives.xml", "ref/netcore50/fr/System.Net.Primitives.xml", "ref/netcore50/it/System.Net.Primitives.xml", "ref/netcore50/ja/System.Net.Primitives.xml", "ref/netcore50/ko/System.Net.Primitives.xml", "ref/netcore50/ru/System.Net.Primitives.xml", "ref/netcore50/zh-hans/System.Net.Primitives.xml", "ref/netcore50/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.0/System.Net.Primitives.dll", "ref/netstandard1.0/System.Net.Primitives.xml", "ref/netstandard1.0/de/System.Net.Primitives.xml", "ref/netstandard1.0/es/System.Net.Primitives.xml", "ref/netstandard1.0/fr/System.Net.Primitives.xml", "ref/netstandard1.0/it/System.Net.Primitives.xml", "ref/netstandard1.0/ja/System.Net.Primitives.xml", "ref/netstandard1.0/ko/System.Net.Primitives.xml", "ref/netstandard1.0/ru/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.1/System.Net.Primitives.dll", "ref/netstandard1.1/System.Net.Primitives.xml", "ref/netstandard1.1/de/System.Net.Primitives.xml", "ref/netstandard1.1/es/System.Net.Primitives.xml", "ref/netstandard1.1/fr/System.Net.Primitives.xml", "ref/netstandard1.1/it/System.Net.Primitives.xml", "ref/netstandard1.1/ja/System.Net.Primitives.xml", "ref/netstandard1.1/ko/System.Net.Primitives.xml", "ref/netstandard1.1/ru/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.3/System.Net.Primitives.dll", "ref/netstandard1.3/System.Net.Primitives.xml", "ref/netstandard1.3/de/System.Net.Primitives.xml", "ref/netstandard1.3/es/System.Net.Primitives.xml", "ref/netstandard1.3/fr/System.Net.Primitives.xml", "ref/netstandard1.3/it/System.Net.Primitives.xml", "ref/netstandard1.3/ja/System.Net.Primitives.xml", "ref/netstandard1.3/ko/System.Net.Primitives.xml", "ref/netstandard1.3/ru/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hant/System.Net.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.net.primitives.4.3.0.nupkg.sha512", "system.net.primitives.nuspec"]}, "System.Net.Sockets/4.3.0": {"sha512": "m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "type": "package", "path": "system.net.sockets/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Net.Sockets.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Net.Sockets.dll", "ref/netstandard1.3/System.Net.Sockets.dll", "ref/netstandard1.3/System.Net.Sockets.xml", "ref/netstandard1.3/de/System.Net.Sockets.xml", "ref/netstandard1.3/es/System.Net.Sockets.xml", "ref/netstandard1.3/fr/System.Net.Sockets.xml", "ref/netstandard1.3/it/System.Net.Sockets.xml", "ref/netstandard1.3/ja/System.Net.Sockets.xml", "ref/netstandard1.3/ko/System.Net.Sockets.xml", "ref/netstandard1.3/ru/System.Net.Sockets.xml", "ref/netstandard1.3/zh-hans/System.Net.Sockets.xml", "ref/netstandard1.3/zh-hant/System.Net.Sockets.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.net.sockets.4.3.0.nupkg.sha512", "system.net.sockets.nuspec"]}, "System.ObjectModel/4.3.0": {"sha512": "bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "type": "package", "path": "system.objectmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ObjectModel.dll", "lib/netstandard1.3/System.ObjectModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ObjectModel.dll", "ref/netcore50/System.ObjectModel.xml", "ref/netcore50/de/System.ObjectModel.xml", "ref/netcore50/es/System.ObjectModel.xml", "ref/netcore50/fr/System.ObjectModel.xml", "ref/netcore50/it/System.ObjectModel.xml", "ref/netcore50/ja/System.ObjectModel.xml", "ref/netcore50/ko/System.ObjectModel.xml", "ref/netcore50/ru/System.ObjectModel.xml", "ref/netcore50/zh-hans/System.ObjectModel.xml", "ref/netcore50/zh-hant/System.ObjectModel.xml", "ref/netstandard1.0/System.ObjectModel.dll", "ref/netstandard1.0/System.ObjectModel.xml", "ref/netstandard1.0/de/System.ObjectModel.xml", "ref/netstandard1.0/es/System.ObjectModel.xml", "ref/netstandard1.0/fr/System.ObjectModel.xml", "ref/netstandard1.0/it/System.ObjectModel.xml", "ref/netstandard1.0/ja/System.ObjectModel.xml", "ref/netstandard1.0/ko/System.ObjectModel.xml", "ref/netstandard1.0/ru/System.ObjectModel.xml", "ref/netstandard1.0/zh-hans/System.ObjectModel.xml", "ref/netstandard1.0/zh-hant/System.ObjectModel.xml", "ref/netstandard1.3/System.ObjectModel.dll", "ref/netstandard1.3/System.ObjectModel.xml", "ref/netstandard1.3/de/System.ObjectModel.xml", "ref/netstandard1.3/es/System.ObjectModel.xml", "ref/netstandard1.3/fr/System.ObjectModel.xml", "ref/netstandard1.3/it/System.ObjectModel.xml", "ref/netstandard1.3/ja/System.ObjectModel.xml", "ref/netstandard1.3/ko/System.ObjectModel.xml", "ref/netstandard1.3/ru/System.ObjectModel.xml", "ref/netstandard1.3/zh-hans/System.ObjectModel.xml", "ref/netstandard1.3/zh-hant/System.ObjectModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.objectmodel.4.3.0.nupkg.sha512", "system.objectmodel.nuspec"]}, "System.Private.DataContractSerialization/4.3.0": {"sha512": "yDaJ2x3mMmjdZEDB4IbezSnCsnjQ4BxinKhRAaP6kEgL6Bb6jANWphs5SzyD8imqeC/3FxgsuXT6ykkiH1uUmA==", "type": "package", "path": "system.private.datacontractserialization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.3/System.Private.DataContractSerialization.dll", "ref/netstandard/_._", "runtimes/aot/lib/netcore50/System.Private.DataContractSerialization.dll", "system.private.datacontractserialization.4.3.0.nupkg.sha512", "system.private.datacontractserialization.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Emit/4.3.0": {"sha512": "228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "type": "package", "path": "system.reflection.emit/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/net45/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/xamarinmac20/_._", "system.reflection.emit.4.3.0.nupkg.sha512", "system.reflection.emit.nuspec"]}, "System.Reflection.Emit.ILGeneration/4.3.0": {"sha512": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec"]}, "System.Reflection.Emit.Lightweight/4.3.0": {"sha512": "oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "type": "package", "path": "system.reflection.emit.lightweight/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec"]}, "System.Reflection.Extensions/4.3.0": {"sha512": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "type": "package", "path": "system.reflection.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.extensions.4.3.0.nupkg.sha512", "system.reflection.extensions.nuspec"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Reflection.TypeExtensions/4.3.0": {"sha512": "7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "type": "package", "path": "system.reflection.typeextensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net462/System.Reflection.TypeExtensions.dll", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net462/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll", "system.reflection.typeextensions.4.3.0.nupkg.sha512", "system.reflection.typeextensions.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Handles/4.3.0": {"sha512": "OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "type": "package", "path": "system.runtime.handles/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/_._", "ref/netstandard1.3/System.Runtime.Handles.dll", "ref/netstandard1.3/System.Runtime.Handles.xml", "ref/netstandard1.3/de/System.Runtime.Handles.xml", "ref/netstandard1.3/es/System.Runtime.Handles.xml", "ref/netstandard1.3/fr/System.Runtime.Handles.xml", "ref/netstandard1.3/it/System.Runtime.Handles.xml", "ref/netstandard1.3/ja/System.Runtime.Handles.xml", "ref/netstandard1.3/ko/System.Runtime.Handles.xml", "ref/netstandard1.3/ru/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Handles.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.handles.4.3.0.nupkg.sha512", "system.runtime.handles.nuspec"]}, "System.Runtime.InteropServices/4.3.0": {"sha512": "uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "type": "package", "path": "system.runtime.interopservices/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.InteropServices.dll", "lib/net463/System.Runtime.InteropServices.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.InteropServices.dll", "ref/net463/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.xml", "ref/netcore50/de/System.Runtime.InteropServices.xml", "ref/netcore50/es/System.Runtime.InteropServices.xml", "ref/netcore50/fr/System.Runtime.InteropServices.xml", "ref/netcore50/it/System.Runtime.InteropServices.xml", "ref/netcore50/ja/System.Runtime.InteropServices.xml", "ref/netcore50/ko/System.Runtime.InteropServices.xml", "ref/netcore50/ru/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.xml", "ref/netcoreapp1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.xml", "ref/netstandard1.1/de/System.Runtime.InteropServices.xml", "ref/netstandard1.1/es/System.Runtime.InteropServices.xml", "ref/netstandard1.1/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.1/it/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.2/System.Runtime.InteropServices.dll", "ref/netstandard1.2/System.Runtime.InteropServices.xml", "ref/netstandard1.2/de/System.Runtime.InteropServices.xml", "ref/netstandard1.2/es/System.Runtime.InteropServices.xml", "ref/netstandard1.2/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.2/it/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.3/System.Runtime.InteropServices.dll", "ref/netstandard1.3/System.Runtime.InteropServices.xml", "ref/netstandard1.3/de/System.Runtime.InteropServices.xml", "ref/netstandard1.3/es/System.Runtime.InteropServices.xml", "ref/netstandard1.3/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.3/it/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.5/System.Runtime.InteropServices.dll", "ref/netstandard1.5/System.Runtime.InteropServices.xml", "ref/netstandard1.5/de/System.Runtime.InteropServices.xml", "ref/netstandard1.5/es/System.Runtime.InteropServices.xml", "ref/netstandard1.5/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.5/it/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hant/System.Runtime.InteropServices.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.interopservices.4.3.0.nupkg.sha512", "system.runtime.interopservices.nuspec"]}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"sha512": "cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "type": "package", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/win8/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/wpa81/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "system.runtime.interopservices.runtimeinformation.nuspec"]}, "System.Runtime.Loader/4.3.0": {"sha512": "DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "type": "package", "path": "system.runtime.loader/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/netstandard1.5/System.Runtime.Loader.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard1.5/System.Runtime.Loader.dll", "ref/netstandard1.5/System.Runtime.Loader.xml", "ref/netstandard1.5/de/System.Runtime.Loader.xml", "ref/netstandard1.5/es/System.Runtime.Loader.xml", "ref/netstandard1.5/fr/System.Runtime.Loader.xml", "ref/netstandard1.5/it/System.Runtime.Loader.xml", "ref/netstandard1.5/ja/System.Runtime.Loader.xml", "ref/netstandard1.5/ko/System.Runtime.Loader.xml", "ref/netstandard1.5/ru/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Loader.xml", "system.runtime.loader.4.3.0.nupkg.sha512", "system.runtime.loader.nuspec"]}, "System.Runtime.Numerics/4.3.0": {"sha512": "yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "type": "package", "path": "system.runtime.numerics/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.Numerics.dll", "lib/netstandard1.3/System.Runtime.Numerics.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.Numerics.dll", "ref/netcore50/System.Runtime.Numerics.xml", "ref/netcore50/de/System.Runtime.Numerics.xml", "ref/netcore50/es/System.Runtime.Numerics.xml", "ref/netcore50/fr/System.Runtime.Numerics.xml", "ref/netcore50/it/System.Runtime.Numerics.xml", "ref/netcore50/ja/System.Runtime.Numerics.xml", "ref/netcore50/ko/System.Runtime.Numerics.xml", "ref/netcore50/ru/System.Runtime.Numerics.xml", "ref/netcore50/zh-hans/System.Runtime.Numerics.xml", "ref/netcore50/zh-hant/System.Runtime.Numerics.xml", "ref/netstandard1.1/System.Runtime.Numerics.dll", "ref/netstandard1.1/System.Runtime.Numerics.xml", "ref/netstandard1.1/de/System.Runtime.Numerics.xml", "ref/netstandard1.1/es/System.Runtime.Numerics.xml", "ref/netstandard1.1/fr/System.Runtime.Numerics.xml", "ref/netstandard1.1/it/System.Runtime.Numerics.xml", "ref/netstandard1.1/ja/System.Runtime.Numerics.xml", "ref/netstandard1.1/ko/System.Runtime.Numerics.xml", "ref/netstandard1.1/ru/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hans/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hant/System.Runtime.Numerics.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.numerics.4.3.0.nupkg.sha512", "system.runtime.numerics.nuspec"]}, "System.Runtime.Serialization.Primitives/4.3.0": {"sha512": "Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "type": "package", "path": "system.runtime.serialization.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Runtime.Serialization.Primitives.dll", "lib/netcore50/System.Runtime.Serialization.Primitives.dll", "lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Runtime.Serialization.Primitives.dll", "ref/netcore50/System.Runtime.Serialization.Primitives.dll", "ref/netcore50/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/de/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/es/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/fr/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/it/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ja/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ko/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ru/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/System.Runtime.Serialization.Primitives.dll", "ref/netstandard1.0/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/de/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/es/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/fr/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/it/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ja/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ko/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ru/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/System.Runtime.Serialization.Primitives.dll", "ref/netstandard1.3/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/de/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/es/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/fr/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/it/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ja/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ko/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ru/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.Serialization.Primitives.dll", "system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "system.runtime.serialization.primitives.nuspec"]}, "System.Runtime.Serialization.Xml/4.3.0": {"sha512": "nUQx/5OVgrqEba3+j7OdiofvVq9koWZAC7Z3xGI8IIViZqApWnZ5+lLcwYgTlbkobrl/Rat+Jb8GeD4WQESD2A==", "type": "package", "path": "system.runtime.serialization.xml/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Runtime.Serialization.Xml.dll", "lib/netcore50/System.Runtime.Serialization.Xml.dll", "lib/netstandard1.3/System.Runtime.Serialization.Xml.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Runtime.Serialization.Xml.dll", "ref/netcore50/System.Runtime.Serialization.Xml.dll", "ref/netcore50/System.Runtime.Serialization.Xml.xml", "ref/netcore50/de/System.Runtime.Serialization.Xml.xml", "ref/netcore50/es/System.Runtime.Serialization.Xml.xml", "ref/netcore50/fr/System.Runtime.Serialization.Xml.xml", "ref/netcore50/it/System.Runtime.Serialization.Xml.xml", "ref/netcore50/ja/System.Runtime.Serialization.Xml.xml", "ref/netcore50/ko/System.Runtime.Serialization.Xml.xml", "ref/netcore50/ru/System.Runtime.Serialization.Xml.xml", "ref/netcore50/zh-hans/System.Runtime.Serialization.Xml.xml", "ref/netcore50/zh-hant/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/System.Runtime.Serialization.Xml.dll", "ref/netstandard1.0/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/de/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/es/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/fr/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/it/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/ja/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/ko/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/ru/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/System.Runtime.Serialization.Xml.dll", "ref/netstandard1.3/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/de/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/es/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/fr/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/it/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/ja/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/ko/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/ru/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Serialization.Xml.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.serialization.xml.4.3.0.nupkg.sha512", "system.runtime.serialization.xml.nuspec"]}, "System.Security.AccessControl/4.5.0": {"sha512": "vW8Eoq0TMyz5vAG/6ce483x/CP83fgm4SJe5P8Tb1tZaobcvPrbMEL7rhH1DRdrYbbb6F0vq3OlzmK0Pkwks5A==", "type": "package", "path": "system.security.accesscontrol/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.5.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Claims/4.3.0": {"sha512": "P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "type": "package", "path": "system.security.claims/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Claims.dll", "lib/netstandard1.3/System.Security.Claims.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Claims.dll", "ref/netstandard1.3/System.Security.Claims.dll", "ref/netstandard1.3/System.Security.Claims.xml", "ref/netstandard1.3/de/System.Security.Claims.xml", "ref/netstandard1.3/es/System.Security.Claims.xml", "ref/netstandard1.3/fr/System.Security.Claims.xml", "ref/netstandard1.3/it/System.Security.Claims.xml", "ref/netstandard1.3/ja/System.Security.Claims.xml", "ref/netstandard1.3/ko/System.Security.Claims.xml", "ref/netstandard1.3/ru/System.Security.Claims.xml", "ref/netstandard1.3/zh-hans/System.Security.Claims.xml", "ref/netstandard1.3/zh-hant/System.Security.Claims.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.claims.4.3.0.nupkg.sha512", "system.security.claims.nuspec"]}, "System.Security.Cryptography.Algorithms/4.3.0": {"sha512": "W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "type": "package", "path": "system.security.cryptography.algorithms/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Algorithms.dll", "lib/net461/System.Security.Cryptography.Algorithms.dll", "lib/net463/System.Security.Cryptography.Algorithms.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Algorithms.dll", "ref/net461/System.Security.Cryptography.Algorithms.dll", "ref/net463/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.3/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.4/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/osx/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net463/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "system.security.cryptography.algorithms.nuspec"]}, "System.Security.Cryptography.Cng/4.5.0": {"sha512": "WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "type": "package", "path": "system.security.cryptography.cng/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net462/System.Security.Cryptography.Cng.dll", "lib/net47/System.Security.Cryptography.Cng.dll", "lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "lib/netstandard1.3/System.Security.Cryptography.Cng.dll", "lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.dll", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.xml", "ref/net462/System.Security.Cryptography.Cng.dll", "ref/net462/System.Security.Cryptography.Cng.xml", "ref/net47/System.Security.Cryptography.Cng.dll", "ref/net47/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.xml", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.cryptography.cng.4.5.0.nupkg.sha512", "system.security.cryptography.cng.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Csp/4.3.0": {"sha512": "X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "type": "package", "path": "system.security.cryptography.csp/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Csp.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Csp.dll", "ref/netstandard1.3/System.Security.Cryptography.Csp.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Csp.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Csp.dll", "runtimes/win/lib/netcore50/_._", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Csp.dll", "system.security.cryptography.csp.4.3.0.nupkg.sha512", "system.security.cryptography.csp.nuspec"]}, "System.Security.Cryptography.Encoding/4.3.0": {"sha512": "1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "type": "package", "path": "system.security.cryptography.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Encoding.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/de/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/es/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/it/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.Encoding.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "system.security.cryptography.encoding.4.3.0.nupkg.sha512", "system.security.cryptography.encoding.nuspec"]}, "System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "type": "package", "path": "system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "ref/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "system.security.cryptography.openssl.4.3.0.nupkg.sha512", "system.security.cryptography.openssl.nuspec"]}, "System.Security.Cryptography.Pkcs/4.5.0": {"sha512": "TGQX51gxpY3K3I6LJlE2LAftVlIMqJf0cBGhz68Y89jjk3LJCB6SrwiD+YN1fkqemBvWGs+GjyMJukl6d6goyQ==", "type": "package", "path": "system.security.cryptography.pkcs/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Cryptography.Pkcs.dll", "lib/net461/System.Security.Cryptography.Pkcs.dll", "lib/netcoreapp2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard1.3/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "ref/net46/System.Security.Cryptography.Pkcs.dll", "ref/net461/System.Security.Cryptography.Pkcs.dll", "ref/net461/System.Security.Cryptography.Pkcs.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Pkcs.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Pkcs.xml", "ref/netstandard1.3/System.Security.Cryptography.Pkcs.dll", "ref/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "ref/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net46/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "system.security.cryptography.pkcs.4.5.0.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Primitives/4.3.0": {"sha512": "7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "type": "package", "path": "system.security.cryptography.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Primitives.dll", "lib/netstandard1.3/System.Security.Cryptography.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Primitives.dll", "ref/netstandard1.3/System.Security.Cryptography.Primitives.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.cryptography.primitives.4.3.0.nupkg.sha512", "system.security.cryptography.primitives.nuspec"]}, "System.Security.Cryptography.ProtectedData/4.4.0": {"sha512": "cJV7ScGW7EhatRsjehfvvYVBvtiSMKgN8bOVI0bQhnF5bU7vnHVIsH49Kva7i7GWaWYvmEzkYVk1TC+gZYBEog==", "type": "package", "path": "system.security.cryptography.protecteddata/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.ProtectedData.dll", "ref/net461/System.Security.Cryptography.ProtectedData.dll", "ref/net461/System.Security.Cryptography.ProtectedData.xml", "ref/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "ref/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "ref/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "system.security.cryptography.protecteddata.4.4.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.X509Certificates/4.3.0": {"sha512": "t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "type": "package", "path": "system.security.cryptography.x509certificates/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.X509Certificates.dll", "lib/net461/System.Security.Cryptography.X509Certificates.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.X509Certificates.dll", "ref/net461/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.3/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.3/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/de/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/es/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/it/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.4/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/de/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/es/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/fr/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/it/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ja/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ko/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ru/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/zh-hans/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/zh-hant/System.Security.Cryptography.X509Certificates.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net46/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net461/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll", "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "system.security.cryptography.x509certificates.nuspec"]}, "System.Security.Cryptography.Xml/4.5.0": {"sha512": "i2Jn6rGXR63J0zIklImGRkDIJL4b1NfPSEbIVHBlqoIb12lfXIigCbDRpDmIEzwSo/v1U5y/rYJdzZYSyCWxvg==", "type": "package", "path": "system.security.cryptography.xml/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "ref/net461/System.Security.Cryptography.Xml.dll", "ref/net461/System.Security.Cryptography.Xml.xml", "ref/netstandard2.0/System.Security.Cryptography.Xml.dll", "ref/netstandard2.0/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.4.5.0.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Permissions/4.5.0": {"sha512": "9gdyuARhUR7H+p5CjyUB/zPk7/Xut3wUSP8NJQB6iZr8L3XUXTMdoLeVAg9N4rqF8oIpE7MpdqHdDHQ7XgJe0g==", "type": "package", "path": "system.security.permissions/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.dll", "ref/net461/System.Security.Permissions.dll", "ref/net461/System.Security.Permissions.xml", "ref/netstandard2.0/System.Security.Permissions.dll", "ref/netstandard2.0/System.Security.Permissions.xml", "system.security.permissions.4.5.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal/4.3.0": {"sha512": "I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "type": "package", "path": "system.security.principal/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Security.Principal.dll", "lib/netstandard1.0/System.Security.Principal.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Security.Principal.dll", "ref/netcore50/System.Security.Principal.xml", "ref/netcore50/de/System.Security.Principal.xml", "ref/netcore50/es/System.Security.Principal.xml", "ref/netcore50/fr/System.Security.Principal.xml", "ref/netcore50/it/System.Security.Principal.xml", "ref/netcore50/ja/System.Security.Principal.xml", "ref/netcore50/ko/System.Security.Principal.xml", "ref/netcore50/ru/System.Security.Principal.xml", "ref/netcore50/zh-hans/System.Security.Principal.xml", "ref/netcore50/zh-hant/System.Security.Principal.xml", "ref/netstandard1.0/System.Security.Principal.dll", "ref/netstandard1.0/System.Security.Principal.xml", "ref/netstandard1.0/de/System.Security.Principal.xml", "ref/netstandard1.0/es/System.Security.Principal.xml", "ref/netstandard1.0/fr/System.Security.Principal.xml", "ref/netstandard1.0/it/System.Security.Principal.xml", "ref/netstandard1.0/ja/System.Security.Principal.xml", "ref/netstandard1.0/ko/System.Security.Principal.xml", "ref/netstandard1.0/ru/System.Security.Principal.xml", "ref/netstandard1.0/zh-hans/System.Security.Principal.xml", "ref/netstandard1.0/zh-hant/System.Security.Principal.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.principal.4.3.0.nupkg.sha512", "system.security.principal.nuspec"]}, "System.Security.Principal.Windows/4.5.0": {"sha512": "U77HfRXlZlOeIXd//Yoj6Jnk8AXlbeisf1oq1os+hxOGVnuG+lGSfGqTwTZBoORFF6j/0q7HXIl8cqwQ9aUGqQ==", "type": "package", "path": "system.security.principal.windows/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.5.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/4.0.1": {"sha512": "h4z6rrA/hxWf4655D18IIZ0eaLRa3tQC/j+e26W+VinIHY0l07iEXaAvO0YSYq3MvCjMYy8Zs5AdC1sxNQOB7Q==", "type": "package", "path": "system.text.encoding.codepages/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Text.Encoding.CodePages.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.3/System.Text.Encoding.CodePages.dll", "ref/netstandard1.3/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/de/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/es/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/fr/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/it/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/ja/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/ko/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/ru/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.CodePages.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "system.text.encoding.codepages.4.0.1.nupkg.sha512", "system.text.encoding.codepages.nuspec"]}, "System.Text.Encoding.Extensions/4.3.0": {"sha512": "YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "type": "package", "path": "system.text.encoding.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.Extensions.dll", "ref/netcore50/System.Text.Encoding.Extensions.xml", "ref/netcore50/de/System.Text.Encoding.Extensions.xml", "ref/netcore50/es/System.Text.Encoding.Extensions.xml", "ref/netcore50/fr/System.Text.Encoding.Extensions.xml", "ref/netcore50/it/System.Text.Encoding.Extensions.xml", "ref/netcore50/ja/System.Text.Encoding.Extensions.xml", "ref/netcore50/ko/System.Text.Encoding.Extensions.xml", "ref/netcore50/ru/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/System.Text.Encoding.Extensions.dll", "ref/netstandard1.0/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/System.Text.Encoding.Extensions.dll", "ref/netstandard1.3/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.extensions.4.3.0.nupkg.sha512", "system.text.encoding.extensions.nuspec"]}, "System.Text.Encodings.Web/4.5.0": {"sha512": "Xg4G4Indi4dqP1iuAiMSwpiWS54ZghzR644OtsRCm/m/lBMG8dUBhLVN7hLm8NNrNTR+iGbshCPTwrvxZPlm4g==", "type": "package", "path": "system.text.encodings.web/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/System.Text.Encodings.Web.dll", "lib/netstandard1.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.4.5.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.RegularExpressions/4.3.0": {"sha512": "RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "type": "package", "path": "system.text.regularexpressions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Text.RegularExpressions.dll", "lib/netcore50/System.Text.RegularExpressions.dll", "lib/netstandard1.6/System.Text.RegularExpressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.xml", "ref/netcore50/de/System.Text.RegularExpressions.xml", "ref/netcore50/es/System.Text.RegularExpressions.xml", "ref/netcore50/fr/System.Text.RegularExpressions.xml", "ref/netcore50/it/System.Text.RegularExpressions.xml", "ref/netcore50/ja/System.Text.RegularExpressions.xml", "ref/netcore50/ko/System.Text.RegularExpressions.xml", "ref/netcore50/ru/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hans/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hant/System.Text.RegularExpressions.xml", "ref/netcoreapp1.1/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.xml", "ref/netstandard1.0/de/System.Text.RegularExpressions.xml", "ref/netstandard1.0/es/System.Text.RegularExpressions.xml", "ref/netstandard1.0/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.0/it/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.3/System.Text.RegularExpressions.dll", "ref/netstandard1.3/System.Text.RegularExpressions.xml", "ref/netstandard1.3/de/System.Text.RegularExpressions.xml", "ref/netstandard1.3/es/System.Text.RegularExpressions.xml", "ref/netstandard1.3/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.3/it/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.6/System.Text.RegularExpressions.dll", "ref/netstandard1.6/System.Text.RegularExpressions.xml", "ref/netstandard1.6/de/System.Text.RegularExpressions.xml", "ref/netstandard1.6/es/System.Text.RegularExpressions.xml", "ref/netstandard1.6/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.6/it/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hant/System.Text.RegularExpressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.regularexpressions.4.3.0.nupkg.sha512", "system.text.regularexpressions.nuspec"]}, "System.Threading/4.3.0": {"sha512": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "type": "package", "path": "system.threading/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.3.0.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.3.0": {"sha512": "npvJkVKl5rKXrtl1Kkm6OhOUaYGEiF9wFbppFRWSMoApKzt2PiPHT2Bb8a5sAWxprvdOAtvaARS9QYMznEUtug==", "type": "package", "path": "system.threading.tasks.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "system.threading.tasks.extensions.4.3.0.nupkg.sha512", "system.threading.tasks.extensions.nuspec"]}, "System.Threading.Timer/4.3.0": {"sha512": "Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "type": "package", "path": "system.threading.timer/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/_._", "lib/portable-net451+win81+wpa81/_._", "lib/win81/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/_._", "ref/netcore50/System.Threading.Timer.dll", "ref/netcore50/System.Threading.Timer.xml", "ref/netcore50/de/System.Threading.Timer.xml", "ref/netcore50/es/System.Threading.Timer.xml", "ref/netcore50/fr/System.Threading.Timer.xml", "ref/netcore50/it/System.Threading.Timer.xml", "ref/netcore50/ja/System.Threading.Timer.xml", "ref/netcore50/ko/System.Threading.Timer.xml", "ref/netcore50/ru/System.Threading.Timer.xml", "ref/netcore50/zh-hans/System.Threading.Timer.xml", "ref/netcore50/zh-hant/System.Threading.Timer.xml", "ref/netstandard1.2/System.Threading.Timer.dll", "ref/netstandard1.2/System.Threading.Timer.xml", "ref/netstandard1.2/de/System.Threading.Timer.xml", "ref/netstandard1.2/es/System.Threading.Timer.xml", "ref/netstandard1.2/fr/System.Threading.Timer.xml", "ref/netstandard1.2/it/System.Threading.Timer.xml", "ref/netstandard1.2/ja/System.Threading.Timer.xml", "ref/netstandard1.2/ko/System.Threading.Timer.xml", "ref/netstandard1.2/ru/System.Threading.Timer.xml", "ref/netstandard1.2/zh-hans/System.Threading.Timer.xml", "ref/netstandard1.2/zh-hant/System.Threading.Timer.xml", "ref/portable-net451+win81+wpa81/_._", "ref/win81/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.timer.4.3.0.nupkg.sha512", "system.threading.timer.nuspec"]}, "System.Xml.ReaderWriter/4.3.0": {"sha512": "GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "type": "package", "path": "system.xml.readerwriter/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Xml.ReaderWriter.dll", "lib/netcore50/System.Xml.ReaderWriter.dll", "lib/netstandard1.3/System.Xml.ReaderWriter.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Xml.ReaderWriter.dll", "ref/netcore50/System.Xml.ReaderWriter.dll", "ref/netcore50/System.Xml.ReaderWriter.xml", "ref/netcore50/de/System.Xml.ReaderWriter.xml", "ref/netcore50/es/System.Xml.ReaderWriter.xml", "ref/netcore50/fr/System.Xml.ReaderWriter.xml", "ref/netcore50/it/System.Xml.ReaderWriter.xml", "ref/netcore50/ja/System.Xml.ReaderWriter.xml", "ref/netcore50/ko/System.Xml.ReaderWriter.xml", "ref/netcore50/ru/System.Xml.ReaderWriter.xml", "ref/netcore50/zh-hans/System.Xml.ReaderWriter.xml", "ref/netcore50/zh-hant/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/System.Xml.ReaderWriter.dll", "ref/netstandard1.0/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/de/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/es/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/fr/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/it/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ja/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ko/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ru/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/zh-hans/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/zh-hant/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/System.Xml.ReaderWriter.dll", "ref/netstandard1.3/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/de/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/es/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/fr/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/it/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ja/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ko/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ru/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/zh-hans/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/zh-hant/System.Xml.ReaderWriter.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.readerwriter.4.3.0.nupkg.sha512", "system.xml.readerwriter.nuspec"]}, "System.Xml.XDocument/4.3.0": {"sha512": "5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "type": "package", "path": "system.xml.xdocument/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Xml.XDocument.dll", "lib/netstandard1.3/System.Xml.XDocument.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Xml.XDocument.dll", "ref/netcore50/System.Xml.XDocument.xml", "ref/netcore50/de/System.Xml.XDocument.xml", "ref/netcore50/es/System.Xml.XDocument.xml", "ref/netcore50/fr/System.Xml.XDocument.xml", "ref/netcore50/it/System.Xml.XDocument.xml", "ref/netcore50/ja/System.Xml.XDocument.xml", "ref/netcore50/ko/System.Xml.XDocument.xml", "ref/netcore50/ru/System.Xml.XDocument.xml", "ref/netcore50/zh-hans/System.Xml.XDocument.xml", "ref/netcore50/zh-hant/System.Xml.XDocument.xml", "ref/netstandard1.0/System.Xml.XDocument.dll", "ref/netstandard1.0/System.Xml.XDocument.xml", "ref/netstandard1.0/de/System.Xml.XDocument.xml", "ref/netstandard1.0/es/System.Xml.XDocument.xml", "ref/netstandard1.0/fr/System.Xml.XDocument.xml", "ref/netstandard1.0/it/System.Xml.XDocument.xml", "ref/netstandard1.0/ja/System.Xml.XDocument.xml", "ref/netstandard1.0/ko/System.Xml.XDocument.xml", "ref/netstandard1.0/ru/System.Xml.XDocument.xml", "ref/netstandard1.0/zh-hans/System.Xml.XDocument.xml", "ref/netstandard1.0/zh-hant/System.Xml.XDocument.xml", "ref/netstandard1.3/System.Xml.XDocument.dll", "ref/netstandard1.3/System.Xml.XDocument.xml", "ref/netstandard1.3/de/System.Xml.XDocument.xml", "ref/netstandard1.3/es/System.Xml.XDocument.xml", "ref/netstandard1.3/fr/System.Xml.XDocument.xml", "ref/netstandard1.3/it/System.Xml.XDocument.xml", "ref/netstandard1.3/ja/System.Xml.XDocument.xml", "ref/netstandard1.3/ko/System.Xml.XDocument.xml", "ref/netstandard1.3/ru/System.Xml.XDocument.xml", "ref/netstandard1.3/zh-hans/System.Xml.XDocument.xml", "ref/netstandard1.3/zh-hant/System.Xml.XDocument.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.xdocument.4.3.0.nupkg.sha512", "system.xml.xdocument.nuspec"]}, "System.Xml.XmlDocument/4.3.0": {"sha512": "lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "type": "package", "path": "system.xml.xmldocument/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Xml.XmlDocument.dll", "lib/netstandard1.3/System.Xml.XmlDocument.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Xml.XmlDocument.dll", "ref/netstandard1.3/System.Xml.XmlDocument.dll", "ref/netstandard1.3/System.Xml.XmlDocument.xml", "ref/netstandard1.3/de/System.Xml.XmlDocument.xml", "ref/netstandard1.3/es/System.Xml.XmlDocument.xml", "ref/netstandard1.3/fr/System.Xml.XmlDocument.xml", "ref/netstandard1.3/it/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ja/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ko/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ru/System.Xml.XmlDocument.xml", "ref/netstandard1.3/zh-hans/System.Xml.XmlDocument.xml", "ref/netstandard1.3/zh-hant/System.Xml.XmlDocument.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.xmldocument.4.3.0.nupkg.sha512", "system.xml.xmldocument.nuspec"]}, "System.Xml.XmlSerializer/4.3.0": {"sha512": "MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "type": "package", "path": "system.xml.xmlserializer/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Xml.XmlSerializer.dll", "lib/netstandard1.3/System.Xml.XmlSerializer.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Xml.XmlSerializer.dll", "ref/netcore50/System.Xml.XmlSerializer.xml", "ref/netcore50/de/System.Xml.XmlSerializer.xml", "ref/netcore50/es/System.Xml.XmlSerializer.xml", "ref/netcore50/fr/System.Xml.XmlSerializer.xml", "ref/netcore50/it/System.Xml.XmlSerializer.xml", "ref/netcore50/ja/System.Xml.XmlSerializer.xml", "ref/netcore50/ko/System.Xml.XmlSerializer.xml", "ref/netcore50/ru/System.Xml.XmlSerializer.xml", "ref/netcore50/zh-hans/System.Xml.XmlSerializer.xml", "ref/netcore50/zh-hant/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/System.Xml.XmlSerializer.dll", "ref/netstandard1.0/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/de/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/es/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/fr/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/it/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/ja/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/ko/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/ru/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/zh-hans/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/zh-hant/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/System.Xml.XmlSerializer.dll", "ref/netstandard1.3/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/de/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/es/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/fr/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/it/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/ja/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/ko/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/ru/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/zh-hans/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/zh-hant/System.Xml.XmlSerializer.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Xml.XmlSerializer.dll", "system.xml.xmlserializer.4.3.0.nupkg.sha512", "system.xml.xmlserializer.nuspec"]}, "Volo.Abp.Auditing/0.15.0": {"sha512": "wH/MfRVkfUE7lfq5tBx1edVNsn9ksdQw3b0BgW9GDkMLmEB9JvbIrAslNHA5Nq9n1df8f5UOEr73t8Z+Tw/UEw==", "type": "package", "path": "volo.abp.auditing/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Auditing.dll", "lib/netstandard2.0/Volo.Abp.Auditing.pdb", "volo.abp.auditing.0.15.0.nupkg.sha512", "volo.abp.auditing.nuspec"]}, "Volo.Abp.AuditLogging.Domain/0.4.1": {"sha512": "pJWJcHfzjJ9ARUApXuatYt/Gz2O/Bg0w58S7lM2THW+PooR8gOWw19o0mLcaotTrLrpiANnHvKa2bxXVbBKjnQ==", "type": "package", "path": "volo.abp.auditlogging.domain/0.4.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.dll", "lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.pdb", "volo.abp.auditlogging.domain.0.4.1.nupkg.sha512", "volo.abp.auditlogging.domain.nuspec"]}, "Volo.Abp.AuditLogging.Domain.Shared/0.4.1": {"sha512": "8I+v3WTuweRCHxnrcibyeQ9K2h0/kacaiscpPPgg06eo82vvqFihbj6ArkXwWiPPi1TGz2i1W3xmD6u+wtrCjQ==", "type": "package", "path": "volo.abp.auditlogging.domain.shared/0.4.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.AuditLogging.Domain.Shared.pdb", "volo.abp.auditlogging.domain.shared.0.4.1.nupkg.sha512", "volo.abp.auditlogging.domain.shared.nuspec"]}, "Volo.Abp.AuditLogging.EntityFrameworkCore/0.4.1": {"sha512": "yzEJeNOdxObXX6AtjCIlTdAktPdxi4NoMqUQm9aLgaio6b7fPLpux/mIkFMo9Ie69/chzvlVbWoF6RXmRktQrA==", "type": "package", "path": "volo.abp.auditlogging.entityframeworkcore/0.4.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.AuditLogging.EntityFrameworkCore.dll", "lib/netstandard2.0/Volo.Abp.AuditLogging.EntityFrameworkCore.pdb", "volo.abp.auditlogging.entityframeworkcore.0.4.1.nupkg.sha512", "volo.abp.auditlogging.entityframeworkcore.nuspec"]}, "Volo.Abp.Authorization/0.14.0": {"sha512": "GBi6CAH83AM6e8KOdx/7qXew37yumCwo8HV07jW6H/idH4OJ7k+49+N/dvS2ZiNxaCmuekc0vgvnkACU2lTc4g==", "type": "package", "path": "volo.abp.authorization/0.14.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Authorization.dll", "lib/netstandard2.0/Volo.Abp.Authorization.pdb", "volo.abp.authorization.0.14.0.nupkg.sha512", "volo.abp.authorization.nuspec"]}, "Volo.Abp.AutoMapper/0.6.0": {"sha512": "mx7T18PfP+8o5sGBC63y6Aj7tEUF95OqyX2Q3rl3BtaAhQ7x07xONI/pOEF67VnzE5Gu0TI4F9n6tQq2Xa0j/Q==", "type": "package", "path": "volo.abp.automapper/0.6.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.AutoMapper.dll", "lib/netstandard2.0/Volo.Abp.AutoMapper.pdb", "volo.abp.automapper.0.6.0.nupkg.sha512", "volo.abp.automapper.nuspec"]}, "Volo.Abp.BackgroundJobs/0.4.1": {"sha512": "kS2RKjeTnvbmABA8oYO4jy8cYEIMkktXZJAPeSPkhR8sXKJA5qsakU865Nvnmg6zLNnCUTA9XfNHziUENnVpKw==", "type": "package", "path": "volo.abp.backgroundjobs/0.4.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.dll", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.pdb", "volo.abp.backgroundjobs.0.4.1.nupkg.sha512", "volo.abp.backgroundjobs.nuspec"]}, "Volo.Abp.BackgroundJobs.Abstractions/0.4.1": {"sha512": "1x0Mfg2KjBj9voyv4vkNOvgkK+VL8YLERh2MbqpHqB8qLDFxVKt58tiXY2B3mXVIOVOnxzXM9TDu18IM9bt9jw==", "type": "package", "path": "volo.abp.backgroundjobs.abstractions/0.4.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.pdb", "volo.abp.backgroundjobs.abstractions.0.4.1.nupkg.sha512", "volo.abp.backgroundjobs.abstractions.nuspec"]}, "Volo.Abp.BackgroundJobs.Domain/0.4.1": {"sha512": "9sutR5YJ5T3qNbQpS3Qwyt9o8h43qyRFAOk6Chna3gMebzPU60MqEQObSDdwAKQ5QeDyks10pJlTPD9axk03pw==", "type": "package", "path": "volo.abp.backgroundjobs.domain/0.4.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.dll", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.pdb", "volo.abp.backgroundjobs.domain.0.4.1.nupkg.sha512", "volo.abp.backgroundjobs.domain.nuspec"]}, "Volo.Abp.BackgroundJobs.Domain.Shared/0.4.1": {"sha512": "hAwebW8U1bB/OBBS6RmVhaLnNVf7GLE8Z03Tca3mcG/ug7P7x4f2vkEYW+gS5rvtG5/oB5/Dc5C5s5TjmXvoKw==", "type": "package", "path": "volo.abp.backgroundjobs.domain.shared/0.4.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Domain.Shared.pdb", "volo.abp.backgroundjobs.domain.shared.0.4.1.nupkg.sha512", "volo.abp.backgroundjobs.domain.shared.nuspec"]}, "Volo.Abp.BackgroundJobs.EntityFrameworkCore/0.4.1": {"sha512": "fW3Es3xk1tLnWjvIDKauyw1pKKHtiOJRYszncVIw33UlX+HKSDfBX1DpPIQuNQrp5Rzt80vIEq33Unp2sWU4Rw==", "type": "package", "path": "volo.abp.backgroundjobs.entityframeworkcore/0.4.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.EntityFrameworkCore.dll", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.EntityFrameworkCore.pdb", "volo.abp.backgroundjobs.entityframeworkcore.0.4.1.nupkg.sha512", "volo.abp.backgroundjobs.entityframeworkcore.nuspec"]}, "Volo.Abp.BackgroundWorkers/0.4.1": {"sha512": "7Hk/LtvlV6WXAfASPGHOnFnnKawODR1cmJA83sGNWYgYqxYhUijYE5BwsCGfZICV0ExNtTQV9JsJNVRh1Bo1oQ==", "type": "package", "path": "volo.abp.backgroundworkers/0.4.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.BackgroundWorkers.dll", "lib/netstandard2.0/Volo.Abp.BackgroundWorkers.pdb", "volo.abp.backgroundworkers.0.4.1.nupkg.sha512", "volo.abp.backgroundworkers.nuspec"]}, "Volo.Abp.Caching/0.15.0": {"sha512": "+VuELRyV7db5b7UfLCVUSIU3XGuwowcvxvB2lWGnzUIcSaO0Pq98tD+bb8i1/z8viU9jyi47VkyropubZTexFg==", "type": "package", "path": "volo.abp.caching/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Caching.dll", "lib/netstandard2.0/Volo.Abp.Caching.pdb", "volo.abp.caching.0.15.0.nupkg.sha512", "volo.abp.caching.nuspec"]}, "Volo.Abp.Core/2.4.0": {"sha512": "wwYm+2cZOEWz7c6uxNtNO35wP9AQDiEbz8sruHoaJ292Y77nqh7eu83H757KHVwjpChf4dK4oARmZ/6Og9aI4w==", "type": "package", "path": "volo.abp.core/2.4.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Core.dll", "lib/netstandard2.0/Volo.Abp.Core.pdb", "volo.abp.core.2.4.0.nupkg.sha512", "volo.abp.core.nuspec"]}, "Volo.Abp.Data/0.15.0": {"sha512": "hObmxdEMInN6ApHcm7bVCtugU/0tU9VbPdt47diqnF7q4rjJlDX8jSbC+bJm5+qQeBH4JCHeFzMcLu8579Vl2A==", "type": "package", "path": "volo.abp.data/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Data.dll", "lib/netstandard2.0/Volo.Abp.Data.pdb", "volo.abp.data.0.15.0.nupkg.sha512", "volo.abp.data.nuspec"]}, "Volo.Abp.Ddd.Domain/0.15.0": {"sha512": "Hpw5aDiH79p1ifeMYiMCfBUP+V4ZeB1lTEENd8KHUiIGtXj33E7AX7iIgwaytu+d9VHLvJZUo2K+xdPb/wHnmQ==", "type": "package", "path": "volo.abp.ddd.domain/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.pdb", "volo.abp.ddd.domain.0.15.0.nupkg.sha512", "volo.abp.ddd.domain.nuspec"]}, "Volo.Abp.Emailing/0.3.0": {"sha512": "UMklB4EO15P+rKP8aozpAuejPaPoxgcsYiuKJEcR2jfw4DcjoE189oLcIoCA4mOSzvRvLe5puOJuF7LM8MqeOA==", "type": "package", "path": "volo.abp.emailing/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Emailing.dll", "lib/netstandard2.0/Volo.Abp.Emailing.pdb", "volo.abp.emailing.0.3.0.nupkg.sha512", "volo.abp.emailing.nuspec"]}, "Volo.Abp.EntityFrameworkCore/0.15.0": {"sha512": "iTdo3UoDtBaB4ASJjuSw+VX6BZGYc3CjG94VJdmMZMulNGfMzON3n5YygNHDjKzJ84pMn2kLvMFMc1rX7aTvMw==", "type": "package", "path": "volo.abp.entityframeworkcore/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.EntityFrameworkCore.dll", "lib/netstandard2.0/Volo.Abp.EntityFrameworkCore.pdb", "volo.abp.entityframeworkcore.0.15.0.nupkg.sha512", "volo.abp.entityframeworkcore.nuspec"]}, "Volo.Abp.EntityFrameworkCore.MySQL/0.3.6": {"sha512": "k29k9RNrwZc1QkBvtyvLBIyhTjwKE/M9tX8YGxUk5lKRKIEArVmCvBGrODXw/ZVq/p3s1dyWPNMfCLlfINa3lA==", "type": "package", "path": "volo.abp.entityframeworkcore.mysql/0.3.6", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.EntityFrameworkCore.MySQL.dll", "lib/netstandard2.0/Volo.Abp.EntityFrameworkCore.MySQL.pdb", "volo.abp.entityframeworkcore.mysql.0.3.6.nupkg.sha512", "volo.abp.entityframeworkcore.mysql.nuspec"]}, "Volo.Abp.EventBus/0.15.0": {"sha512": "0E75JSwcSroaCcxHjgn69QWwCvp9OleXTxUV6uJVteQMR3r9wq4wYKgMAvgJZBMMJatOV4wCJWrn0/tleVUewg==", "type": "package", "path": "volo.abp.eventbus/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.EventBus.dll", "lib/netstandard2.0/Volo.Abp.EventBus.pdb", "volo.abp.eventbus.0.15.0.nupkg.sha512", "volo.abp.eventbus.nuspec"]}, "Volo.Abp.FeatureManagement.Domain/0.15.0": {"sha512": "t2Yu/ufuXx5qqm/sX5rFotZMUZeoVhk3ZW9L1EY2HTR27DXMOwshWJ/dt1XsJtXpRlDlJ0GCrA+eViYmD4rKCg==", "type": "package", "path": "volo.abp.featuremanagement.domain/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.dll", "lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.pdb", "volo.abp.featuremanagement.domain.0.15.0.nupkg.sha512", "volo.abp.featuremanagement.domain.nuspec"]}, "Volo.Abp.FeatureManagement.Domain.Shared/0.15.0": {"sha512": "pOzZ6FpDrOxw47YEvDS5G6CzV8CbtCwRiPt0oa27D1+Ht7+NebxevdNlArkR5f2W2M7u4ZCgz7+ZCWdlGhymoA==", "type": "package", "path": "volo.abp.featuremanagement.domain.shared/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.FeatureManagement.Domain.Shared.pdb", "volo.abp.featuremanagement.domain.shared.0.15.0.nupkg.sha512", "volo.abp.featuremanagement.domain.shared.nuspec"]}, "Volo.Abp.FeatureManagement.EntityFrameworkCore/0.15.0": {"sha512": "a+nZ3MAJ6F7eQ5iDZdjmpFaqBpTG8dwXoEPLBAYevAO2IpRc8LHqWqW3WmbKQeds/I99/l0O6WE1o7Tudltrnw==", "type": "package", "path": "volo.abp.featuremanagement.entityframeworkcore/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.FeatureManagement.EntityFrameworkCore.dll", "lib/netstandard2.0/Volo.Abp.FeatureManagement.EntityFrameworkCore.pdb", "volo.abp.featuremanagement.entityframeworkcore.0.15.0.nupkg.sha512", "volo.abp.featuremanagement.entityframeworkcore.nuspec"]}, "Volo.Abp.Features/0.15.0": {"sha512": "V6TYdx753IMtwiBMynVhMhGW7tqRc15nFB6d4QzMwF71pI6yJ2cXt7S6p5U2bGMZJQWU0hlMZFVdCtHX1Xyihw==", "type": "package", "path": "volo.abp.features/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Features.dll", "lib/netstandard2.0/Volo.Abp.Features.pdb", "volo.abp.features.0.15.0.nupkg.sha512", "volo.abp.features.nuspec"]}, "Volo.Abp.Guids/0.15.0": {"sha512": "mq2qYc+q3WXPtw1SJB+f1GIdhN2Z0jG69WMyioJ3tJebD5MNwFlySc4v4vu0G/gqwhKydoPVC1HqSOc712EWLw==", "type": "package", "path": "volo.abp.guids/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Guids.dll", "lib/netstandard2.0/Volo.Abp.Guids.pdb", "volo.abp.guids.0.15.0.nupkg.sha512", "volo.abp.guids.nuspec"]}, "Volo.Abp.Identity.Domain/0.3.0": {"sha512": "fO0OPrBpYUZrzxQ+zPjHk7MSmmWj8hAV6Q3lrMS/U0J0pu2oIBnb7RzN/RxawBXXjs0ia4AvDNjaWXQFLmhRwA==", "type": "package", "path": "volo.abp.identity.domain/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Identity.Domain.dll", "lib/netstandard2.0/Volo.Abp.Identity.Domain.pdb", "volo.abp.identity.domain.0.3.0.nupkg.sha512", "volo.abp.identity.domain.nuspec"]}, "Volo.Abp.Identity.Domain.Shared/0.3.0": {"sha512": "VNXIl8HvkyaHxSqAicL3p1aR1VCv74ROvZ7mmfVdOjpX450R4KnQ/80eFSFUatHodhytYg7wO/gxUHTNxmNYew==", "type": "package", "path": "volo.abp.identity.domain.shared/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.pdb", "volo.abp.identity.domain.shared.0.3.0.nupkg.sha512", "volo.abp.identity.domain.shared.nuspec"]}, "Volo.Abp.Identity.EntityFrameworkCore/0.3.0": {"sha512": "936r0I/kcwgOJp+b6W2jSy7N6b7tJJ6RpvvxcbywNdnMIrKAlxyBdbwGmXaPkcCpbBpnVu/TD47I2Phljmf+nA==", "type": "package", "path": "volo.abp.identity.entityframeworkcore/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Identity.EntityFrameworkCore.dll", "lib/netstandard2.0/Volo.Abp.Identity.EntityFrameworkCore.pdb", "volo.abp.identity.entityframeworkcore.0.3.0.nupkg.sha512", "volo.abp.identity.entityframeworkcore.nuspec"]}, "Volo.Abp.IdentityServer.Domain/0.6.0": {"sha512": "H9lxR9X/teOyIj2VjZ2vhZ84m7f8A/KiAFvmyk7wfiebI1DW7WwKKp41HAjWgziUZC0igzMjWYFmudCqExGVvw==", "type": "package", "path": "volo.abp.identityserver.domain/0.6.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.dll", "lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.pdb", "volo.abp.identityserver.domain.0.6.0.nupkg.sha512", "volo.abp.identityserver.domain.nuspec"]}, "Volo.Abp.IdentityServer.Domain.Shared/0.6.0": {"sha512": "6aBzeF2bcYBS8sOMP3BYdGJVNe6hoODLCfV/nyYsGVMOuwq3Zk2/NlPK2355EIHmhanx2NXsPsY3GaQrfdpQeA==", "type": "package", "path": "volo.abp.identityserver.domain.shared/0.6.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.IdentityServer.Domain.Shared.pdb", "volo.abp.identityserver.domain.shared.0.6.0.nupkg.sha512", "volo.abp.identityserver.domain.shared.nuspec"]}, "Volo.Abp.IdentityServer.EntityFrameworkCore/0.6.0": {"sha512": "XY9Y5S5NZerzJBJyXQo5qnnRxYf/+2vOwMbshkjbL3+vBlhsnAPH6BsoYRx901kqOIiyqzH8puSAw2N2BMv1Og==", "type": "package", "path": "volo.abp.identityserver.entityframeworkcore/0.6.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.IdentityServer.EntityFrameworkCore.dll", "lib/netstandard2.0/Volo.Abp.IdentityServer.EntityFrameworkCore.pdb", "volo.abp.identityserver.entityframeworkcore.0.6.0.nupkg.sha512", "volo.abp.identityserver.entityframeworkcore.nuspec"]}, "Volo.Abp.Json/0.15.0": {"sha512": "iTNXjdrAAB7SVEqAgcjCa4hP2uF9GeBxymjiGrrloL3DswMVDeZtRt4DlXBaVA9gw3nZEsFOwOQ9AC7yBB+4jw==", "type": "package", "path": "volo.abp.json/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Json.dll", "lib/netstandard2.0/Volo.Abp.Json.pdb", "volo.abp.json.0.15.0.nupkg.sha512", "volo.abp.json.nuspec"]}, "Volo.Abp.Localization/0.3.0": {"sha512": "VGjyBKlfsB9no2E2q09LS/I7O/Vbqg3mJcLwtXSNibXwwZFEDTKnqo4k3pPJfIFJmG+n2ThtzUG8OpA69Cgswg==", "type": "package", "path": "volo.abp.localization/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Localization.dll", "lib/netstandard2.0/Volo.Abp.Localization.pdb", "volo.abp.localization.0.3.0.nupkg.sha512", "volo.abp.localization.nuspec"]}, "Volo.Abp.Localization.Abstractions/0.15.0": {"sha512": "wCcrsNDKRCnMbdhPwIUnDlvdwDDsM/ifhKCoxajVmPLtpvaS9hk4+0XStV3Tg3KH6r+ke6swTYfTxTKe5GdmzA==", "type": "package", "path": "volo.abp.localization.abstractions/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.pdb", "volo.abp.localization.abstractions.0.15.0.nupkg.sha512", "volo.abp.localization.abstractions.nuspec"]}, "Volo.Abp.MultiTenancy.Abstractions/0.15.0": {"sha512": "mGMR9Cdpu9KES6wVCrTIFmsH06Rn/hgc3/v02BuDE7Ky3dvO14hXooK8wEZHB9xPbkkv8XKQgRye2WrhPVNQpA==", "type": "package", "path": "volo.abp.multitenancy.abstractions/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.MultiTenancy.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.MultiTenancy.Abstractions.pdb", "volo.abp.multitenancy.abstractions.0.15.0.nupkg.sha512", "volo.abp.multitenancy.abstractions.nuspec"]}, "Volo.Abp.ObjectExtending/2.4.0": {"sha512": "K8K3ZjbX7k6rF7Q1fBizcI2cSaG/qauDdq9lFoldpCuQbkgBzNup9KpcZytMKgnufIWtnFBx1+Ysn2ScIQIlaQ==", "type": "package", "path": "volo.abp.objectextending/2.4.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.ObjectExtending.dll", "lib/netstandard2.0/Volo.Abp.ObjectExtending.pdb", "volo.abp.objectextending.2.4.0.nupkg.sha512", "volo.abp.objectextending.nuspec"]}, "Volo.Abp.ObjectMapping/0.15.0": {"sha512": "ehV91mKc5F6ifuHnsx7nWShMloKD0ptT3Yk8NF70XM4B3wliuScg9rghHcvqcq8ago0E8ZvN/PSAUN4wCPcsrA==", "type": "package", "path": "volo.abp.objectmapping/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.ObjectMapping.dll", "lib/netstandard2.0/Volo.Abp.ObjectMapping.pdb", "volo.abp.objectmapping.0.15.0.nupkg.sha512", "volo.abp.objectmapping.nuspec"]}, "Volo.Abp.PermissionManagement.Domain/0.14.0": {"sha512": "ihpL/wtORKsUqdIwPBmrEdHQ3Za6kwQ+/U6LYMfuz3bfQ6fsWGZkIURx6pHeyBtbUWt8oIJbKVKa6Dcbdi9Hww==", "type": "package", "path": "volo.abp.permissionmanagement.domain/0.14.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.dll", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.pdb", "volo.abp.permissionmanagement.domain.0.14.0.nupkg.sha512", "volo.abp.permissionmanagement.domain.nuspec"]}, "Volo.Abp.PermissionManagement.Domain.Identity/0.13.0": {"sha512": "CMyCzb2EDG2Wq1CGONdMP7VG+DKMn6+K3f0q8DUEQw76r0kVQk7+rMna88SIZ6AqrnfQnsLmRNcY6twdNdvmQA==", "type": "package", "path": "volo.abp.permissionmanagement.domain.identity/0.13.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Identity.dll", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Identity.pdb", "volo.abp.permissionmanagement.domain.identity.0.13.0.nupkg.sha512", "volo.abp.permissionmanagement.domain.identity.nuspec"]}, "Volo.Abp.PermissionManagement.Domain.IdentityServer/0.14.0": {"sha512": "4I/GE7J+s2BzjusndIJlsZx8Kp080irVu26Fo7XuWnxYcAAUlU3FHasweQgjYo2+LXD5y6+uoWi/cNR73H0d7g==", "type": "package", "path": "volo.abp.permissionmanagement.domain.identityserver/0.14.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.IdentityServer.dll", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.IdentityServer.pdb", "volo.abp.permissionmanagement.domain.identityserver.0.14.0.nupkg.sha512", "volo.abp.permissionmanagement.domain.identityserver.nuspec"]}, "Volo.Abp.PermissionManagement.EntityFrameworkCore/0.3.0": {"sha512": "rw2SJpUy8TFThvDT0mdaZkmF4rh3aaSk1+jH8DU9j4vPSDODyCS+KnCMX7NQ0fsHbQXuXbzk2TMOdKzcEybx0A==", "type": "package", "path": "volo.abp.permissionmanagement.entityframeworkcore/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.PermissionManagement.EntityFrameworkCore.dll", "lib/netstandard2.0/Volo.Abp.PermissionManagement.EntityFrameworkCore.pdb", "volo.abp.permissionmanagement.entityframeworkcore.0.3.0.nupkg.sha512", "volo.abp.permissionmanagement.entityframeworkcore.nuspec"]}, "Volo.Abp.Security/0.15.0": {"sha512": "0mtjSbFhJg5ODGbOmN168YwqR/COQM62NJmMoAqzocVrv2wCL990kliKZJVE/8DL9ehrAMvS0+sZwJIHs9Oawg==", "type": "package", "path": "volo.abp.security/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Security.dll", "lib/netstandard2.0/Volo.Abp.Security.pdb", "volo.abp.security.0.15.0.nupkg.sha512", "volo.abp.security.nuspec"]}, "Volo.Abp.Serialization/0.15.0": {"sha512": "SUMtcUsoZ0ZgNjvjKg86hBfgiFJCv8i6DDyJ3CHJOlw2pE8CUI8QnIPAY57l3bF6tws3rviWoxc4Owd3y1AfEQ==", "type": "package", "path": "volo.abp.serialization/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Serialization.dll", "lib/netstandard2.0/Volo.Abp.Serialization.pdb", "volo.abp.serialization.0.15.0.nupkg.sha512", "volo.abp.serialization.nuspec"]}, "Volo.Abp.SettingManagement.Domain/0.3.0": {"sha512": "Z2M0QLZxyB6cSxYAumrdXW/U1mMg9wj7f2E06S7XRMmdrLF4AjjpixRIPbYSrqcXrV6D7zqxDpUvteeMEWOu/Q==", "type": "package", "path": "volo.abp.settingmanagement.domain/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.dll", "lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.pdb", "volo.abp.settingmanagement.domain.0.3.0.nupkg.sha512", "volo.abp.settingmanagement.domain.nuspec"]}, "Volo.Abp.SettingManagement.Domain.Shared/0.3.0": {"sha512": "lVxsLlQf7ZalX7GtDD3K6KRUcStqb6FiVDCBs4HW0V60qp2lO9P2Ei681TJAO6aTPk0DSvaoyDbtK7ls0fZyGQ==", "type": "package", "path": "volo.abp.settingmanagement.domain.shared/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.Shared.pdb", "volo.abp.settingmanagement.domain.shared.0.3.0.nupkg.sha512", "volo.abp.settingmanagement.domain.shared.nuspec"]}, "Volo.Abp.SettingManagement.EntityFrameworkCore/0.3.0": {"sha512": "LL/hGpXfxeM2xSPaDqbI+6mx3awyXIJtnILxP4v9pfZUoIf+AdIbIzjsK75aDXpDSmjpdMlTcfUf/Zso6gudoA==", "type": "package", "path": "volo.abp.settingmanagement.entityframeworkcore/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.SettingManagement.EntityFrameworkCore.dll", "lib/netstandard2.0/Volo.Abp.SettingManagement.EntityFrameworkCore.pdb", "volo.abp.settingmanagement.entityframeworkcore.0.3.0.nupkg.sha512", "volo.abp.settingmanagement.entityframeworkcore.nuspec"]}, "Volo.Abp.Settings/0.3.0": {"sha512": "kriv+CdU0Yc1Io2oC+t3rAkh3q8g/vAaMcJlQwCicQkQcaCmOBZ03r9U/nr/YegSU/Lrsm4jjfvbiLe0Zk2gqw==", "type": "package", "path": "volo.abp.settings/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Settings.dll", "lib/netstandard2.0/Volo.Abp.Settings.pdb", "volo.abp.settings.0.3.0.nupkg.sha512", "volo.abp.settings.nuspec"]}, "Volo.Abp.TenantManagement.Domain/0.3.0": {"sha512": "Pw6jYsFRIiaozKY3H7QT+w+ofN3VLQZjlKPwhK0uPqW0RDWt2feKwuPJ3pHqdaAlViCfNkXdBvVut0EmOacMiw==", "type": "package", "path": "volo.abp.tenantmanagement.domain/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.TenantManagement.Domain.dll", "lib/netstandard2.0/Volo.Abp.TenantManagement.Domain.pdb", "volo.abp.tenantmanagement.domain.0.3.0.nupkg.sha512", "volo.abp.tenantmanagement.domain.nuspec"]}, "Volo.Abp.TenantManagement.Domain.Shared/0.3.0": {"sha512": "e6rOKpbOX0e0l+0juEcKom9t/4tEL6mtzjkhthLWwCeXYwCkMt7CUMWNIMP+7j9j7EQBsXOnIqd4lcsh19pI1g==", "type": "package", "path": "volo.abp.tenantmanagement.domain.shared/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.TenantManagement.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.TenantManagement.Domain.Shared.pdb", "volo.abp.tenantmanagement.domain.shared.0.3.0.nupkg.sha512", "volo.abp.tenantmanagement.domain.shared.nuspec"]}, "Volo.Abp.TenantManagement.EntityFrameworkCore/0.3.0": {"sha512": "ELU/OtsKuegC3RktODeISBFqDewRFjBNXZL1nQlPeYfLOWZf5qrK/bcBqFaWNCQvYntR4GXjGqZnIe6icQ8nGg==", "type": "package", "path": "volo.abp.tenantmanagement.entityframeworkcore/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.TenantManagement.EntityFrameworkCore.dll", "lib/netstandard2.0/Volo.Abp.TenantManagement.EntityFrameworkCore.pdb", "volo.abp.tenantmanagement.entityframeworkcore.0.3.0.nupkg.sha512", "volo.abp.tenantmanagement.entityframeworkcore.nuspec"]}, "Volo.Abp.Threading/0.15.0": {"sha512": "ZjmNZh2vi2GxCmgcwYFFpDFbzJcQWWbPmvZlZgT66XwiDNhXvFfs0e1ddiwPn6xuw53lhzGuWw3Jefu6y5ojug==", "type": "package", "path": "volo.abp.threading/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Threading.dll", "lib/netstandard2.0/Volo.Abp.Threading.pdb", "volo.abp.threading.0.15.0.nupkg.sha512", "volo.abp.threading.nuspec"]}, "Volo.Abp.Timing/0.15.0": {"sha512": "zYEz2T8J9VAv5Mq7DW48e3fC2qYA/Y1ZbLqk8+ojha24Q4Ul9rEPmj6yJLYaaUmSQL0IiBmuyJ5mwcFPmpbkag==", "type": "package", "path": "volo.abp.timing/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Timing.dll", "lib/netstandard2.0/Volo.Abp.Timing.pdb", "volo.abp.timing.0.15.0.nupkg.sha512", "volo.abp.timing.nuspec"]}, "Volo.Abp.UI/0.3.0": {"sha512": "zG629M51SfmdL8Zoqti+GCwmOKvKjD4AjsssfZTVfu8t7esprDueYtnVk6sTbsvtx4pzAbYkQnQrsS/tOaZqnw==", "type": "package", "path": "volo.abp.ui/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.UI.dll", "lib/netstandard2.0/Volo.Abp.UI.pdb", "volo.abp.ui.0.3.0.nupkg.sha512", "volo.abp.ui.nuspec"]}, "Volo.Abp.Uow/0.15.0": {"sha512": "n4b8jxmMLuIuxjUD8eIHgdNupTDUgmSzN0QKOKbdTlZNop91Na8310bLyeUi8tDC/TZ3iaK8iOl/oskmSxKHmQ==", "type": "package", "path": "volo.abp.uow/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Uow.dll", "lib/netstandard2.0/Volo.Abp.Uow.pdb", "volo.abp.uow.0.15.0.nupkg.sha512", "volo.abp.uow.nuspec"]}, "Volo.Abp.Users.Abstractions/0.3.0": {"sha512": "22aeRRttmoNAM8qonoxPXQobnqTQcP1a1h/8/5+MLrvrwjccfNCOeJxkpLiXF7SoJx8C6xaV+bR4G9YZHFKo6A==", "type": "package", "path": "volo.abp.users.abstractions/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Users.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Users.Abstractions.pdb", "volo.abp.users.abstractions.0.3.0.nupkg.sha512", "volo.abp.users.abstractions.nuspec"]}, "Volo.Abp.Users.Domain/0.3.0": {"sha512": "Y2kBSKqtAT48u061ZK2fIgUnnVRn6jWNtLRkCOBPWGGc3a1E6yQ6CPmTqHlmOJueGryMs1jF/BG78tPe7ix9tA==", "type": "package", "path": "volo.abp.users.domain/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Users.Domain.dll", "lib/netstandard2.0/Volo.Abp.Users.Domain.pdb", "volo.abp.users.domain.0.3.0.nupkg.sha512", "volo.abp.users.domain.nuspec"]}, "Volo.Abp.Users.Domain.Shared/0.3.0": {"sha512": "7C21JDHOg1BZ/hWQGjs2shLETJ8dn0bH/kIoWVLEQ18Pra4GIeT2MURaut/tnZoDibuXKj6k3JRiB0iUSGMtnw==", "type": "package", "path": "volo.abp.users.domain.shared/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.pdb", "volo.abp.users.domain.shared.0.3.0.nupkg.sha512", "volo.abp.users.domain.shared.nuspec"]}, "Volo.Abp.Users.EntityFrameworkCore/0.3.0": {"sha512": "J8ulr6LnNieRclxMOU/dvhRU8q+vuer7AM79tz9esH2wNvfqHqC6K3TC8vG/rNNznzBOD8vrXppjIIpawVx+VQ==", "type": "package", "path": "volo.abp.users.entityframeworkcore/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Users.EntityFrameworkCore.dll", "lib/netstandard2.0/Volo.Abp.Users.EntityFrameworkCore.pdb", "volo.abp.users.entityframeworkcore.0.3.0.nupkg.sha512", "volo.abp.users.entityframeworkcore.nuspec"]}, "Volo.Abp.Validation/0.3.0": {"sha512": "qu/vW3VES3VjBu/2aj1P8MSkxfEo/ihD05i7Io9CQm5oKFFlx23q6cgxEpnhhMcQIb1Rw5MjvigWozrioFZlKA==", "type": "package", "path": "volo.abp.validation/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Validation.dll", "lib/netstandard2.0/Volo.Abp.Validation.pdb", "volo.abp.validation.0.3.0.nupkg.sha512", "volo.abp.validation.nuspec"]}, "Volo.Abp.VirtualFileSystem/0.3.0": {"sha512": "+bvsvGJbq2tYGOHMPWEQEJlCESgdB5JMxN2VD0t8vAKpoPlwNMVSG+kNJS+bBYF9NxYpJkUPCdqnXZzWTkKwdg==", "type": "package", "path": "volo.abp.virtualfilesystem/0.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.pdb", "volo.abp.virtualfilesystem.0.3.0.nupkg.sha512", "volo.abp.virtualfilesystem.nuspec"]}, "TSZ.ServiceBase.FileStorageCenter.Domain/1.0.0": {"type": "project", "path": "../2_Domain/TSZ.ServiceBase.FileStorageCenter.Domain.csproj", "msbuildProject": "../2_Domain/TSZ.ServiceBase.FileStorageCenter.Domain.csproj"}, "TSZ.ServiceBase.FileStorageCenter.Domain.Shared/1.0.0": {"type": "project", "path": "../2_Domain.Shared/TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj", "msbuildProject": "../2_Domain.Shared/TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["TSZ.Abp.Modulies", "TSZ.ServiceBase.FileStorageCenter.Domain >= 1.0.0", "Volo.Abp.AuditLogging.EntityFrameworkCore", "Volo.Abp.BackgroundJobs.EntityFrameworkCore", "Volo.Abp.EntityFrameworkCore.MySQL", "Volo.Abp.FeatureManagement.EntityFrameworkCore", "Volo.Abp.Identity.EntityFrameworkCore", "Volo.Abp.IdentityServer.EntityFrameworkCore", "Volo.Abp.PermissionManagement.EntityFrameworkCore", "Volo.Abp.SettingManagement.EntityFrameworkCore", "Volo.Abp.TenantManagement.EntityFrameworkCore"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\4_EntityFrameworkCore\\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.csproj", "projectName": "TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\4_EntityFrameworkCore\\TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\4_EntityFrameworkCore\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain\\TSZ.ServiceBase.FileStorageCenter.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"TSZ.Abp.Modulies": {"target": "Package", "version": "(, )"}, "Volo.Abp.AuditLogging.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.BackgroundJobs.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.EntityFrameworkCore.MySQL": {"target": "Package", "version": "(, )"}, "Volo.Abp.FeatureManagement.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.Identity.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.IdentityServer.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.PermissionManagement.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.SettingManagement.EntityFrameworkCore": {"target": "Package", "version": "(, )"}, "Volo.Abp.TenantManagement.EntityFrameworkCore": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 TSZ.Abp.Modulies 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "TSZ.Abp.Modulies", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.AuditLogging.EntityFrameworkCore 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.AuditLogging.EntityFrameworkCore", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.BackgroundJobs.EntityFrameworkCore 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.BackgroundJobs.EntityFrameworkCore", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.EntityFrameworkCore.MySQL 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.EntityFrameworkCore.MySQL", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.FeatureManagement.EntityFrameworkCore 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.FeatureManagement.EntityFrameworkCore", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.Identity.EntityFrameworkCore 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.Identity.EntityFrameworkCore", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.IdentityServer.EntityFrameworkCore 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.IdentityServer.EntityFrameworkCore", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.PermissionManagement.EntityFrameworkCore 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.PermissionManagement.EntityFrameworkCore", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.SettingManagement.EntityFrameworkCore 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.SettingManagement.EntityFrameworkCore", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "项目依赖项 Volo.Abp.TenantManagement.EntityFrameworkCore 不具有包含下限。在依赖项版本中包含下限可确保一致的还原结果。", "libraryId": "Volo.Abp.TenantManagement.EntityFrameworkCore", "targetGraphs": [".NETCoreApp,Version=v9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Microsoft.Extensions.FileProviders.Embedded 的下限(含)。已改为解析 Microsoft.Extensions.FileProviders.Embedded 1.0.0。", "libraryId": "Microsoft.Extensions.FileProviders.Embedded", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.AuditLogging.EntityFrameworkCore 0.4.1 不提供依赖项 Volo.Abp.AuditLogging.Domain 的下限(含)。已改为解析 Volo.Abp.AuditLogging.Domain 0.4.1。", "libraryId": "Volo.Abp.AuditLogging.Domain", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.AuditLogging.Domain 0.4.1 不提供依赖项 Volo.Abp.AuditLogging.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.AuditLogging.Domain.Shared 0.4.1。", "libraryId": "Volo.Abp.AuditLogging.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.BackgroundJobs.EntityFrameworkCore 0.4.1 不提供依赖项 Volo.Abp.BackgroundJobs.Domain 的下限(含)。已改为解析 Volo.Abp.BackgroundJobs.Domain 0.4.1。", "libraryId": "Volo.Abp.BackgroundJobs.Domain", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.BackgroundJobs.Domain 0.4.1 不提供依赖项 Volo.Abp.BackgroundJobs.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.BackgroundJobs.Domain.Shared 0.4.1。", "libraryId": "Volo.Abp.BackgroundJobs.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "TSZ.ServiceBase.FileStorageCenter.Domain 不提供依赖项 Volo.Abp.Emailing 的下限(含)。已改为解析 Volo.Abp.Emailing 0.3.0。", "libraryId": "Volo.Abp.Emailing", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.FeatureManagement.EntityFrameworkCore 0.15.0 不提供依赖项 Volo.Abp.FeatureManagement.Domain 的下限(含)。已改为解析 Volo.Abp.FeatureManagement.Domain 0.15.0。", "libraryId": "Volo.Abp.FeatureManagement.Domain", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.FeatureManagement.Domain 0.15.0 不提供依赖项 Volo.Abp.FeatureManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.FeatureManagement.Domain.Shared 0.15.0。", "libraryId": "Volo.Abp.FeatureManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.Identity.EntityFrameworkCore 0.3.0 不提供依赖项 Volo.Abp.Identity.Domain 的下限(含)。已改为解析 Volo.Abp.Identity.Domain 0.3.0。", "libraryId": "Volo.Abp.Identity.Domain", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.Identity.Domain 0.3.0 不提供依赖项 Volo.Abp.Identity.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.Identity.Domain.Shared 0.3.0。", "libraryId": "Volo.Abp.Identity.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.IdentityServer.EntityFrameworkCore 0.6.0 不提供依赖项 Volo.Abp.IdentityServer.Domain 的下限(含)。已改为解析 Volo.Abp.IdentityServer.Domain 0.6.0。", "libraryId": "Volo.Abp.IdentityServer.Domain", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.IdentityServer.Domain 0.6.0 不提供依赖项 Volo.Abp.IdentityServer.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.IdentityServer.Domain.Shared 0.6.0。", "libraryId": "Volo.Abp.IdentityServer.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.Localization 的下限(含)。已改为解析 Volo.Abp.Localization 0.3.0。", "libraryId": "Volo.Abp.Localization", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.ObjectExtending 的下限(含)。已改为解析 Volo.Abp.ObjectExtending 2.4.0。", "libraryId": "Volo.Abp.ObjectExtending", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "TSZ.ServiceBase.FileStorageCenter.Domain 不提供依赖项 Volo.Abp.PermissionManagement.Domain.Identity 的下限(含)。已改为解析 Volo.Abp.PermissionManagement.Domain.Identity 0.13.0。", "libraryId": "Volo.Abp.PermissionManagement.Domain.Identity", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "TSZ.ServiceBase.FileStorageCenter.Domain 不提供依赖项 Volo.Abp.PermissionManagement.Domain.IdentityServer 的下限(含)。已改为解析 Volo.Abp.PermissionManagement.Domain.IdentityServer 0.14.0。", "libraryId": "Volo.Abp.PermissionManagement.Domain.IdentityServer", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.SettingManagement.EntityFrameworkCore 0.3.0 不提供依赖项 Volo.Abp.SettingManagement.Domain 的下限(含)。已改为解析 Volo.Abp.SettingManagement.Domain 0.3.0。", "libraryId": "Volo.Abp.SettingManagement.Domain", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.SettingManagement.Domain 0.3.0 不提供依赖项 Volo.Abp.SettingManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.SettingManagement.Domain.Shared 0.3.0。", "libraryId": "Volo.Abp.SettingManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.TenantManagement.EntityFrameworkCore 0.3.0 不提供依赖项 Volo.Abp.TenantManagement.Domain 的下限(含)。已改为解析 Volo.Abp.TenantManagement.Domain 0.3.0。", "libraryId": "Volo.Abp.TenantManagement.Domain", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Volo.Abp.TenantManagement.Domain 0.3.0 不提供依赖项 Volo.Abp.TenantManagement.Domain.Shared 的下限(含)。已改为解析 Volo.Abp.TenantManagement.Domain.Shared 0.3.0。", "libraryId": "Volo.Abp.TenantManagement.Domain.Shared", "targetGraphs": ["net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared 不提供依赖项 Volo.Abp.Validation 的下限(含)。已改为解析 Volo.Abp.Validation 0.3.0。", "libraryId": "Volo.Abp.Validation", "targetGraphs": ["net9.0"]}, {"code": "NU1101", "level": "Error", "message": "找不到包 TSZ.Abp.Modulies。源 Microsoft Visual Studio Offline Packages, nuget.org, 国内 中不存在具有此 ID 的包", "libraryId": "TSZ.Abp.Modulies", "targetGraphs": ["net9.0"]}, {"code": "NU1107", "level": "Error", "message": "Volo.Abp.PermissionManagement.Domain.Shared 中检测到版本冲突。直接安装/引用 Volo.Abp.PermissionManagement.Domain.Shared 0.3.0 到项目 TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore 可解决此问题。 \r\n TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore -> TSZ.ServiceBase.FileStorageCenter.Domain -> TSZ.ServiceBase.FileStorageCenter.Domain.Shared -> Volo.Abp.PermissionManagement.Domain.Shared \r\n TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore -> Volo.Abp.PermissionManagement.EntityFrameworkCore 0.3.0 -> Volo.Abp.PermissionManagement.Domain 0.14.0 -> Volo.Abp.PermissionManagement.Domain.Shared (>= 0.14.0).", "libraryId": "Volo.Abp.PermissionManagement.Domain.Shared", "targetGraphs": ["net9.0"]}]}