﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using TSZ.Common.Shared;
using TSZ.ServiceBase.FileStorageCenter.FileStorageCenter;
using TSZ.Common.Core.Helper;
using Volo.Abp.Application.Services;
using TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto;
using static TSZ.Common.Shared.CompressHelper;
using Newtonsoft.Json;
using System.Buffers;
using Volo.Abp.Uow;
using FluentFTP;
using Microsoft.IO;
using TSZ.Common.Services.DataService;

namespace TSZ.ServiceBase.FileStorageCenter
{
    /// <summary>
    /// 
    /// </summary>
    //[Authorize]
    /* Inherit your application services from this class.
     */
    public class FileStorageCenterAppService : FileApplicationService, IFileStorageCenterAppService
    {
        /// <summary>
        /// 
        /// </summary>
        public readonly static string MyInstanceId = Guid.NewGuid().ToString().ToUpper();
        private IHttpContextAccessor _httpContextAccessor;
        /// <summary>
        /// 上传下载操作是否启用高性能的内存操作（大数据对象，字节数组，1MB大小）
        /// </summary>
        private static readonly bool IsUserHighPerformanceAPI = false;

        /// <summary>
        /// 
        /// </summary>
        protected virtual HttpRequest Request
        {
            get
            {
                return _httpContextAccessor.HttpContext.Request;
            }
        }
        private static readonly DataServiceImpl instance = new DataServiceImpl();
        /// <summary>
        /// 
        /// </summary>
        public const string Application_Octet_Stream_ContentType = "application/octet-stream";
        /// <summary>
        /// 
        /// </summary>
        private FtpClient _ftpClient;
        /// <summary>
        /// 
        /// </summary>
        private bool _windowspath;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpContextAccessor">IHttpContextAccessor上下文接口</param>
        /// <param name="configuration">IConfiguration配置接口</param>
        public FileStorageCenterAppService(IHttpContextAccessor httpContextAccessor, IConfiguration configuration)
            : base(configuration)
        {
            this._httpContextAccessor = httpContextAccessor;

            var ftpIp = configuration["ServiceBase_FileStorageCenter:ftp:ip"];
            var ftpport = configuration["ServiceBase_FileStorageCenter:ftp:port"];
            var username = configuration["ServiceBase_FileStorageCenter:ftp:username"];
            var password = configuration["ServiceBase_FileStorageCenter:ftp:password"];
            var windowspath = configuration["ServiceBase_FileStorageCenter:ftp:windowspath"];
            if (!ftpIp.IsNullOrEmpty() &&
                !ftpport.IsNullOrEmpty() &&
                !username.IsNullOrEmpty() &&
                !password.IsNullOrEmpty() &&
                !windowspath.IsNullOrEmpty())
            {
                this._ftpClient = new FtpClient(ftpIp);
                this._ftpClient.Port = Convert.ToInt32(ftpport);
                this._ftpClient.Credentials = new System.Net.NetworkCredential(username, password);
                this._windowspath = windowspath.ToLower() == "true";
            }
        }

        #region 常规文件上传下载复制移动
        /// <summary>
        /// 上传文件，分割上传（也可以理解为分片上传，但是不需要合并，每次上传都是直接追加到文件的末尾，顺序一直上传，直到上传全部完成）
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名）</param>
        /// <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult<string>> UploadFile(string strFullFileName, int uploadMode, bool isEncryptDecrypt)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            byte[] buffer = Array.Empty<byte>();
            ServiceResult<string> serviceResult = null;

            if (IsUserHighPerformanceAPI)
            {
                ValueTuple<byte[], long> decompress;
                int intCount = 0;

                #region 兼容 form-data提交的文件
                if (Request.HasFormContentType)
                {
                    try
                    {
                        //理论上只允许一次上传一个文件，如果有多个文件了，则批量添加到同一个服务器文件的末尾，视为一个文件来处理                      
                        foreach (var postFile in Request.Form.Files)
                        {
                            using (var stream = postFile.OpenReadStream())
                            {
                                decompress = CompressHelper.DeCompressBytesPerformance(stream);
                                buffer = decompress.Item1;
                                intCount = (int)decompress.Item2;
                            }
                            break;
                        }

                        serviceResult = instance.UploadFile(strFullFileName, buffer, 0, intCount, uploadMode, isEncryptDecrypt);
                        buffer = null;
                        return serviceResult;
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex.ToString());
                    }
                }
                #endregion

                long intContentLength = 0;
                if (Request.ContentLength.HasValue)
                {
                    intContentLength = Request.ContentLength.Value;
                }
                if (intContentLength > 0)
                {
                    using (var memoryStream = StreamPool.RecyclableMemoryStreamManagerInstance.GetStream())
                    {
                        await Request.Body.CopyToAsync(memoryStream);
                        decompress = CompressHelper.DeCompressBytesPerformance(memoryStream.GetBuffer(), 0, (int)memoryStream.Length);
                        buffer = decompress.Item1;
                        intCount = (int)decompress.Item2;
                    }
                }
                serviceResult = instance.UploadFile(strFullFileName, buffer, 0, intCount, uploadMode, isEncryptDecrypt);
                buffer = null;
                return serviceResult;
            }
            else
            {
                #region 兼容 form-data提交的文件
                if (Request.HasFormContentType)
                {
                    try
                    {
                        //理论上只允许一次上传一个文件，如果有多个文件了，则批量添加到同一个服务器文件的末尾，视为一个文件来处理
                        using (MemoryStream ms = StreamPool.RecyclableMemoryStreamManagerInstance.GetStream())
                        {
                            foreach (var postFile in Request.Form.Files)
                            {
                                buffer = await postFile.GetAllBytesAsync();
                                buffer = TSZ.Common.Shared.CompressHelper.DeCompressBytes(buffer);
                                ms.Write(buffer, 0, buffer.Length);
                            }
                            buffer = ms.ToArray();
                        }
                        serviceResult = instance.UploadFile(strFullFileName, buffer, uploadMode, isEncryptDecrypt);
                        buffer = null;
                        return serviceResult;
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex.ToString());
                    }
                }
                #endregion

                long intContentLength = 0;
                if (Request.ContentLength.HasValue)
                {
                    intContentLength = Request.ContentLength.Value;
                }
                if (intContentLength > 0)
                {
                    //using (MemoryStream ms = new MemoryStream((int)intContentLength))
                    using (MemoryStream ms = StreamPool.RecyclableMemoryStreamManagerInstance.GetStream(null, intContentLength))
                    {
                        await Request.Body.CopyToAsync(ms);
                        buffer = TSZ.Common.Shared.CompressHelper.DeCompressBytes(ms.ToArray());
                    }
                }
                serviceResult = instance.UploadFile(strFullFileName, buffer, uploadMode, isEncryptDecrypt);
                buffer = null;
                return serviceResult;
            }
        }

        /// <summary>
        /// 分片上传文件，不管分片数量多少，后续都必须调用FileMerge合并文件
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <param name="identifier">分片标识符</param>
        /// <param name="chunkNumber">分片号</param>
        /// <param name="chunkSize">分片大小</param>
        /// <param name="totalChunks">总分片数量，不管分片数量多少，后续都必须调用FileMerge合并文件</param>
        /// <param name="totalSize">文件总大小</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        [UnitOfWork(IsDisabled = true)]
        public async Task<ServiceResult<string>> UploadFileChunk(string strFullFileName, bool isEncryptDecrypt, string identifier, int chunkNumber, long chunkSize, int totalChunks, long totalSize)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            byte[] buffer = Array.Empty<byte>();
            try
            {
                if (totalChunks <= 0)
                {
                    //不分块直接上传
                    return ServiceResult.Failed<string>("分片信息错误");
                }

                ValueTuple<byte[], long> decompress;
                int intCount = 0;

                if (IsUserHighPerformanceAPI)
                {
                    #region 兼容 form-data提交的文件
                    if (Request.HasFormContentType)
                    {
                        try
                        {
                            //理论上只允许一次上传一个文件，如果有多个文件了，则批量添加到同一个服务器文件的末尾，视为一个文件来处理                      
                            foreach (var postFile in Request.Form.Files)
                            {
                                using (var stream = postFile.OpenReadStream())
                                {
                                    decompress = CompressHelper.DeCompressBytesPerformance(stream);
                                    buffer = decompress.Item1;
                                    intCount = (int)decompress.Item2;
                                }
                                break;
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.LogError(ex.ToString());
                        }
                    }
                    #endregion
                    else
                    {
                        //没有文件内容，则重新从Body获取
                        long intContentLength = 0;
                        if (Request.ContentLength.HasValue)
                        {
                            intContentLength = Request.ContentLength.Value;
                        }
                        if (intContentLength > 0)
                        {
                            using (var memoryStream = StreamPool.RecyclableMemoryStreamManagerInstance.GetStream())
                            {
                                await Request.Body.CopyToAsync(memoryStream);
                                decompress = CompressHelper.DeCompressBytesPerformance(memoryStream.GetBuffer(), 0, (int)memoryStream.Length);
                                buffer = decompress.Item1;
                                intCount = (int)decompress.Item2;
                            }
                        }
                    }
                }
                else
                {
                    #region 兼容 form-data提交的文件
                    if (Request.HasFormContentType)
                    {
                        try
                        {
                            //理论上只允许一次上传一个文件，如果有多个文件了，则批量添加到同一个服务器文件的末尾，视为一个文件来处理
                            using (MemoryStream ms = StreamPool.RecyclableMemoryStreamManagerInstance.GetStream())
                            {
                                foreach (var postFile in Request.Form.Files)
                                {
                                    buffer = await postFile.GetAllBytesAsync();
                                    buffer = TSZ.Common.Shared.CompressHelper.DeCompressBytes(buffer);
                                    ms.Write(buffer, 0, buffer.Length);
                                }
                                ms.Flush();
                                buffer = ms.ToArray();
                                intCount = buffer.Length;
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.LogError(ex.ToString());
                        }
                    }
                    #endregion
                    else
                    {
                        //没有文件内容，则重新从Body获取
                        long intContentLength = 0;
                        if (Request.ContentLength.HasValue)
                        {
                            intContentLength = Request.ContentLength.Value;
                        }
                        if (intContentLength > 0)
                        {
                            using (MemoryStream ms = StreamPool.RecyclableMemoryStreamManagerInstance.GetStream(null, intContentLength))
                            {
                                await Request.Body.CopyToAsync(ms);
                                buffer = TSZ.Common.Shared.CompressHelper.DeCompressBytes(ms.ToArray());
                                intCount = buffer.Length;
                            }
                        }
                    }
                }
                string strSavePath = PubMethod.GetDirectoryName(strFullFileName);
                string strTemporaryPath = PubMethod.Combine(strSavePath, identifier);  //临时保存分块的目录
                try
                {
                    if (!Directory.Exists(strTemporaryPath))
                    {
                        Directory.CreateDirectory(strTemporaryPath);
                    }
                    string strTemporaryFile = PubMethod.Combine(strTemporaryPath, identifier + "_" + chunkNumber.ToString());
                    using (CryptoFileStream fs = new CryptoFileStream(strTemporaryFile, FileMode.Append, FileAccess.Write, FileShare.ReadWrite))
                    {
                        fs.IsEncryptDecrypt = isEncryptDecrypt;
                        if (buffer != null && intCount > 0)
                        {
                            fs.Write(buffer, 0, intCount);
                        }
                        fs.Flush();
                        fs.Close();
                    }
                    return ServiceResult.Success(string.Empty);
                }
                catch
                {
                    PubMethod.DeleteFolder(strTemporaryPath);
                    throw;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex.ToString());
                return ServiceResult.Failed<string>(ex.Message);
            }
            finally
            {
                buffer = null;
            }
        }

        /// <summary>
        /// 合并分片
        /// </summary>
        /// <param name="strFullFileName">完成服务器文件名</param>
        /// <param name="fileUploadBehaviour"></param>
        /// <param name="identifier"></param>
        /// <param name="totalChunks"></param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult<string>> FileMerge(string strFullFileName, FileUploadBehaviours fileUploadBehaviour, string identifier, int totalChunks)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            string strSavePath = PubMethod.GetDirectoryName(strFullFileName);
            string strTemporaryPath = PubMethod.Combine(strSavePath, identifier);  //临时保存分块的目录
            try
            {
                bool isCheckChunkFileDone = true;
                if (totalChunks <= 0)
                {
                    return ServiceResult.Failed<string>("分片信息错误");
                }
                for (int chunkNumber = 0; chunkNumber < totalChunks; chunkNumber++)
                {
                    //分片的序号从0开始
                    string filePath = PubMethod.Combine(strTemporaryPath, identifier + "_" + chunkNumber.ToString());
                    if (!System.IO.File.Exists(filePath))
                    {
                        isCheckChunkFileDone = false;
                        break;
                    }
                }
                if (!isCheckChunkFileDone)
                {
                    //分片信息有误
                    return ServiceResult.Failed<string>("分片信息错误");
                }
                else
                {
                    string strNewFileName = null;
                    //合并分片
                    await Task.Run(() =>
                    {
                        #region 准备工作                     
                        if (!Directory.Exists(strSavePath))
                        {
                            Directory.CreateDirectory(strSavePath);
                        }
                        FileAttribute fileInfo = new FileAttribute(strFullFileName);
                        bool isFileReadOnly = false;
                        if (fileInfo.Exists && fileInfo.IsReadOnly)
                        {
                            isFileReadOnly = true;
                            fileInfo.IsReadOnly = false;
                        }

                        switch (fileUploadBehaviour)
                        {
                            case FileUploadBehaviours.CreateNew:
                                if (System.IO.File.Exists(strFullFileName))
                                {
                                    do
                                    {
                                        strNewFileName = PubMethod.CreateGuidKey();
                                        string strExtension = Path.GetExtension(strFullFileName);
                                        if (strExtension != null && strExtension.Length > 0)
                                        {
                                            strNewFileName += strExtension;
                                        }
                                        strFullFileName = PubMethod.Combine(strSavePath, strNewFileName);
                                    }
                                    while (System.IO.File.Exists(strFullFileName));
                                }
                                break;
                            case FileUploadBehaviours.Override:
                                if (System.IO.File.Exists(strFullFileName))
                                {
                                    System.IO.File.Delete(strFullFileName);
                                }
                                break;
                            case FileUploadBehaviours.ResumeFromBreakPoint:
                                break;
                        }

                        #endregion

                        //打开现有文件或者创建新文件进行写入
                        using (FileStream fileSteam = new FileStream(strFullFileName, FileMode.OpenOrCreate, FileAccess.Write, FileShare.None))
                        {
                            for (int chunkNumber = 0; chunkNumber < totalChunks; chunkNumber++)
                            {
                                string strTemporaryFile = PubMethod.Combine(strTemporaryPath, identifier + "_" + chunkNumber.ToString());
                                using (FileStream stream = PubMethod.OpenRead(strTemporaryFile))
                                {
                                    stream.Position = 0;
                                    byte[] buffer = new byte[1024 * 1024];
                                    int intReadCount = stream.Read(buffer, 0, buffer.Length);
                                    while (intReadCount > 0)
                                    {
                                        fileSteam.Write(buffer, 0, intReadCount);
                                        intReadCount = stream.Read(buffer, 0, buffer.Length);
                                    }
                                    buffer = null;
                                }
                            }
                            fileSteam.Flush();
                            fileSteam.Close();
                        }

                        if (isFileReadOnly)
                        {
                            fileInfo.IsReadOnly = true;
                        }
                    });
                    if (strNewFileName != null && strNewFileName.Length > 0)
                    {
                        return ServiceResult.Success(strNewFileName);
                    }
                    return ServiceResult.Success(string.Empty);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex.ToString());
                return ServiceResult.Failed<string>(ex.ToString());
            }
            finally
            {
                //删除临时目录及其下的所有分片文件
                PubMethod.DeleteFolder(strTemporaryPath);
            }
        }

        /// <summary>
        /// 不可取，不能处理多线程同步（mjy）
        /// 超大文件，按照1MB来分片，导致服务器上临时文件太多，1GB = 1024个1MB的分片文件，碎片化严重
        /// 特根据totalChunks来变换chunkNumber的值，让多碎片文件在服务器上临时存储为几个分片，比如1GB文件 = 1,2,3,4,5,6个分片
        /// </summary>
        /// <param name="chunkNumber"></param>
        /// <param name="totalChunks"></param>
        /// <returns></returns>
        private static int GetChunkNumberTranslateToGroup(int chunkNumber, int totalChunks)
        {
            int groupCount = 6;
            if (totalChunks <= groupCount)
            {
                return chunkNumber;
            }
            int groupSize = totalChunks / groupCount;
            int newChunkNumber = chunkNumber / groupSize;
            return newChunkNumber;
        }

        /// <summary>
        /// 下载文件，分割下载，一次只下载 FileServiceConfig.MAXFILESIZE（默认为1MB）大小，顺序一直下载，直到下载全部完成
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名）</param>
        /// <param name="intReadCount">文件读取的次数（第几次，从0开始计数）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpPost]
        public async Task<FileContentResult> DownloadFile(string strFullFileName, int intReadCount, bool isEncryptDecrypt)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            byte[] buffer = instance.DownloadFile(strFullFileName, intReadCount, isEncryptDecrypt);
            if (buffer == null)
            {
                buffer = Array.Empty<byte>();
            }
            else
            {
                if (buffer.Length > 0)
                {
                    buffer = TSZ.Common.Shared.CompressHelper.CompressBytes(buffer);
                }
            }
            await Task.CompletedTask;
            return new FileContentResult(buffer, Application_Octet_Stream_ContentType);
        }

        /// <summary>
        /// 下载文件，从指定节点
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名）</param>
        /// <param name="lPosition">读取文件的位置（文件从0开始的偏移量）</param>
        /// <param name="intReadSize">读取文件大小（一次读取的文件大小）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpPost]
        public async Task<FileContentResult> DownloadFilePosition(string strFullFileName, long lPosition, int intReadSize, bool isEncryptDecrypt)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            byte[] buffer = instance.DownloadFilePosition(strFullFileName, lPosition, intReadSize, isEncryptDecrypt);
            if (buffer == null)
            {
                buffer = Array.Empty<byte>();
            }
            else
            {
                if (buffer.Length > 0)
                {
                    buffer = TSZ.Common.Shared.CompressHelper.CompressBytes(buffer);
                }
            }
            await Task.CompletedTask;
            return new FileContentResult(buffer, Application_Octet_Stream_ContentType);
        }

        /// <summary>
        /// 覆盖文件
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名）</param>
        /// <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult<string>> OverrideFile(string strFullFileName, int uploadMode, bool isEncryptDecrypt)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            byte[] buffer = Array.Empty<byte>();
            ServiceResult<string> serviceResult = null;

            #region 兼容 form-data提交的文件
            if (Request.HasFormContentType)
            {
                try
                {
                    //理论上只允许一次上传一个文件，如果有多个文件了，则批量添加到同一个服务器文件的末尾，视为一个文件来处理
                    using (MemoryStream ms = new MemoryStream())
                    {
                        foreach (var postFile in Request.Form.Files)
                        {
                            buffer = await postFile.GetAllBytesAsync();
                            buffer = TSZ.Common.Shared.CompressHelper.DeCompressBytes(buffer);
                            ms.Write(buffer, 0, buffer.Length);
                        }
                        ms.Flush();
                        buffer = ms.ToArray();
                    }
                    serviceResult = instance.OverrideFile(strFullFileName, buffer, uploadMode, isEncryptDecrypt);
                    buffer = null;
                    return serviceResult;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex.ToString());
                }
            }
            #endregion

            long intContentLength = 0;
            if (Request.ContentLength.HasValue)
            {
                intContentLength = Request.ContentLength.Value;
            }
            if (intContentLength > 0)
            {
                using (MemoryStream ms = StreamPool.RecyclableMemoryStreamManagerInstance.GetStream(null, intContentLength))
                {
                    await Request.Body.CopyToAsync(ms);
                    buffer = TSZ.Common.Shared.CompressHelper.DeCompressBytes(ms.ToArray());
                }
            }
            serviceResult = instance.OverrideFile(strFullFileName, buffer, uploadMode, isEncryptDecrypt);
            buffer = null;
            return serviceResult;
        }

        #endregion

        #region 文件分块上传

        /// <summary>
        /// 创建签名文件【分块】
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="intChunkSize">分块数量</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpGet]
        public async Task<ServiceResult<string>> CreateSignatureFile(string strFullFileName, short intChunkSize, bool isEncryptDecrypt = true)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            //返回的是全路径文件名
            string result = instance.CreateSignatureFile(strFullFileName, intChunkSize, isEncryptDecrypt);
            if (IsLinuxPlatform)
            {
                if (result != null && result.Length > 0)
                {
                    result = GetAvalidatedFileName(result);
                }
            }
            await Task.CompletedTask;
            if (PubMethod.IsNullOrEmpty(result))
            {
                return ServiceResult.Failed<string>("计算参考文件的哈希签名失败。", result);
            }
            //去除根目录 "/tszappdatas"
            if (result.ToLower().StartsWith(_storageRootPath.ToLower()))
            {
                result = result.Substring(_storageRootPath.Length + 1);
            }
            return ServiceResult.Success(result);
        }

        /// <summary>
        /// 创建Delta文件【分块】
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strSignatureFile">Delta完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpGet]
        public async Task<ServiceResult<string>> CreateDeltaFile(string strFullFileName, string strSignatureFile, bool isEncryptDecrypt = false)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            strSignatureFile = GetAvalidatedFileName(strSignatureFile);

            string result = instance.CreateDeltaFile(strFullFileName, strSignatureFile, isEncryptDecrypt);
            if (IsLinuxPlatform)
            {
                if (result != null && result.Length > 0)
                {
                    result = GetAvalidatedFileName(result);
                }
            }
            await Task.CompletedTask;
            if (PubMethod.IsNullOrEmpty(result))
            {
                return ServiceResult.Failed<string>("对目标文件计算并创建差异文件。", result);
            }
            //去除根目录 "/tszappdatas"
            if (result.ToLower().StartsWith(_storageRootPath.ToLower()))
            {
                result = result.Substring(_storageRootPath.Length + 1);
            }
            return ServiceResult.Success(result);
        }

        /// <summary>
        /// 从Delta文件创建新文件
        /// </summary>
        /// <param name="strReferenceServerFullFileName">参考的完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strDeltaFile">Delta完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpGet]
        public async Task<ServiceResult<string>> ApplyDeltaFileToCreateNewFile(string strReferenceServerFullFileName, string strFullFileName, string strDeltaFile, bool isEncryptDecrypt = true)
        {
            strReferenceServerFullFileName = GetAvalidatedFileName(strReferenceServerFullFileName);
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            strDeltaFile = GetAvalidatedFileName(strDeltaFile);

            string result = instance.ApplyDeltaFileToCreateNewFile(strReferenceServerFullFileName, strFullFileName, strDeltaFile, isEncryptDecrypt);
            if (IsLinuxPlatform)
            {
                if (result != null && result.Length > 0)
                {
                    result = GetAvalidatedFileName(result);
                }
            }
            await Task.CompletedTask;
            if (PubMethod.IsNullOrEmpty(result))
            {
                return ServiceResult.Failed<string>("合并出最终的目标文件。", result);
            }
            //去除根目录 "/tszappdatas"
            if (result.ToLower().StartsWith(_storageRootPath.ToLower()))
            {
                result = result.Substring(_storageRootPath.Length + 1);
            }
            return ServiceResult.Success(result);
        }
        #endregion

        #region  一次性上传下载API

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否是加密文件</param>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpGet]
        public async Task<FileStreamResult> DownloadFileWhole(string strFullFileName, bool isEncryptDecrypt)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            try
            {
                if (System.IO.File.Exists(strFullFileName))
                {
                    CryptoFileStream fs = new CryptoFileStream(strFullFileName, FileMode.Open, FileAccess.Read, FileShare.Read);
                    fs.IsEncryptDecrypt = isEncryptDecrypt;
                    return new FileStreamResult(fs, "application/octet-stream");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex.ToString());
            }
            await Task.CompletedTask;
            return new FileStreamResult(StreamPool.RecyclableMemoryStreamManagerInstance.GetStream(), "application/octet-stream");
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="intReadCount">文件读取的次数（第几次，从0开始计数）</param>
        /// <param name="isEncryptDecrypt">是否是加密文件</param>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpGet]
        public async Task<FileStreamResult> DownloadFileWholePart(string strFullFileName, int intReadCount, bool isEncryptDecrypt)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            try
            {
                byte[] buffer = instance.DownloadFile(strFullFileName, intReadCount, isEncryptDecrypt);
                if (buffer == null)
                {
                    buffer = Array.Empty<byte>();
                }
                return new FileStreamResult(StreamPool.RecyclableMemoryStreamManagerInstance.GetStream(buffer), "application/octet-stream");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex.ToString());
            }
            await Task.CompletedTask;
            return new FileStreamResult(new MemoryStream(), "application/octet-stream");
        }

        /// <summary>
        /// 下载文件(发布目录下的模型等文件)（通用），在特定目录下（发布目录）去下载
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否是加密文件</param>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpGet]
        public async Task<FileContentResult> DownloadModelFileWhole(string strFullFileName, bool isEncryptDecrypt)
        {
            strFullFileName = GetAvalidatedModelFolderName(strFullFileName);
            FileInfo fileInfo = new FileInfo(strFullFileName);
            byte[] buffer = instance.DownloadFile(strFullFileName, (int)fileInfo.Length, isEncryptDecrypt);
            if (buffer == null)
            {
                buffer = Array.Empty<byte>();
            }
            else
            {
                if (buffer.Length > 0)
                {
                    buffer = TSZ.Common.Shared.CompressHelper.CompressBytes(buffer);
                }
            }
            await Task.CompletedTask;
            return new FileContentResult(buffer, Application_Octet_Stream_ContentType);
        }

        /// <summary>
        /// 上传文件（整个一起上传）
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否加密文件</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        [DisableRequestSizeLimit]
        public async Task<ServiceResult<string>> UploadFileWhole(string strFullFileName, bool isEncryptDecrypt)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            ServiceResult<string> serviceResult = null;

            #region 兼容 form-data提交的文件
            if (Request.HasFormContentType)
            {
                try
                {
                    //理论上只允许一次上传一个文件，如果有多个文件了，则批量添加到同一个服务器文件的末尾，视为一个文件来处理
                    int intIndex = 0;
                    foreach (var postFile in Request.Form.Files)
                    {
                        using (Stream postStream = postFile.OpenReadStream())
                        {
                            if (intIndex == 0)
                            {
                                PubMethod.DeleteFile(strFullFileName);
                                //uploadMode == 1 时为第一次上传，检查目标文件是否存在，如果目标文件已经存在，则会在服务器上自动创建新名称的文件，并返回新文件名（短名称）
                                serviceResult = instance.UploadFile(strFullFileName, postStream, 1, isEncryptDecrypt);
                            }
                            else
                            {
                                serviceResult = instance.UploadFile(strFullFileName, postStream, 0, isEncryptDecrypt);
                            }
                            intIndex++;
                        }
                    }
                    return serviceResult;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex.ToString());
                }
            }
            #endregion

            byte[] buffer = Array.Empty<byte>();
            if (Request.ContentLength.HasValue)
            {
                buffer = new byte[Request.ContentLength.Value];
            }
            using (MemoryStream ms = StreamPool.RecyclableMemoryStreamManagerInstance.GetStream(buffer))
            {
                await Request.Body.CopyToAsync(ms);
                buffer = ms.ToArray();
            }
            PubMethod.DeleteFile(strFullFileName);
            serviceResult = instance.UploadFile(strFullFileName, buffer, 1, isEncryptDecrypt);
            return serviceResult;
        }

        /// <summary>
        /// 上传文件，切割上传
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
        /// <param name="isEncryptDecrypt">是否加密文件</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult<string>> UploadFileWholePart(string strFullFileName, int uploadMode, bool isEncryptDecrypt)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            ServiceResult<string> serviceResult = null;

            #region 兼容 form-data提交的文件
            if (Request.HasFormContentType)
            {
                try
                {
                    //理论上只允许一次上传一个文件，如果有多个文件了，则批量添加到同一个服务器文件的末尾，视为一个文件来处理
                    int intIndex = 0;
                    foreach (var postFile in Request.Form.Files)
                    {
                        using (Stream postStream = postFile.OpenReadStream())
                        {
                            if (intIndex == 0)
                            {
                                if (uploadMode == 1)
                                {
                                    PubMethod.DeleteFile(strFullFileName);
                                }
                                //uploadMode == 1 时为第一次上传，检查目标文件是否存在，如果目标文件已经存在，则会在服务器上自动创建新名称的文件，并返回新文件名（短名称）
                                serviceResult = instance.UploadFile(strFullFileName, postStream, uploadMode, isEncryptDecrypt);
                            }
                            else
                            {
                                serviceResult = instance.UploadFile(strFullFileName, postStream, 0, isEncryptDecrypt);
                            }
                            intIndex++;
                        }
                    }
                    return serviceResult;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex.ToString());
                }
            }
            #endregion

            byte[] buffer = Array.Empty<byte>();
            if (Request.ContentLength.HasValue)
            {
                buffer = new byte[Request.ContentLength.Value];
            }
            using (MemoryStream ms = StreamPool.RecyclableMemoryStreamManagerInstance.GetStream(buffer))
            {
                await Request.Body.CopyToAsync(ms);
                buffer = ms.ToArray();
            }
            if (uploadMode == 1)
            {
                PubMethod.DeleteFile(strFullFileName);
            }
            serviceResult = instance.UploadFile(strFullFileName, buffer, uploadMode, isEncryptDecrypt);
            return serviceResult;
        }

        #endregion

        #region 文件压测接口，不实际读写文件，避免磁盘爆炸和大量垃圾
        /// <summary>
        /// 上传文件（整个一起上传）,文件压测接口，不实际读写文件，避免磁盘爆炸和大量垃圾
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否加密文件</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        [DisableRequestSizeLimit]
        public async Task<ServiceResult<string>> UploadFileWholeEx(string strFullFileName, bool isEncryptDecrypt)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            ServiceResult<string> serviceResult = null;

            #region 兼容 form-data提交的文件
            if (Request.HasFormContentType)
            {
                try
                {
                    //理论上只允许一次上传一个文件，如果有多个文件了，则批量添加到同一个服务器文件的末尾，视为一个文件来处理
                    int intIndex = 0;
                    foreach (var postFile in Request.Form.Files)
                    {
                        using (Stream postStream = postFile.OpenReadStream())
                        {
                            if (intIndex == 0)
                            {
                                //PubMethod.DeleteFile(strFullFileName);
                                //uploadMode == 1 时为第一次上传，检查目标文件是否存在，如果目标文件已经存在，则会在服务器上自动创建新名称的文件，并返回新文件名（短名称）
                                serviceResult = instance.UploadFileEx(strFullFileName, postStream, 1, isEncryptDecrypt);
                            }
                            else
                            {
                                serviceResult = instance.UploadFileEx(strFullFileName, postStream, 0, isEncryptDecrypt);
                            }
                            intIndex++;
                        }
                    }
                    return serviceResult;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex.ToString());
                }
            }
            #endregion

            byte[] buffer = Array.Empty<byte>();
            if (Request.ContentLength.HasValue)
            {
                buffer = new byte[Request.ContentLength.Value];
            }
            using (MemoryStream ms = StreamPool.RecyclableMemoryStreamManagerInstance.GetStream(buffer))
            {
                await Request.Body.CopyToAsync(ms);
                buffer = ms.ToArray();
            }
            //PubMethod.DeleteFile(strFullFileName);
            serviceResult = instance.UploadFileEx(strFullFileName, buffer, 1, isEncryptDecrypt);
            return serviceResult;
        }

        /// <summary>
        /// 上传文件，切割上传,文件压测接口，不实际读写文件，避免磁盘爆炸和大量垃圾
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
        /// <param name="isEncryptDecrypt">是否加密文件</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult<string>> UploadFileWholePartEx(string strFullFileName, int uploadMode, bool isEncryptDecrypt)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            ServiceResult<string> serviceResult = null;

            #region 兼容 form-data提交的文件
            if (Request.HasFormContentType)
            {
                try
                {
                    //理论上只允许一次上传一个文件，如果有多个文件了，则批量添加到同一个服务器文件的末尾，视为一个文件来处理
                    int intIndex = 0;
                    foreach (var postFile in Request.Form.Files)
                    {
                        using (Stream postStream = postFile.OpenReadStream())
                        {
                            if (intIndex == 0)
                            {
                                if (uploadMode == 1)
                                {
                                    //PubMethod.DeleteFile(strFullFileName);
                                }
                                //uploadMode == 1 时为第一次上传，检查目标文件是否存在，如果目标文件已经存在，则会在服务器上自动创建新名称的文件，并返回新文件名（短名称）
                                serviceResult = instance.UploadFileEx(strFullFileName, postStream, uploadMode, isEncryptDecrypt);
                            }
                            else
                            {
                                serviceResult = instance.UploadFileEx(strFullFileName, postStream, 0, isEncryptDecrypt);
                            }
                            intIndex++;
                        }
                    }
                    return serviceResult;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex.ToString());
                }
            }
            #endregion

            byte[] buffer = Array.Empty<byte>();
            if (Request.ContentLength.HasValue)
            {
                buffer = new byte[Request.ContentLength.Value];
            }
            using (MemoryStream ms = StreamPool.RecyclableMemoryStreamManagerInstance.GetStream(buffer))
            {
                await Request.Body.CopyToAsync(ms);
                buffer = ms.ToArray();
            }
            if (uploadMode == 1)
            {
               //PubMethod.DeleteFile(strFullFileName);
            }
            serviceResult = instance.UploadFileEx(strFullFileName, buffer, uploadMode, isEncryptDecrypt);
            return serviceResult;
        }
        #endregion

        #region  vue-simple-uploader 服务 https://www.cnblogs.com/xiahj/p/vue-simple-uploader.html
        /// <summary>
        /// GET请求，获取已经上传的分片信息（分片上传前返回已经上传的分片给前端）【vue前端专用】
        /// </summary>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpGet]
        public async Task<ServiceResult<FileResultDto>> UploadFileChunks()
        {
            await Task.CompletedTask;
            FileResultDto fileResult = new FileResultDto();
            string identifier = _httpContextAccessor.HttpContext.Request.Query["identifier"];
            string oneceGuid = _httpContextAccessor.HttpContext.Request.Query["oneceGuid"];
            var temporary = PubMethod.Combine(_storageRootPath, oneceGuid);//最终的文件名
            if (!Directory.Exists(temporary))
            {
                Directory.CreateDirectory(temporary);
            }
            int totalChunks = Convert.ToInt32(_httpContextAccessor.HttpContext.Request.Query["totalChunks"]);
            string temporaryPath = PubMethod.Combine(temporary, identifier);//临时保存分块的目录
            if (!Directory.Exists(temporaryPath))
            {
                return null;
            }
            List<int> listChunks = new List<int>();
            for (int i = 1; i <= totalChunks; i++)
            {
                string filePath = PubMethod.Combine(temporaryPath, "t_" + i);
                if (System.IO.File.Exists(filePath))
                {
                    listChunks.Add(i);
                }
            }
            fileResult.Uploaded = listChunks;
            return ServiceResult.Success(fileResult);
        }

        /// <summary>
        /// 文件分片上传，后续需要调用FileMergeChunks合并分片（除非totalChunks=1则直接处理为直接上传，不需要FileMergeChunks合并）【vue前端专用】
        /// 临时分片文件存储目录为：根目录+oneceGuid+identifier
        /// </summary>
        /// <param name="chunkNumber">分片编号</param>
        /// <param name="chunkSize">分片大小</param>
        /// <param name="totalSize">文件总大小</param>
        /// <param name="identifier">文件标识符，临时分片文件存储目录为：根目录+oneceGuid+identifier</param>      
        /// <param name="totalChunks">总分片数，大于1的总分片，后续需要调用FileMergeChunks合并分片，如果等于1，则变成直接上传</param>
        /// <param name="bucketName">模拟对象存储的bucketName，必填，可以为租户Id，可以是任意字符串，如tszobs、Guid等</param>
        /// <param name="objectName">模拟对象存储的objectName，可以是文件对象名称而且可以带子目录，如exampledir/exampleobject.txt</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        [DisableRequestSizeLimit]
        public async Task<ServiceResult<FileResultDto>> UploadFileChunks(
            [FromForm] int chunkNumber, [FromForm] long chunkSize, [FromForm] long totalSize,
            [FromForm] string identifier, [FromForm] int totalChunks, [FromForm] string bucketName, [FromForm] string objectName)
        {
            objectName = GetAvalidatedFileNameWithOutRoot(objectName);
            string subPath = PubMethod.GetDirectoryName(objectName);
            FileResultDto fileResult = new FileResultDto();
            var httpForm = await Request.ReadFormAsync();
            IFormFile formFile = httpForm.Files[0];
            if (formFile == null || formFile.Length <= 0)
            {
                return ServiceResult.Failed<FileResultDto>("上传的文件体为空");
            }

            if (totalChunks <= 1)
            {
                //不分块直接上传
                string fileExtension = Path.GetExtension(objectName);//获取文件后缀
                string alias = PubMethod.CreateGuidKey() + fileExtension;

                var fullSavePath = PubMethod.Combine(_storageRootPath, bucketName, subPath);//最终的文件存储目录
                if (!Directory.Exists(fullSavePath))
                {
                    Directory.CreateDirectory(fullSavePath);
                }
                var fileRelativePath = PubMethod.Combine(bucketName, subPath, alias); //文件相对完整路径，无根目录
                var fullFile = PubMethod.Combine(fullSavePath, alias); //最终的完整全路径文件名
                using (FileStream stream = new FileStream(fullFile, FileMode.Create))
                {
                    await formFile.CopyToAsync(stream);
                }
                fileResult.NeedMerge = false;
                fileResult.FileName = Path.GetFileName(objectName);
                fileResult.Extension = fileExtension;
                fileResult.TotalSize = totalSize.ToString();
                fileResult.MD5 = identifier;
                fileResult.FilePath = fileRelativePath; //文件相对完整路径，无根目录
                fileResult.Alias = alias;
                fileResult.BucketName = bucketName;
                return ServiceResult.Success(fileResult);
            }
            else
            {
                var fullSavePath = PubMethod.Combine(_storageRootPath, bucketName, subPath);//最终的文件存储目录
                if (!Directory.Exists(fullSavePath))
                {
                    Directory.CreateDirectory(fullSavePath);
                }
                string temporaryPath = PubMethod.Combine(fullSavePath, identifier);//临时保存分块的存储目录
                try
                {
                    if (!Directory.Exists(temporaryPath))
                    {
                        Directory.CreateDirectory(temporaryPath);
                    }
                    string fullFile = PubMethod.Combine(temporaryPath, "t_" + chunkNumber.ToString()); //最终的完整全路径文件名
                    using (FileStream stream = new FileStream(fullFile, FileMode.CreateNew))
                    {
                        await formFile.CopyToAsync(stream); //保存文件到磁盘
                    }
                    fileResult.NeedMerge = true;
                    return ServiceResult.Success(fileResult);
                }
                catch (Exception ex)
                {
                    PubMethod.DeleteFolder(temporaryPath);
                    // Directory.Delete(temporaryPath); //删除临时文件夹
                    return ServiceResult.Failed<FileResultDto>(ex);
                }
            }
        }

        /// <summary>
        /// 合并分片文件，完成上传（UploadFileChunks的后续方法）【vue前端专用】
        /// 临时分片文件存储目录为：根目录+oneceGuid+identifier
        /// </summary>
        /// <param name="identifier">文件标识符，临时分片文件存储目录为：根目录+oneceGuid+identifier</param>       
        /// <param name="totalChunks">总分片数量</param>
        /// <param name="bucketName">模拟对象存储的bucketName，必填，可以为租户Id，可以是任意字符串，如tszobs、Guid等</param>
        /// <param name="objectName">模拟对象存储的objectName，可以是文件对象名称而且可以带子目录，如exampledir/exampleobject.txt</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult<FileResultDto>> FileMergeChunks(string identifier, int totalChunks, string bucketName, string objectName)
        {
            objectName = GetAvalidatedFileNameWithOutRoot(objectName);
            string subPath = PubMethod.GetDirectoryName(objectName);
            bool allFileIsCheckDone = true;
            var fullSavePath = PubMethod.Combine(_storageRootPath, bucketName, subPath);//最终的文件存储目录
            if (!Directory.Exists(fullSavePath))
            {
                Directory.CreateDirectory(fullSavePath);
            }
            string temporaryPath = PubMethod.Combine(fullSavePath, identifier);//临时保存分块的存储目录
            if (totalChunks <= 1)
            {
                return ServiceResult.Failed<FileResultDto>("未分片");
            }
            for (int i = 1; i <= totalChunks; i++)
            {
                string filePath = PubMethod.Combine(temporaryPath, "t_" + i);
                if (!System.IO.File.Exists(filePath))
                {
                    allFileIsCheckDone = false;
                    break;
                }
            }
            if (!allFileIsCheckDone)
            {
                allFileIsCheckDone = true;
                for (int i = 0; i < totalChunks; i++)
                {
                    string filePath = PubMethod.Combine(temporaryPath, "t_" + i);
                    if (!System.IO.File.Exists(filePath))
                    {
                        allFileIsCheckDone = false;
                        break;
                    }
                }
            }
            if (!allFileIsCheckDone)
            {
                //分片信息有误
                return ServiceResult.Failed<FileResultDto>("分片信息有误，请检查");
            }
            else
            {
                //合并分片
                return await FileMergeChunksAll(identifier, bucketName, objectName);
            }
        }

        /// <summary>
        /// 合并分片方法，忽略totalChunks参数，进行全部合并（合并分片目录下的全部分片，而忽略totalChunks的限制），对应于FileMergeChunks的扩展方法【vue前端专用】
        /// </summary>
        /// <param name="identifier">文件MD5值</param>
        /// <param name="bucketName">模拟对象存储的bucketName，必填，可以为租户Id，可以是任意字符串，如tszobs、Guid等</param>
        /// <param name="objectName">模拟对象存储的objectName，可以是文件对象名称而且可以带子目录，如exampledir/exampleobject.txt</param>
        /// <returns></returns>
        private async Task<ServiceResult<FileResultDto>> FileMergeChunksAll(string identifier, string bucketName, string objectName)
        {
            try
            {
                string subPath = PubMethod.GetDirectoryName(objectName);
                string fileExtension = Path.GetExtension(objectName);//获取文件后缀
                string alias = PubMethod.CreateGuidKey() + fileExtension;

                var fullSavePath = PubMethod.Combine(_storageRootPath, bucketName, subPath);//最终的文件存储目录             
                if (!Directory.Exists(fullSavePath))
                {
                    Directory.CreateDirectory(fullSavePath);
                }
                string temporaryPath = PubMethod.Combine(fullSavePath, identifier);//临时保存分块的目录  
                var files = Directory.GetFiles(temporaryPath);//获得下面的所有文件
                var fullFile = PubMethod.Combine(fullSavePath, alias);//最终的文件名
                using (FileStream fs = new FileStream(fullFile, FileMode.Create, FileAccess.Write, FileShare.ReadWrite))
                {
                    foreach (string part in files.OrderBy(x => x.Length).ThenBy(x => x))//排一下序，保证从0-N Write
                    {
                        byte[] bytes = PubMethod.ReadAllBytes(part);
                        await fs.WriteAsync(bytes, 0, bytes.Length);
                        bytes = null;
                        PubMethod.DeleteFile(part); //删除分块
                    }
                }
                var fileRelativePath = PubMethod.Combine(bucketName, subPath, alias); //文件相对完整路径，无根目录
                PubMethod.DeleteFolder(temporaryPath);//删除文件夹
                                                      //判断MD5               
                string strFileMD5 = HashHelper.ComputeMD5(fullFile);
                FileResultDto fileResult = new FileResultDto();
                fileResult.Extension = fileExtension;
                fileResult.FileName = Path.GetFileName(objectName);
                fileResult.MD5 = identifier;
                fileResult.Alias = alias;
                fileResult.FilePath = fileRelativePath; //文件相对完整路径，无根目录
                fileResult.BucketName = bucketName;
                if (strFileMD5 == identifier)
                {
                    return ServiceResult.Success(fileResult);
                }
                else
                {
                    return ServiceResult.Failed<FileResultDto>("文件MD5值校验失败");
                }
            }
            catch (Exception ex)
            {
                return ServiceResult.Failed<FileResultDto>(ex);
            }
        }
        #endregion

        #region 其它API

        /// <summary>
        /// 测试API，检查文件服务是否正常
        /// </summary>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpGet]
        public async Task<ServiceResult<bool>> CanOpened()
        {
            await Task.CompletedTask;
            ServiceResult<bool> serviceResult = ServiceResult.Success(true);
            serviceResult.Tag = MyInstanceId;
            return serviceResult;
        }

        /// <summary>
        /// 获取服务器时间，返回标准时间
        /// </summary>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpGet]
        public async Task<ServiceResult<DateTime>> GetServerDateTime()
        {
            await Task.CompletedTask;
            ServiceResult<DateTime> serviceResult = ServiceResult.Success(DateTime.Now);
            serviceResult.Tag = MyInstanceId;
            return serviceResult;
        }

        /// <summary>
        /// 判断文件是否存在
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpGet]
        public async Task<ServiceResult<bool>> ExistsFile(string strFullFileName)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            await Task.CompletedTask;

            //string message = $"{MyInstanceId} {strFullFileName} = {File.Exists(strFullFileName)}";
            //Console.WriteLine(message);
            //Logger.LogError(message);
            //return ServiceResult<bool>.Success(instance.ExistsFile(strFullFileName), message);

            return ServiceResult<bool>.Success(instance.ExistsFile(strFullFileName));
        }

        /// <summary>
        /// 获取文件大小
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpGet]
        public async Task<ServiceResult<long>> GetFileLength(string strFullFileName)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            await Task.CompletedTask;
            return ServiceResult<bool>.Success(instance.GetFileLength(strFullFileName));
        }

        /// <summary>
        /// 获取文件信息
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpGet]
        public async Task<ServiceResult<List<string>>> GetFileInfo(string strFullFileName, bool isEncryptDecrypt)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            await Task.CompletedTask;
            var fileInfos = instance.GetFileInfo(strFullFileName, isEncryptDecrypt);
            return ServiceResult<List<string>>.Success(fileInfos);
        }

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpGet]
        public async Task<ServiceResult<string>> DeleteFile(string strFullFileName)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            await Task.CompletedTask;

            //Console.WriteLine($"{MyInstanceId} {strFullFileName} DeleteFile。");
            //Logger.LogError($"{MyInstanceId} {strFullFileName} DeleteFile。");

            return instance.DeleteFile(strFullFileName);
        }

        /// <summary>
        /// 删除文件夹
        /// </summary>
        /// <param name="strDirectoryName"></param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpGet]
        public async Task<ServiceResult<bool>> DeleteDirectory(string strDirectoryName)
        {
            strDirectoryName = GetAvalidatedFileName(strDirectoryName);
            await Task.CompletedTask;
            return instance.DeleteDirectory(strDirectoryName);
        }

        /// <summary>
        /// 复制文件
        /// </summary>
        /// <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpGet]
        public async Task<ServiceResult<string>> CopyFile(string strSrcFullFileName, string strTargetFullFileName)
        {
            strSrcFullFileName = GetAvalidatedFileName(strSrcFullFileName);
            strTargetFullFileName = GetAvalidatedFileName(strTargetFullFileName);
            await Task.CompletedTask;
            return instance.CopyFile(strSrcFullFileName, strTargetFullFileName);
        }

        /// <summary>
        /// 复制文件
        /// </summary>
        /// <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpGet]
        public async Task<ServiceResult<string>> CopyFileOverwrite(string strSrcFullFileName, string strTargetFullFileName)
        {
            strSrcFullFileName = GetAvalidatedFileName(strSrcFullFileName);
            strTargetFullFileName = GetAvalidatedFileName(strTargetFullFileName);
            await Task.CompletedTask;
            return instance.CopyFileOverwrite(strSrcFullFileName, strTargetFullFileName);
        }

        /// <summary>
        /// 解密文件
        /// </summary>
        /// <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpGet]
        public async Task<ServiceResult<string>> CopyFileEncryptToDecrypt(string strSrcFullFileName, string strTargetFullFileName)
        {
            strSrcFullFileName = GetAvalidatedFileName(strSrcFullFileName);
            strTargetFullFileName = GetAvalidatedFileName(strTargetFullFileName);
            await Task.CompletedTask;
            return instance.CopyFileEncryptToDecrypt(strSrcFullFileName, strTargetFullFileName);
        }

        /// <summary>
        /// 加密文件
        /// </summary>
        /// <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpGet]
        public async Task<ServiceResult<string>> CopyFileDecryptToEncrypt(string strSrcFullFileName, string strTargetFullFileName)
        {
            strSrcFullFileName = GetAvalidatedFileName(strSrcFullFileName);
            strTargetFullFileName = GetAvalidatedFileName(strTargetFullFileName);
            await Task.CompletedTask;
            return instance.CopyFileDecryptToEncrypt(strSrcFullFileName, strTargetFullFileName);
        }

        /// <summary>
        /// 获取文件MD5值
        /// </summary>
        /// <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="lFileLength">文件长度</param>
        /// <param name="isEncryptDecrypt">是否加密</param>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpGet]
        public async Task<ServiceResult<string>> ComputeFileMD5(string strFullFileName, long lFileLength, bool isEncryptDecrypt)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            //if (!File.Exists(strFullFileName))
            //{
            //    Console.WriteLine($"{MyInstanceId} {strFullFileName} 文件不存在。");
            //    Logger.LogError($"{MyInstanceId} {strFullFileName} 文件不存在。");
            //}
            //else
            //{
            //    Console.WriteLine($"{MyInstanceId} {strFullFileName} 文件存在。");
            //    Logger.LogError($"{MyInstanceId} {strFullFileName} 文件存在。");
            //}
            await Task.CompletedTask;
            var fileMd5 = instance.ComputeFileMD5(strFullFileName, lFileLength, isEncryptDecrypt);
            if (PubMethod.IsNullOrEmpty(fileMd5))
            {
                return ServiceResult.Failed<string>("计算文件MD5值时文件不存在或者计算错误。");
            }
            return ServiceResult.Success(fileMd5);
        }

        /// <summary>
        /// 重命名文件
        /// </summary>
        /// <param name="strFullFileName">文件原始路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strNewFileName">文件新路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpGet]
        public async Task<ServiceResult<bool>> ReNameFile(string strFullFileName, string strNewFileName)
        {
            strFullFileName = GetAvalidatedFileName(strFullFileName);
            strNewFileName = GetAvalidatedFileName(strNewFileName);
            await Task.CompletedTask;
            bool isOK = instance.ReNameFile(strFullFileName, strNewFileName);
            if (isOK)
            {
                return ServiceResult.Success(true);
            }
            return ServiceResult.Failed<bool>("重命名失败。");
        }

        /// <summary>
        /// 移动文件
        /// </summary>
        /// <param name="strSrcFullFileName">文件原始路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetFullFileName">文件目的路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpGet]
        public async Task<ServiceResult<bool>> MoveFile(string strSrcFullFileName, string strTargetFullFileName)
        {
            strSrcFullFileName = GetAvalidatedFileName(strSrcFullFileName);
            strTargetFullFileName = GetAvalidatedFileName(strTargetFullFileName);
            await Task.CompletedTask;
            bool isOK = instance.MoveFile(strSrcFullFileName, strTargetFullFileName);
            if (isOK)
            {
                return ServiceResult.Success(true);
            }
            return ServiceResult.Failed<bool>("移动文件失败。");
        }

        #endregion

        #region  迁移文件添加的接口
        /// <summary>
        /// 获取文件夹文件
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpGet]
        public async Task<ServiceResult<List<string>>> GetDirectoyChild(string path)
        {
            path = GetAvalidatedFileName(path);
            await Task.CompletedTask;
            var datas = instance.GetDirectories(path);
            if (datas == null)
            {
                return ServiceResult.Failed<List<string>>("目录不存在");
            }
            return ServiceResult.Success(datas.ToList());
        }

        /// <summary>
        /// 获取文件下面目录
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpGet]
        public async Task<ServiceResult<List<string>>> GetDirectoryFiles(string path)
        {
            path = GetAvalidatedFileName(path);
            await Task.CompletedTask;
            var files = instance.GetFiles(path);
            if (files == null)
            {
                return ServiceResult.Failed<List<string>>("目录不存在");
            }
            return ServiceResult.Success(files.ToList());
        }

        /// <summary>
        /// 移动文件夹下面的文件
        /// </summary>
        /// <param name="sourcePath"></param>
        /// <param name="targetPath"></param>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpGet]
        public async Task<ServiceResult<bool>> MoveDirectoryFile(string sourcePath, string targetPath)
        {
            sourcePath = GetAvalidatedFileName(sourcePath);
            await Task.CompletedTask;
            var files = instance.GetFiles(sourcePath);
            if (files == null)
            {
                return ServiceResult.Failed<bool>("目录不存在");
            }
            foreach (var file in files)
            {
                string sourceFullName = GetAvalidatedFileName(file);
                string name = Path.GetFileName(sourceFullName);
                string targetFullName = GetAvalidatedFileName(PubMethod.Combine(targetPath, name));
                instance.MoveFile(sourceFullName, targetFullName);
            }
            return ServiceResult.Success(true);
        }
        #endregion

        /// <summary>
        /// 解压指定文件到指定目录
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatas，则API会自动加上，只能在 /tszappdatas 根目录下解压到指定的子目录</param>
        /// <param name="isEncryptDecrypt">原始文件是否是加密的</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult<string>> UnzipFile(string strFullFileName, string strTargetDirectory, bool isEncryptDecrypt)
        {
            try
            {
                strFullFileName = GetAvalidatedFileName(strFullFileName);
                strTargetDirectory = GetAvalidatedFileName(strTargetDirectory);

                await Task.Factory.StartNew(() =>
                {
                    if (isEncryptDecrypt)
                    {
                        CompressHelper.UnzipFileWithEncryptDecrypt(strFullFileName, strTargetDirectory);
                    }
                    else
                    {
                        CompressHelper.UnzipFile(strFullFileName, strTargetDirectory);
                    }
                });
                string result = strTargetDirectory;

                //去除根目录 "/tszappdatas"
                if (result.ToLower().StartsWith(_storageRootPath.ToLower()))
                {
                    result = result.Substring(_storageRootPath.Length + 1);
                }
                return ServiceResult.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult.Failed<string>("解压压缩包错误。" + ex);
            }
        }

        /// <summary>
        /// 生成压缩包的方法(TSV5专用)-改为通用方法
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [ActionPrefix("read")]
        [HttpPost]
        public async Task<ServiceResult<string>> GetZipFolderArchive(ZipFolderArchivesDto dto)
        {
            try
            {
                if (dto == null ||
                    PubMethod.IsNullOrEmpty(dto.FolderName) ||
                    PubMethod.IsNullOrEmpty(dto.ZipTargetFolder))
                {
                    return ServiceResult.Failed<string>("生成压缩包所提供的参数错误");
                }
                string targetPath = GetAvalidatedFileName(PubMethod.Combine(_storageRootPath, dto.ZipTargetFolder));
                string zipFileName = PubMethod.CreateGuidKey() + ".zip";
                string desZipFullFile = GetAvalidatedFileName(PubMethod.Combine(targetPath, zipFileName));
                if (!Directory.Exists(targetPath))
                {
                    Directory.CreateDirectory(targetPath);
                }

                List<ZipEntryInfo> fileInfos = new List<ZipEntryInfo>();
                CreateDictionaryFileInfos(dto, targetPath, dto.FolderName, ref fileInfos);
                //压缩文件夹
                CompressHelper.ToZipFile(fileInfos, desZipFullFile);
                if (File.Exists(desZipFullFile))
                {
                    string retpath = PubMethod.Combine(targetPath, zipFileName); //返回给前端的路径
                    return ServiceResult.Success(retpath);
                }
                await Task.CompletedTask;
                return ServiceResult.Failed<string>("生成压缩包错误");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failed<string>("生成压缩包错误。" + ex);
            }
        }

        /// <summary>
        /// 创建目录文件实体对象的文件信息对象集合（ZipEntryInfo）
        /// </summary>
        /// <param name="zipFolderArchivesDto"></param>
        /// <param name="parentPath"></param>
        /// <param name="floderName"></param>
        /// <param name="fileInfos"></param>
        private void CreateDictionaryFileInfos(ZipFolderArchivesDto zipFolderArchivesDto, string parentPath, string floderName, ref List<ZipEntryInfo> fileInfos)
        {
            if (zipFolderArchivesDto.FolderName.IsNullOrWhiteSpace())
            {
                return;
            }

            string path = GetAvalidatedFileName(PubMethod.Combine(parentPath, zipFolderArchivesDto.FolderName));
            if (zipFolderArchivesDto.Archives != null && zipFolderArchivesDto.Archives.Count > 0)
            {
                foreach (ZipArchiveDto archive in zipFolderArchivesDto.Archives)
                {
                    string sourceFile = GetAvalidatedFileName(archive.ServerSubFileName);
                    string desName = GetAvalidatedFileName(PubMethod.Combine(path, archive.ArchName));//目的路径

                    ZipEntryInfo zipEntryInfo = new ZipEntryInfo();
                    zipEntryInfo.EntryPath = GetAvalidatedFileNameWithOutRoot(floderName);
                    zipEntryInfo.File = sourceFile;
                    zipEntryInfo.EntryName = Path.GetFileName(desName);
                    fileInfos.Add(zipEntryInfo);
                }
            }
            //处理子目录
            if (zipFolderArchivesDto.SubFolders != null && zipFolderArchivesDto.SubFolders.Count > 0)
            {
                foreach (ZipFolderArchivesDto child in zipFolderArchivesDto.SubFolders)
                {
                    CreateDictionaryFileInfos(child, path, GetAvalidatedFileNameWithOutRoot(PubMethod.Combine(floderName, child.FolderName)), ref fileInfos);
                }
            }
        }


        /// <summary>
        /// 解压指定文件到指定目录并生成2d图纸信息json文件（BimBang专属接口，其它应用不能使用）
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下解压到指定的子目录</param>
        /// <param name="isEncryptDecrypt">原始文件是否是加密的</param>
        /// <param name="jsonFileName">生成json的文件名</param>
        /// <returns>true：有2d图纸，false：无2d图纸</returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult<bool>> UnzipFileWithJsonInfo(string strFullFileName, string strTargetDirectory, bool isEncryptDecrypt, string jsonFileName)
        {
            try
            {
                strFullFileName = GetAvalidatedFileName(strFullFileName);
                strTargetDirectory = GetAvalidatedModelFolderName(strTargetDirectory);
                await Task.Factory.StartNew(() =>
                {
                    if (isEncryptDecrypt)
                    {
                        CompressHelper.UnzipFileWithEncryptDecrypt(strFullFileName, strTargetDirectory);
                    }
                    else
                    {
                        CompressHelper.UnzipFile(strFullFileName, strTargetDirectory);
                    }
                });
                #region 生成2d图纸路径json数据文件
                bool isInclude2d = false;
                DirectoryInfo directoryInfo = new DirectoryInfo(strTargetDirectory);
                List<string> nameList = new List<string>();
                DirectoryInfo[] directoryInfos = directoryInfo.GetDirectories();
                if (directoryInfos.Length > 0)
                {
                    nameList = directoryInfos.Where(p => p.Name.Contains("f2d")).Select(p => p.Name).ToList();
                }
                if (nameList.Count > 0)
                {
                    isInclude2d = true;
                    string fp = PubMethod.Combine(strTargetDirectory, jsonFileName);
                    if (!File.Exists(fp))
                    {
                        File.WriteAllText(fp, JsonConvert.SerializeObject(nameList));
                    }
                }
                #endregion
                return ServiceResult.Success<bool>(isInclude2d);
            }
            catch (Exception ex)
            {
                return ServiceResult.Failed<bool>("解压压缩包错误。" + ex);
            }
        }

        /// <summary>
        /// 删除指定文件并删除模型发布服务的指定目录（通用）
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下解压到指定的子目录</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult<bool>> DeleteFileAndFolder(string strFullFileName, string strTargetDirectory)
        {
            try
            {
                strFullFileName = GetAvalidatedFileName(strFullFileName);
                strTargetDirectory = GetAvalidatedModelFolderName(strTargetDirectory);
                await Task.Factory.StartNew(() =>
                {
                    instance.DeleteFile(strFullFileName);
                    instance.DeleteDirectory(strTargetDirectory);
                });
                return ServiceResult.Success<bool>(true, "删除成功");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failed<bool>("删除失败：" + ex);
            }
        }

        /// <summary>
        /// 解压模型文件到模型web服务（通用）
        /// </summary>
        /// <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下解压到指定的子目录</param>
        /// <param name="isEncryptDecrypt">原始文件是否是加密的</param>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult<bool>> UnzipModelFile(string strFullFileName, string strTargetDirectory, bool isEncryptDecrypt)
        {
            try
            {
                strFullFileName = GetAvalidatedFileName(strFullFileName);
                strTargetDirectory = GetAvalidatedModelFolderName(strTargetDirectory);
                await Task.Factory.StartNew(() =>
                {
                    if (isEncryptDecrypt)
                    {
                        CompressHelper.UnzipFileWithEncryptDecrypt(strFullFileName, strTargetDirectory);
                    }
                    else
                    {
                        CompressHelper.UnzipFile(strFullFileName, strTargetDirectory);
                    }
                });
                return ServiceResult.Success<bool>(true);
            }
            catch (Exception ex)
            {
                return ServiceResult.Failed<bool>("解压压缩包错误。" + ex);
            }
        }

        /// <summary>
        /// 复制模型文件到模型web服务（通用）
        /// </summary>
        /// <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
        /// <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下复制到指定的子目录</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult<string>> CopyModelFile(string strSrcFullFileName, string strTargetDirectory)
        {
            strSrcFullFileName = GetAvalidatedFileName(strSrcFullFileName);
            strTargetDirectory = GetAvalidatedModelFolderName(strTargetDirectory);
            await Task.CompletedTask;
            return instance.CopyFile(strSrcFullFileName, strTargetDirectory);
        }

        #region Ftp文件服务
        /// <summary>
        /// 单个传文件
        /// </summary>
        /// <param name="clientFullName">客户端的全路径</param>
        /// <param name="serverFullName">服务端的全路径</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult> UpLoadFileWholeByFtp(string clientFullName, string serverFullName)
        {
            ServiceResult result = new ServiceResult();
            try
            {
                if (_ftpClient != null)
                {
                    if (!_ftpClient.IsConnected)
                    {
                        _ftpClient.Connect();
                    }
                    clientFullName = GetAvalidatedFileName(clientFullName);
                    if (!this._windowspath)
                    {
                        serverFullName = serverFullName.Replace(@"\", @"/");
                    }
                    var ftpstatus = _ftpClient.UploadFile(clientFullName, serverFullName);
                    if (ftpstatus == FtpStatus.Success)
                    {
                        result.Code = ServiceResultCode.Succeed;
                    }
                    else
                    {
                        result.Code = ServiceResultCode.Failed;
                        result.Message = "ftp上传文件失败";
                    }
                    return result;
                }
                result.Code = ServiceResultCode.Failed;
                result.Message = "ftp客户端对象不能为null";
            }
            catch (Exception ex)
            {
                result.Code = ServiceResultCode.Failed;
                result.Message = ex.ToString();
            }
            await Task.CompletedTask;
            return result;
        }

        /// <summary>
        /// 批量上传文件
        /// </summary>
        /// <param name="clientFullNames">上传的文件客户端全路径</param>
        /// <param name="serverDirec">上传的目录</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult> UpLoadFilesWholeByFtp(List<string> clientFullNames, string serverDirec)
        {
            ServiceResult result = new ServiceResult();
            try
            {
                if (_ftpClient != null)
                {
                    if (!_ftpClient.IsConnected)
                    {
                        _ftpClient.Connect();
                    }
                    List<string> cilentnames = new List<string>();
                    foreach (var clientFullName in clientFullNames)
                    {
                        cilentnames.Add(GetAvalidatedFileName(clientFullName));
                    }
                    var ftpstatus = _ftpClient.UploadFiles(cilentnames, serverDirec, FtpRemoteExists.Overwrite, true);
                    if (ftpstatus.Exists(p => p.IsFailed))
                    {
                        result.Code = ServiceResultCode.Failed;
                        result.Message = "ftp上传文件失败";
                    }
                    else
                    {
                        result.Code = ServiceResultCode.Succeed;
                    }
                    return result;
                }
                result.Code = ServiceResultCode.Failed;
                result.Message = "ftp客户端对象不能为null";
            }
            catch (Exception ex)
            {
                result.Code = ServiceResultCode.Failed;
                result.Message = ex.ToString();
            }
            await Task.CompletedTask;
            return result;
        }
        /// <summary>
        /// ftp下载文件
        /// </summary>
        /// <param name="clientFullName">客户端全路径</param>
        /// <param name="serverFullName">服务器全路径</param>
        /// <returns></returns>
        public async Task<ServiceResult> DownLoadFileByFtp(string clientFullName, string serverFullName)
        {
            ServiceResult result = new ServiceResult();
            try
            {
                if (_ftpClient != null)
                {
                    if (!_ftpClient.IsConnected)
                    {
                        _ftpClient.Connect();
                    }
                    if (!this._windowspath)
                    {
                        serverFullName = serverFullName.Replace(@"\", @"/");
                    }
                    var ftpstatus = _ftpClient.DownloadFile(clientFullName, serverFullName);
                    if (ftpstatus == FtpStatus.Success)
                    {
                        result.Code = ServiceResultCode.Succeed;
                    }
                    else
                    {
                        result.Code = ServiceResultCode.Failed;
                        result.Message = "ftp上传文件失败";
                    }
                    return result;
                }
                result.Code = ServiceResultCode.Failed;
                result.Message = "ftp客户端对象不能为null";
            }
            catch (Exception ex)
            {
                result.Code = ServiceResultCode.Failed;
                result.Message = ex.ToString();
            }
            await Task.CompletedTask;
            return result;
        }

        /// <summary>
        /// ftp创建目录
        /// </summary>
        /// <param name="drecName">目录名称</param>
        /// <returns></returns>
        [ActionPrefix("write")]
        [HttpPost]
        public async Task<ServiceResult> CreateDirectoryByFtp(string drecName)
        {
            ServiceResult result = new ServiceResult();
            try
            {
                if (_ftpClient != null)
                {
                    if (!_ftpClient.IsConnected)
                    {
                        _ftpClient.Connect();
                    }
                    result.Code = ServiceResultCode.Succeed;
                    if (!_ftpClient.DirectoryExists(drecName))
                    {
                        result.Code = _ftpClient.CreateDirectory(drecName) ? ServiceResultCode.Succeed : ServiceResultCode.Failed;
                    }
                }
            }
            catch (Exception ex)
            {
                result.Code = ServiceResultCode.Failed;
                result.Message = ex.ToString();
            }
            await Task.CompletedTask;
            return result;
        }
        #endregion
    }
}
