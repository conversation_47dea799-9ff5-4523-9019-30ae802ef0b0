﻿/*****************************************************************************
*Copyright (c) 2021, 北京探索者软件股份有限公司
*All rights reserved.       
*文件名称: ImportFloderStructDto
*文件描述: 待填写描述
*创建者:    涂驰
*创建日期: 2021-12-29 17:29:22  
*版本号：******* 
*个人审查：待填写姓名-时间
*组长审查：待填写姓名-时间
*******************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto
{
    /// <summary>
    /// 返回给文件服务器的路径
    /// </summary>
    public class ZipFolderArchivesDto
    {
        /// <summary>
        /// 文件夹名称（短名称）
        /// </summary>
        public string FolderName { get; set; }
        /// <summary>
        /// 文件信息
        /// </summary>
        public List<ZipArchiveDto> Archives { get; set; }
        /// <summary>
        /// 子目录
        /// </summary>
        public List<ZipFolderArchivesDto> SubFolders { get; set; }
        /// <summary>
        /// 租户ID
        /// </summary>
        public Guid TenantId { get; set; }
        /// <summary>
        /// 生成的压缩包所在目录
        /// </summary>
        public string ZipTargetFolder { get; set; }
    }

    /// <summary>
    /// 文件下载信息
    /// </summary>
    public class ZipArchiveDto
    {        
        /// <summary>
        /// 文件名（真实的显示文件名，也是个短名称，仅仅是文件名称）
        /// </summary>
        public string ArchName { get; set; }
        /// <summary>
        /// 文件路径（相对路径，即后半部分完整文件路径，包含了文件名称）
        /// </summary>
        public string ServerSubFileName { get; set; }
    }
}
