using System;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 存储统计请求
    /// </summary>
    public class StorageStatisticsRequest
    {
        /// <summary>
        /// 存储桶名称
        /// </summary>
        public string BucketName { get; set; }

        /// <summary>
        /// 前缀过滤
        /// </summary>
        public string Prefix { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 是否包含详细信息
        /// </summary>
        public bool IncludeDetails { get; set; } = false;

        /// <summary>
        /// 是否按存储类别分组
        /// </summary>
        public bool GroupByStorageClass { get; set; } = false;
    }
}
