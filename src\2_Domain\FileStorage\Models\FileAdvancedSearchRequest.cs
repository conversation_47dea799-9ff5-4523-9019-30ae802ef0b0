using System;
using System.Collections.Generic;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件高级搜索请求
    /// 支持多条件组合查询，用于实现复杂的文件搜索功能
    /// </summary>
    public class FileAdvancedSearchRequest
    {
        /// <summary>
        /// 搜索关键词（搜索文件名、原始文件名）
        /// </summary>
        public string Keyword { get; set; }

        /// <summary>
        /// 文件ID列表（精确匹配）
        /// </summary>
        public List<string> FileIds { get; set; }

        /// <summary>
        /// MD5哈希值列表（精确匹配）
        /// </summary>
        public List<string> MD5Hashes { get; set; }

        /// <summary>
        /// 文件扩展名列表（不包含点号，如：jpg, pdf）
        /// </summary>
        public List<string> Extensions { get; set; }

        /// <summary>
        /// MIME类型列表
        /// </summary>
        public List<string> MimeTypes { get; set; }

        /// <summary>
        /// 存储类型列表
        /// </summary>
        public List<StorageType> StorageTypes { get; set; }

        /// <summary>
        /// 文件状态列表
        /// </summary>
        public List<FileStatus> Statuses { get; set; }

        /// <summary>
        /// 最小文件大小（字节）
        /// </summary>
        public long? MinFileSize { get; set; }

        /// <summary>
        /// 最大文件大小（字节）
        /// </summary>
        public long? MaxFileSize { get; set; }

        /// <summary>
        /// 创建时间范围开始
        /// </summary>
        public DateTime? CreatedAfter { get; set; }

        /// <summary>
        /// 创建时间范围结束
        /// </summary>
        public DateTime? CreatedBefore { get; set; }

        /// <summary>
        /// 修改时间范围开始
        /// </summary>
        public DateTime? ModifiedAfter { get; set; }

        /// <summary>
        /// 修改时间范围结束
        /// </summary>
        public DateTime? ModifiedBefore { get; set; }

        /// <summary>
        /// 是否包含已删除文件
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;

        /// <summary>
        /// 是否包含已归档文件
        /// </summary>
        public bool IncludeArchived { get; set; } = true;

        /// <summary>
        /// 是否只显示公开文件
        /// </summary>
        public bool? IsPublic { get; set; }

        /// <summary>
        /// 是否只显示加密文件
        /// </summary>
        public bool? IsEncrypted { get; set; }

        /// <summary>
        /// 最小访问次数
        /// </summary>
        public int? MinAccessCount { get; set; }

        /// <summary>
        /// 最大访问次数
        /// </summary>
        public int? MaxAccessCount { get; set; }

        /// <summary>
        /// 最小下载次数
        /// </summary>
        public int? MinDownloadCount { get; set; }

        /// <summary>
        /// 最大下载次数
        /// </summary>
        public int? MaxDownloadCount { get; set; }

        /// <summary>
        /// 标签搜索（支持多个标签）
        /// </summary>
        public List<string> Tags { get; set; }

        /// <summary>
        /// 路径前缀过滤（搜索指定目录下的文件）
        /// </summary>
        public string PathPrefix { get; set; }

        /// <summary>
        /// 跳过数量（分页）
        /// </summary>
        public int SkipCount { get; set; } = 0;

        /// <summary>
        /// 最大结果数量（分页）
        /// </summary>
        public int MaxResultCount { get; set; } = 10;

        /// <summary>
        /// 排序字段和方向
        /// 格式：字段名 [asc|desc]，如：CreationTime desc, FileSize asc
        /// </summary>
        public string Sorting { get; set; }

        /// <summary>
        /// 是否包含详细信息
        /// </summary>
        public bool IncludeDetails { get; set; } = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        public FileAdvancedSearchRequest()
        {
            FileIds = new List<string>();
            MD5Hashes = new List<string>();
            Extensions = new List<string>();
            MimeTypes = new List<string>();
            StorageTypes = new List<StorageType>();
            Statuses = new List<FileStatus>();
            Tags = new List<string>();
        }

        /// <summary>
        /// 验证搜索请求参数
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            // 验证分页参数
            if (SkipCount < 0)
            {
                result.AddError("跳过数量不能小于0");
            }

            if (MaxResultCount <= 0 || MaxResultCount > 1000)
            {
                result.AddError("最大结果数量必须在1-1000之间");
            }

            // 验证文件大小范围
            if (MinFileSize.HasValue && MinFileSize.Value < 0)
            {
                result.AddError("最小文件大小不能小于0");
            }

            if (MaxFileSize.HasValue && MaxFileSize.Value < 0)
            {
                result.AddError("最大文件大小不能小于0");
            }

            if (MinFileSize.HasValue && MaxFileSize.HasValue && MinFileSize.Value > MaxFileSize.Value)
            {
                result.AddError("最小文件大小不能大于最大文件大小");
            }

            // 验证时间范围
            if (CreatedAfter.HasValue && CreatedBefore.HasValue && CreatedAfter.Value > CreatedBefore.Value)
            {
                result.AddError("创建开始时间不能大于结束时间");
            }

            if (ModifiedAfter.HasValue && ModifiedBefore.HasValue && ModifiedAfter.Value > ModifiedBefore.Value)
            {
                result.AddError("修改开始时间不能大于结束时间");
            }

            // 验证访问次数范围
            if (MinAccessCount.HasValue && MinAccessCount.Value < 0)
            {
                result.AddError("最小访问次数不能小于0");
            }

            if (MaxAccessCount.HasValue && MaxAccessCount.Value < 0)
            {
                result.AddError("最大访问次数不能小于0");
            }

            if (MinAccessCount.HasValue && MaxAccessCount.HasValue && MinAccessCount.Value > MaxAccessCount.Value)
            {
                result.AddError("最小访问次数不能大于最大访问次数");
            }

            // 验证下载次数范围
            if (MinDownloadCount.HasValue && MinDownloadCount.Value < 0)
            {
                result.AddError("最小下载次数不能小于0");
            }

            if (MaxDownloadCount.HasValue && MaxDownloadCount.Value < 0)
            {
                result.AddError("最大下载次数不能小于0");
            }

            if (MinDownloadCount.HasValue && MaxDownloadCount.HasValue && MinDownloadCount.Value > MaxDownloadCount.Value)
            {
                result.AddError("最小下载次数不能大于最大下载次数");
            }

            return result;
        }

        /// <summary>
        /// 检查是否有任何搜索条件
        /// </summary>
        /// <returns>如果有搜索条件返回true，否则返回false</returns>
        public bool HasSearchCriteria()
        {
            return !string.IsNullOrWhiteSpace(Keyword) ||
                   (FileIds?.Count > 0) ||
                   (MD5Hashes?.Count > 0) ||
                   (Extensions?.Count > 0) ||
                   (MimeTypes?.Count > 0) ||
                   (StorageTypes?.Count > 0) ||
                   (Statuses?.Count > 0) ||
                   MinFileSize.HasValue ||
                   MaxFileSize.HasValue ||
                   CreatedAfter.HasValue ||
                   CreatedBefore.HasValue ||
                   ModifiedAfter.HasValue ||
                   ModifiedBefore.HasValue ||
                   IsPublic.HasValue ||
                   IsEncrypted.HasValue ||
                   MinAccessCount.HasValue ||
                   MaxAccessCount.HasValue ||
                   MinDownloadCount.HasValue ||
                   MaxDownloadCount.HasValue ||
                   (Tags?.Count > 0) ||
                   !string.IsNullOrWhiteSpace(PathPrefix);
        }
    }
}
