using System.IO;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 分片上传请求
    /// </summary>
    public class ChunkedUploadRequest
    {
        /// <summary>
        /// 上传ID（用于续传）
        /// </summary>
        public string UploadId { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 分片号（从1开始）
        /// </summary>
        public int ChunkNumber { get; set; }

        /// <summary>
        /// 分片数据流
        /// </summary>
        public Stream ChunkStream { get; set; }

        /// <summary>
        /// 分片大小
        /// </summary>
        public long ChunkSize { get; set; }

        /// <summary>
        /// 总文件大小
        /// </summary>
        public long TotalFileSize { get; set; }

        /// <summary>
        /// 总分片数
        /// </summary>
        public int TotalChunks { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 内容类型
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// 是否为最后一个分片
        /// </summary>
        public bool IsLastChunk { get; set; }
    }
}
