using System.Collections.Generic;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 预签名URL请求
    /// </summary>
    public class PresignedUrlRequest
    {
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public PresignedUrlOperation Operation { get; set; }

        /// <summary>
        /// 过期时间（秒）
        /// </summary>
        public int ExpirationSeconds { get; set; } = 3600;

        /// <summary>
        /// 内容类型
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// 响应头
        /// </summary>
        public Dictionary<string, string> ResponseHeaders { get; set; } = new Dictionary<string, string>();
    }
}
