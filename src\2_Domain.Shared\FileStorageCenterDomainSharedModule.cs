﻿using TSZ.ServiceBase.FileStorageCenter.Localization;
using Volo.Abp.AuditLogging;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using Volo.Abp.IdentityServer;
using Volo.Abp.Localization;
using Volo.Abp.Localization.ExceptionHandling;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.TenantManagement;
using Volo.Abp.Validation;
using Volo.Abp.VirtualFileSystem;

namespace TSZ.ServiceBase.FileStorageCenter
{
    //[DependsOn(
    //    typeof(AbpAuditLoggingDomainSharedModule),
    //    //typeof(AbpBackgroundJobsDomainSharedModule),
    //    typeof(AbpFeatureManagementDomainSharedModule),
    //    typeof(AbpIdentityDomainSharedModule),
    //    typeof(AbpIdentityServerDomainSharedModule),
    //    typeof(AbpPermissionManagementDomainSharedModule),
    //    typeof(AbpSettingManagementDomainSharedModule),
    //    typeof(AbpTenantManagementDomainSharedModule)
    //    )]
    public class FileStorageCenterDomainSharedModule : AbpModule
    {
        public override void PreConfigureServices(ServiceConfigurationContext context)
        {
            FileStorageCenterGlobalFeatureConfigurator.Configure();
            FileStorageCenterModuleExtensionConfigurator.Configure();
        }

        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            //Configure<AbpVirtualFileSystemOptions>(options =>
            //{
            //    options.FileSets.AddEmbedded<FileStorageCenterDomainSharedModule>();
            //});

            //Configure<AbpLocalizationOptions>(options =>
            //{
            //    options.Resources
            //        .Add<FileStorageCenterResource>("en")
            //        .AddBaseTypes(typeof(AbpValidationResource))
            //        .AddVirtualJson("/Localization/FileStorageCenter");

            //    options.DefaultResourceType = typeof(FileStorageCenterResource);
            //});

            //Configure<AbpExceptionLocalizationOptions>(options =>
            //{
            //    options.MapCodeNamespace("FileStorageCenter", typeof(FileStorageCenterResource));
            //});
        }
    }
}
