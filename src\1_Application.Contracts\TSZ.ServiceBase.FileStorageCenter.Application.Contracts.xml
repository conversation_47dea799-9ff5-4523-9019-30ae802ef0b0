<?xml version="1.0"?>
<doc>
    <assembly>
        <name>TSZ.ServiceBase.FileStorageCenter.Application.Contracts</name>
    </assembly>
    <members>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.AliyunOSS.IAliyunOSSService">
            <summary>
            STS
            </summary>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.AliyunOSS.IAliyunOSSService.GetSTSToken">
            <summary>
            获取STS
            </summary>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.AliyunOSS.IAliyunOSSService.DeleteObject(System.String,System.String)">
            <summary>
            删除文件信息（临时删除）
            </summary>
            <param name="bucketName">桶名</param>
            <param name="objectName">路径</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.AliyunOSS.IAliyunOSSService.DeleteObjectByVersion(System.String,System.String,System.String)">
            <summary>
            删除文件信息(永久删除)
            </summary>
            <param name="bucketName">桶名</param>
            <param name="objectName">路径</param>
            <param name="versionId">版本号</param>
            <returns></returns>
        </member>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterApplicationContractsModule">
            <summary>
            
            </summary>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterApplicationContractsModule.PreConfigureServices(Volo.Abp.Modularity.ServiceConfigurationContext)">
            <summary>
            
            </summary>
            <param name="context"></param>
        </member>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterDtoExtensions">
            <summary>
            
            </summary>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.FileStorageCenterDtoExtensions.Configure">
            <summary>
            
            </summary>
        </member>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.FileResultDto">
            <summary>
            Vue文件上传返回结果
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.FileResultDto.NeedMerge">
            <summary>
            是否需要合并
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.FileResultDto.Message">
            <summary>
            
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.FileResultDto.Extension">
            <summary>
            扩展名
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.FileResultDto.Uploaded">
            <summary>
            
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.FileResultDto.ServerId">
            <summary>
            服务器存贮路径ID
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.FileResultDto.MD5">
            <summary>
            
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.FileResultDto.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.FileResultDto.Alias">
            <summary>
            文件别名
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.FileResultDto.TotalSize">
            <summary>
            总分片
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.FileResultDto.FilePath">
            <summary>
            文件全路径 
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.FileResultDto.BucketName">
            <summary>
            子目录
            </summary>
        </member>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto.ZipFolderArchivesDto">
            <summary>
            返回给文件服务器的路径
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto.ZipFolderArchivesDto.FolderName">
            <summary>
            文件夹名称（短名称）
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto.ZipFolderArchivesDto.Archives">
            <summary>
            文件信息
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto.ZipFolderArchivesDto.SubFolders">
            <summary>
            子目录
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto.ZipFolderArchivesDto.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto.ZipFolderArchivesDto.ZipTargetFolder">
            <summary>
            生成的压缩包所在目录
            </summary>
        </member>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto.ZipArchiveDto">
            <summary>
            文件下载信息
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto.ZipArchiveDto.ArchName">
            <summary>
            文件名（真实的显示文件名，也是个短名称，仅仅是文件名称）
            </summary>
        </member>
        <member name="P:TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto.ZipArchiveDto.ServerSubFileName">
            <summary>
            文件路径（相对路径，即后半部分完整文件路径，包含了文件名称）
            </summary>
        </member>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService">
            <summary>
            
            </summary>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.UploadFile(System.String,System.Int32,System.Boolean)">
            <summary>
            上传文件，分割上传（也可以理解为分片上传，但是不需要合并，每次上传都是直接追加到文件的末尾，顺序一直上传，直到上传全部完成）
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名）</param>
            <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>    
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.UploadFileChunk(System.String,System.Boolean,System.String,System.Int32,System.Int64,System.Int32,System.Int64)">
            <summary>
            分片上传文件，不管分片数量多少，后续都必须调用FileMerge合并文件
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <param name="identifier">分片标识符</param>
            <param name="chunkNumber">分片号</param>
            <param name="chunkSize">分片大小</param>
            <param name="totalChunks">总分片数量，不管分片数量多少，后续都必须调用FileMerge合并文件</param>
            <param name="totalSize">文件总大小</param>
            <returns></returns>     
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.FileMerge(System.String,TSZ.ServiceBase.FileStorageCenter.FileUploadBehaviours,System.String,System.Int32)">
            <summary>
            合并分片
            </summary>
            <param name="strFullFileName">完成服务器文件名</param>
            <param name="fileUploadBehaviour"></param>
            <param name="identifier"></param>
            <param name="totalChunks"></param>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.DownloadFile(System.String,System.Int32,System.Boolean)">
            <summary>
            下载文件，分割下载，一次只下载 FileServiceConfig.MAXFILESIZE（默认为1MB）大小，顺序一直下载，直到下载全部完成
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名）</param>
            <param name="intReadCount">文件读取的次数（第几次，从0开始计数）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>     
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.DownloadFilePosition(System.String,System.Int64,System.Int32,System.Boolean)">
            <summary>
            下载文件，从指定节点
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名）</param>
            <param name="lPosition">读取文件的位置（文件从0开始的偏移量）</param>
            <param name="intReadSize">读取文件大小（一次读取的文件大小）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.OverrideFile(System.String,System.Int32,System.Boolean)">
            <summary>
            覆盖文件
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名）</param>
            <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.CreateSignatureFile(System.String,System.Int16,System.Boolean)">
            <summary>
            创建签名文件【分块】
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="intChunkSize">分块数量</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.CreateDeltaFile(System.String,System.String,System.Boolean)">
            <summary>
            创建Delta文件【分块】
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strSignatureFile">Delta完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.ApplyDeltaFileToCreateNewFile(System.String,System.String,System.String,System.Boolean)">
            <summary>
            从Delta文件创建新文件
            </summary>
            <param name="strReferenceServerFullFileName">参考的完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strDeltaFile">Delta完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.DownloadFileWhole(System.String,System.Boolean)">
            <summary>
            下载文件
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否是加密文件</param>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.DownloadFileWholePart(System.String,System.Int32,System.Boolean)">
            <summary>
            下载文件
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="intReadCount">文件读取的次数（第几次，从0开始计数）</param>
            <param name="isEncryptDecrypt">是否是加密文件</param>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.DownloadModelFileWhole(System.String,System.Boolean)">
            <summary>
            下载文件(发布目录下的模型等文件);（通用），在特定目录下（发布目录）去下载
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否是加密文件</param>
            <returns></returns>     
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.UploadFileWhole(System.String,System.Boolean)">
            <summary>
            上传文件（整个一起上传）
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否加密文件</param>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.UploadFileWholePart(System.String,System.Int32,System.Boolean)">
            <summary>
            上传文件，切割上传
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
            <param name="isEncryptDecrypt">是否加密文件</param>
            <returns></returns>     
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.UploadFileWholeEx(System.String,System.Boolean)">
            <summary>
            上传文件（整个一起上传）,文件压测接口，不实际读写文件，避免磁盘爆炸和大量垃圾
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否加密文件</param>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.UploadFileWholePartEx(System.String,System.Int32,System.Boolean)">
            <summary>
            上传文件，切割上传，文件压测接口，不实际读写文件，避免磁盘爆炸和大量垃圾
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="uploadMode">上传模式，为1时为第一次上传，若文件已经存在，自动创建新名称的文件，并返回新文件名（短名称）</param>
            <param name="isEncryptDecrypt">是否加密文件</param>
            <returns></returns>     
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.UploadFileChunks">
            <summary>
            GET请求，获取已经上传的分片信息（分片上传前返回已经上传的分片给前端）【vue前端专用】
            </summary>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.UploadFileChunks(System.Int32,System.Int64,System.Int64,System.String,System.Int32,System.String,System.String)">
            <summary>
            文件分片上传，后续需要调用FileMergeChunks合并分片（除非totalChunks=1则直接处理为直接上传，不需要FileMergeChunks合并）【vue前端专用】
            临时分片文件存储目录为：根目录+oneceGuid+identifier
            </summary>
            <param name="chunkNumber">分片编号</param>
            <param name="chunkSize">分片大小</param>
            <param name="totalSize">文件总大小</param>
            <param name="identifier">文件标识符，临时分片文件存储目录为：根目录+oneceGuid+identifier</param>      
            <param name="totalChunks">总分片数，大于1的总分片，后续需要调用FileMergeChunks合并分片，如果等于1，则变成直接上传</param>
            <param name="bucketName">模拟对象存储的bucketName，必填，可以为租户Id，可以是任意字符串，如tszobs、Guid等</param>
            <param name="objectName">模拟对象存储的objectName，可以是文件对象名称而且可以带子目录，如exampledir/exampleobject.txt</param>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.FileMergeChunks(System.String,System.Int32,System.String,System.String)">
            <summary>
            合并分片文件，完成上传（UploadFileChunks的后续方法）【vue前端专用】
            临时分片文件存储目录为：根目录+oneceGuid+identifier
            </summary>
            <param name="identifier">文件标识符，临时分片文件存储目录为：根目录+oneceGuid+identifier</param>       
            <param name="totalChunks">总分片数量</param>
            <param name="bucketName">模拟对象存储的bucketName，必填，可以为租户Id，可以是任意字符串，如tszobs、Guid等</param>
            <param name="objectName">模拟对象存储的objectName，可以是文件对象名称而且可以带子目录，如exampledir/exampleobject.txt</param>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.CanOpened">
            <summary>
            测试API，检查文件服务是否正常
            </summary>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.GetServerDateTime">
            <summary>
            获取服务器时间，返回标准时间
            </summary>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.ExistsFile(System.String)">
            <summary>
            判断文件是否存在
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.GetFileLength(System.String)">
            <summary>
            获取文件大小
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.GetFileInfo(System.String,System.Boolean)">
            <summary>
            获取文件信息
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.DeleteFile(System.String)">
            <summary>
            删除文件
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.DeleteDirectory(System.String)">
            <summary>
            删除文件夹
            </summary>
            <param name="strDirectoryName"></param>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.CopyFile(System.String,System.String)">
            <summary>
            复制文件
            </summary>
            <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.CopyFileOverwrite(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="strSrcFullFileName"></param>
            <param name="strTargetFullFileName"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.CopyFileEncryptToDecrypt(System.String,System.String)">
            <summary>
            解密文件
            </summary>
            <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.CopyFileDecryptToEncrypt(System.String,System.String)">
            <summary>
            加密文件
            </summary>
            <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetFullFileName">目的文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.ComputeFileMD5(System.String,System.Int64,System.Boolean)">
            <summary>
            获取文件MD5值
            </summary>
            <param name="strFullFileName">文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="lFileLength">文件长度</param>
            <param name="isEncryptDecrypt">是否加密</param>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.ReNameFile(System.String,System.String)">
            <summary>
            重命名文件
            </summary>
            <param name="strFullFileName">文件原始路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strNewFileName">文件新路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.MoveFile(System.String,System.String)">
            <summary>
            移动文件
            </summary>
            <param name="strSrcFullFileName">文件原始路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetFullFileName">文件目的路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.GetDirectoyChild(System.String)">
            <summary>
            获取文件夹文件
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.GetDirectoryFiles(System.String)">
            <summary>
            获取文件下面目录
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.MoveDirectoryFile(System.String,System.String)">
            <summary>
            移动文件夹下面的文件
            </summary>
            <param name="sourcePath"></param>
            <param name="targetPath"></param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.UnzipFile(System.String,System.String,System.Boolean)">
            <summary>
            解压指定文件到指定目录
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatas，则API会自动加上，只能在 /tszappdatas 根目录下解压到指定的子目录</param>
            <param name="isEncryptDecrypt">原始文件是否是加密的</param>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.GetZipFolderArchive(TSZ.ServiceBase.FileStorageCenter.FileStorageCenter.Dto.ZipFolderArchivesDto)">
            <summary>
            生成压缩包的方法(TSV5专用);-改为通用方法
            </summary>
            <param name="dto"></param>
            <returns></returns>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.UnzipFileWithJsonInfo(System.String,System.String,System.Boolean,System.String)">
            <summary>
            解压指定文件到指定目录并生成2d图纸信息json文件（BimBang专属接口，其它应用不能使用）
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下解压到指定的子目录</param>
            <param name="isEncryptDecrypt">原始文件是否是加密的</param>
            <param name="jsonFileName">生成json的文件名</param>
            <returns>true：有2d图纸，false：无2d图纸</returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.DeleteFileAndFolder(System.String,System.String)">
            <summary>
            删除指定文件并删除模型发布服务的指定目录（通用）
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下解压到指定的子目录</param>
            <returns></returns>      
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.UnzipModelFile(System.String,System.String,System.Boolean)">
            <summary>
            解压模型文件到模型web服务（通用）
            </summary>
            <param name="strFullFileName">完整服务器文件名（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下解压到指定的子目录</param>
            <param name="isEncryptDecrypt">原始文件是否是加密的</param>       
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.CopyModelFile(System.String,System.String)">
            <summary>
            复制模型文件到模型web服务（通用）
            </summary>
            <param name="strSrcFullFileName">源文件路径（短路径文件名，不包括服务器根目录，服务器根目录对客户端是隐藏的）</param>
            <param name="strTargetDirectory">目标文件夹，如果不带 /tszappdatasstatic，则API会自动加上，只能在 /tszappdatasstatic 根目录下复制到指定的子目录</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.UpLoadFileWholeByFtp(System.String,System.String)">
            <summary>
            单个传文件
            </summary>
            <param name="clientFullName">客户端的全路径</param>
            <param name="serverFullName">服务端的全路径</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.UpLoadFilesWholeByFtp(System.Collections.Generic.List{System.String},System.String)">
            <summary>
            批量上传文件
            </summary>
            <param name="clientFullNames">上传的文件客户端全路径</param>
            <param name="serverDirec">上传的目录</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.DownLoadFileByFtp(System.String,System.String)">
            <summary>
            ftp下载文件
            </summary>
            <param name="clientFullName">客户端全路径</param>
            <param name="serverFullName">服务器全路径</param>
            <returns></returns>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileStorageCenterAppService.CreateDirectoryByFtp(System.String)">
            <summary>
            ftp创建目录
            </summary>
            <param name="drecName">目录名称</param>
            <returns></returns>
        </member>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.IFileSynchronizerAppService">
            <summary>
            文件同步服务
            </summary>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.IFileSynchronizerAppService.DownloadAsync(System.String,System.String)">
            <summary>
            从相同架构文件服务同步下载文件
            </summary>
            <param name="strFullFileName"></param>
            <param name="tenantId"></param>
            <returns></returns>
        </member>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.Permissions.FileStorageCenterPermissionDefinitionProvider">
            <summary>
            
            </summary>
        </member>
        <member name="M:TSZ.ServiceBase.FileStorageCenter.Permissions.FileStorageCenterPermissionDefinitionProvider.Define(Volo.Abp.Authorization.Permissions.IPermissionDefinitionContext)">
            <summary>
            
            </summary>
            <param name="context"></param>
        </member>
        <member name="T:TSZ.ServiceBase.FileStorageCenter.Permissions.FileStorageCenterPermissions">
            <summary>
            
            </summary>
        </member>
        <member name="F:TSZ.ServiceBase.FileStorageCenter.Permissions.FileStorageCenterPermissions.GroupName">
            <summary>
            
            </summary>
        </member>
    </members>
</doc>
