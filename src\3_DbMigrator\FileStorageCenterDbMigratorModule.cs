﻿using TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore;
using Volo.Abp.Autofac;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Modularity;

namespace TSZ.ServiceBase.FileStorageCenter.DbMigrator
{
    [DependsOn(
        typeof(AbpAutofacModule),
        typeof(FileStorageCenterEntityFrameworkCoreDbMigrationsModule),
        typeof(FileStorageCenterApplicationContractsModule)
        )]
    public class FileStorageCenterDbMigratorModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            Configure<AbpBackgroundJobOptions>(options => options.IsJobExecutionEnabled = false);
        }
    }
}
