{"format": 1, "restore": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj": {}}, "projects": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "projectName": "TSZ.ServiceBase.FileStorageCenter.HttpApi", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\TSZ.ServiceBase.FileStorageCenter.HttpApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\0_HttpApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Volo.Abp.Account.HttpApi": {"target": "Package", "version": "(, )"}, "Volo.Abp.FeatureManagement.HttpApi": {"target": "Package", "version": "(, )"}, "Volo.Abp.Identity.HttpApi": {"target": "Package", "version": "(, )"}, "Volo.Abp.PermissionManagement.HttpApi": {"target": "Package", "version": "(, )"}, "Volo.Abp.SettingManagement.HttpApi": {"target": "Package", "version": "(, )"}, "Volo.Abp.TenantManagement.HttpApi": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj", "projectName": "TSZ.ServiceBase.FileStorageCenter.Application.Contracts", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\1_Application.Contracts\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj": {"projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AlibabaCloud.SDK.Sts20150401": {"target": "Package", "version": "(, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "(, )"}, "Volo.Abp.Account.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.FeatureManagement.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.Identity.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.ObjectExtending": {"target": "Package", "version": "(, )"}, "Volo.Abp.PermissionManagement.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.SettingManagement.Application.Contracts": {"target": "Package", "version": "(, )"}, "Volo.Abp.TenantManagement.Application.Contracts": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj", "projectName": "TSZ.ServiceBase.FileStorageCenter.Domain.Shared", "projectPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\TSZ.ServiceBase.FileStorageCenter.Domain.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\一体化平台优化\\0-filestorage\\TSZ.ServiceBase.FileStorageCenter3\\src\\2_Domain.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "(, )"}, "Volo.Abp.AuditLogging.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.BackgroundJobs.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.FeatureManagement.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.Identity.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.IdentityServer.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.Localization": {"target": "Package", "version": "(, )"}, "Volo.Abp.ObjectExtending": {"target": "Package", "version": "(, )"}, "Volo.Abp.PermissionManagement.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.SettingManagement.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.TenantManagement.Domain.Shared": {"target": "Package", "version": "(, )"}, "Volo.Abp.Validation": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}