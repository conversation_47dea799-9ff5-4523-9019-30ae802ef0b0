using System.Collections.Generic;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 存储配置
    /// </summary>
    public class StorageConfiguration
    {
        /// <summary>
        /// 配置名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 存储类型
        /// </summary>
        public StorageType StorageType { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否为默认存储
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 优先级（数字越小优先级越高）
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 连接字符串或配置参数
        /// </summary>
        public Dictionary<string, string> ConnectionParameters { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 扩展配置
        /// </summary>
        public Dictionary<string, object> ExtendedProperties { get; set; } = new Dictionary<string, object>();
    }
}
