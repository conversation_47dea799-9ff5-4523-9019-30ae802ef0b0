﻿using System;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Serilog;
using TSZ.Common.Shared;

namespace TSZ.ServiceBase.FileStorageCenter
{
    public class Program
    {
        public static int Main(string[] args)
        {
            System.Runtime.Loader.AssemblyLoadContext.Default.Resolving += TSZ.Common.Shared.AppBaseInfo.Default_Resolving;
            TSZ.Common.Core.Helper.TszSkyApmHostingStartup.AddSkyApmHostingStartupEnvironmentVariable();
            Log.Logger = new LoggerConfiguration().WriteTo.Async(t => t.Console()).CreateLogger();
            var serviceHostName = Assembly.GetEntryAssembly().GetName().Name;
            try
            {
                Console.WriteLine($"[{PubMethod.GetCurrentDateTime()}] Starting {serviceHostName}.");
                CreateHostBuilder(args).Build().Run();
                return 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{PubMethod.GetCurrentDateTime()}] Host {serviceHostName} terminated unexpectedly!\r\n" + ex.ToString());
                return 1;
            }
        }

        internal static IHostBuilder CreateHostBuilder(string[] args)
        {
            //******以下为在Linux容器中运行******
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                return Host.CreateDefaultBuilder(args)
                    //.AddTszApollo(true)
                    .AddTszSeriLog()
                    .ConfigureWebHostDefaults(webBuilder =>
                    {
                        var configuration = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("host.json", optional: true).Build();
                        var strUrls = configuration["urls"];
                        webBuilder.UseContentRoot(Environment.CurrentDirectory)
                          .UseKestrel(opt =>
                          {
                              opt.Limits.MinRequestBodyDataRate = null;
                              opt.Limits.KeepAliveTimeout = TimeSpan.FromSeconds(60 * 3);
                              opt.Limits.RequestHeadersTimeout = TimeSpan.FromSeconds(60 * 1);
                          })
                          .UseStartup<Startup>()
                          .UseUrls(strUrls);
                        Console.WriteLine($"[{PubMethod.GetCurrentDateTime()}] WebHost.UseUrls({strUrls})");
                    })
                     .ConfigureAppConfiguration((context, builder) =>
                     {
                         //配置Nacos-add by lijie 2025-06-06
                         var c = builder.Build();
                         //json 解析器
                         builder.AddNacosV2Configuration(c.GetSection("NacosConfig"));
                         //ini 解析器
                         //builder.AddNacosV2Configuration(c.GetSection("NacosConfig"), parser: Nacos.IniParser.IniConfigurationStringParser.Instance);
                         //yarm解析器
                         //builder.AddNacosV2Configuration(c.GetSection("NacosConfig"), parser: Nacos.YamlParser.YamlConfigurationStringParser.Instance);
                     })
                    .UseAutofac();
            }
            else
            {
                var isAspNetCoreUseSelfPort = Environment.GetEnvironmentVariable("ASPNETCOREUSESELFPORT");
                string strUrls = PubMethod.GetAllBindUrls();
                if (isAspNetCoreUseSelfPort == "true")
                {
                    var configuration = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("host.json", optional: true).Build();
                    strUrls = configuration["urls"];
                }
                Console.WriteLine($"[{PubMethod.GetCurrentDateTime()}] WebHost.UseUrls({strUrls})");
                return Host.CreateDefaultBuilder(args)
                    //.AddTszApollo(true)
                    .AddTszSeriLog()
                    .ConfigureWebHostDefaults(webBuilder =>
                    {
                        webBuilder.UseContentRoot(Environment.CurrentDirectory)
                          .UseKestrel(opt =>
                          {
                              opt.Limits.MinRequestBodyDataRate = null;
                              opt.Limits.KeepAliveTimeout = TimeSpan.FromSeconds(60 * 3);
                              opt.Limits.RequestHeadersTimeout = TimeSpan.FromSeconds(60 * 1);
                          })
                          .UseStartup<Startup>()
                          .UseUrls(strUrls);
                    })
                     .ConfigureAppConfiguration((context, builder) =>
                     {
                         //配置Nacos-add by lijie 2025-06-06
                         var c = builder.Build();
                         //json 解析器
                         builder.AddNacosV2Configuration(c.GetSection("NacosConfig"));
                         //ini 解析器
                         //builder.AddNacosV2Configuration(c.GetSection("NacosConfig"), parser: Nacos.IniParser.IniConfigurationStringParser.Instance);
                         //yarm解析器
                         //builder.AddNacosV2Configuration(c.GetSection("NacosConfig"), parser: Nacos.YamlParser.YamlConfigurationStringParser.Instance);
                     })
                    .UseAutofac();
            }
        }
    }
}
