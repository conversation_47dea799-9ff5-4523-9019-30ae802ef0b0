using System.Collections.Generic;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 批量删除请求
    /// </summary>
    public class BatchDeleteRequest
    {
        /// <summary>
        /// 文件路径列表
        /// </summary>
        public List<string> FilePaths { get; set; } = new List<string>();

        /// <summary>
        /// 是否永久删除
        /// </summary>
        public bool PermanentDelete { get; set; }
    }
}
