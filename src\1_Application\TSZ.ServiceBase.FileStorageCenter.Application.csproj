﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<RootNamespace>TSZ.ServiceBase.FileStorageCenter</RootNamespace>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>TSZ.ServiceBase.FileStorageCenter.Application.xml</DocumentationFile>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="FluentFTP"  />
		<PackageReference Include="Volo.Abp.Account.Application"  />
		<PackageReference Include="Volo.Abp.Identity.Application"  />
		<PackageReference Include="Volo.Abp.PermissionManagement.Application"  />
		<PackageReference Include="Volo.Abp.TenantManagement.Application"  />
		<PackageReference Include="Volo.Abp.FeatureManagement.Application"  />
		<PackageReference Include="Volo.Abp.SettingManagement.Application"  />
		<PackageReference Include="AlibabaCloud.SDK.Sts20150401"  />
		<PackageReference Include="Aliyun.OSS.SDK.NetCore"  />
		<PackageReference Include="AWSSDK.S3" Version="3.7.307.25" />
		<PackageReference Include="Minio" Version="6.0.3" />

		
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\1_Application.Contracts\TSZ.ServiceBase.FileStorageCenter.Application.Contracts.csproj" />
		<ProjectReference Include="..\2_Domain\TSZ.ServiceBase.FileStorageCenter.Domain.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Update="TSZ.ServiceBase.FileStorageCenter.Application.xml">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
