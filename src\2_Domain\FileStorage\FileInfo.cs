using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;

namespace TSZ.ServiceBase.FileStorageCenter.FileStorage
{
    /// <summary>
    /// 文件信息实体
    /// 用于存储文件的元数据信息，支持多种存储后端和文件快传功能
    /// </summary>
    public class FileInfo : FullAuditedAggregateRoot<Guid>, IMultiTenant
    {
        /// <summary>
        /// 租户ID
        /// </summary>
        public virtual Guid? TenantId { get; set; }

        /// <summary>
        /// 文件ID（业务主键，用于外部访问）
        /// 格式：32位十六进制字符串，基于文件内容和路径生成
        /// </summary>
        [Required]
        [StringLength(32)]
        public virtual string FileId { get; set; }

        /// <summary>
        /// 文件名（不包含路径）
        /// </summary>
        [Required]
        [StringLength(255)]
        public virtual string FileName { get; set; }

        /// <summary>
        /// 文件原始名称（用户上传时的文件名）
        /// </summary>
        [StringLength(255)]
        public virtual string OriginalFileName { get; set; }

        /// <summary>
        /// 文件相对路径（相对于存储根目录）
        /// 使用标准化的路径分隔符（/）
        /// </summary>
        [Required]
        [StringLength(1000)]
        public virtual string RelativePath { get; set; }

        /// <summary>
        /// 文件绝对路径（完整的物理路径）
        /// </summary>
        [StringLength(2000)]
        public virtual string AbsolutePath { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public virtual long FileSize { get; set; }

        /// <summary>
        /// 文件MD5哈希值（用于文件快传和完整性校验）
        /// </summary>
        [StringLength(32)]
        public virtual string MD5Hash { get; set; }

        /// <summary>
        /// 文件SHA256哈希值（用于安全校验）
        /// </summary>
        [StringLength(64)]
        public virtual string SHA256Hash { get; set; }

        /// <summary>
        /// 文件MIME类型
        /// </summary>
        [StringLength(100)]
        public virtual string MimeType { get; set; }

        /// <summary>
        /// 文件扩展名（包含点号，如：.jpg）
        /// </summary>
        [StringLength(20)]
        public virtual string FileExtension { get; set; }

        /// <summary>
        /// 存储类型
        /// </summary>
        public virtual StorageType StorageType { get; set; }

        /// <summary>
        /// 存储配置名称（对应配置文件中的存储配置）
        /// </summary>
        [StringLength(100)]
        public virtual string StorageConfig { get; set; }

        /// <summary>
        /// 文件状态
        /// </summary>
        public virtual FileStatus Status { get; set; }

        /// <summary>
        /// 引用计数（用于文件快传，记录有多少个逻辑文件引用此物理文件）
        /// </summary>
        public virtual int ReferenceCount { get; set; }

        /// <summary>
        /// 是否加密存储
        /// </summary>
        public virtual bool IsEncrypted { get; set; }

        /// <summary>
        /// 加密密钥（如果加密存储）
        /// </summary>
        [StringLength(256)]
        public virtual string EncryptionKey { get; set; }

        /// <summary>
        /// 是否公开访问
        /// </summary>
        public virtual bool IsPublic { get; set; }

        /// <summary>
        /// 访问次数统计
        /// </summary>
        public virtual long AccessCount { get; set; }

        /// <summary>
        /// 最后访问时间
        /// </summary>
        public virtual DateTime? LastAccessTime { get; set; }

        /// <summary>
        /// 下载次数统计
        /// </summary>
        public virtual long DownloadCount { get; set; }

        /// <summary>
        /// 最后下载时间
        /// </summary>
        public virtual DateTime? LastDownloadTime { get; set; }

        /// <summary>
        /// 文件过期时间（可选，用于临时文件）
        /// </summary>
        public virtual DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 文件标签（JSON格式，用于分类和搜索）
        /// </summary>
        [StringLength(1000)]
        public virtual string Tags { get; set; }

        /// <summary>
        /// 扩展属性（JSON格式，用于存储自定义属性）
        /// </summary>
        [StringLength(2000)]
        public new virtual string ExtraProperties { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(500)]
        public virtual string Remarks { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        protected FileInfo()
        {
        }

        /// <summary>
        /// 公共构造函数
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="fileName">文件名</param>
        /// <param name="relativePath">相对路径</param>
        public FileInfo(string fileId, string fileName, string relativePath)
        {
            FileId = fileId;
            FileName = fileName;
            RelativePath = relativePath;
            Status = FileStatus.Active;
            StorageType = StorageType.Local;
            CreationTime = DateTime.UtcNow;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="id">实体ID</param>
        /// <param name="fileId">文件ID</param>
        /// <param name="fileName">文件名</param>
        /// <param name="relativePath">相对路径</param>
        /// <param name="fileSize">文件大小</param>
        /// <param name="storageType">存储类型</param>
        /// <param name="tenantId">租户ID</param>
        public FileInfo(
            Guid id,
            string fileId,
            string fileName,
            string relativePath,
            long fileSize,
            StorageType storageType,
            Guid? tenantId = null)
        {
            Id = id;
            FileId = fileId;
            FileName = fileName;
            RelativePath = relativePath;
            FileSize = fileSize;
            StorageType = storageType;
            TenantId = tenantId;
            Status = FileStatus.Active;
            ReferenceCount = 1;
            IsEncrypted = false;
            IsPublic = false;
            AccessCount = 0;
            DownloadCount = 0;
        }

        /// <summary>
        /// 增加引用计数
        /// </summary>
        public virtual void IncreaseReferenceCount()
        {
            ReferenceCount++;
        }

        /// <summary>
        /// 减少引用计数
        /// </summary>
        public virtual void DecreaseReferenceCount()
        {
            if (ReferenceCount > 0)
            {
                ReferenceCount--;
            }
        }

        /// <summary>
        /// 记录访问
        /// </summary>
        public virtual void RecordAccess()
        {
            AccessCount++;
            LastAccessTime = DateTime.UtcNow;
        }

        /// <summary>
        /// 记录下载
        /// </summary>
        public virtual void RecordDownload()
        {
            DownloadCount++;
            LastDownloadTime = DateTime.UtcNow;
            RecordAccess(); // 下载也算作访问
        }

        /// <summary>
        /// 标记为删除状态
        /// </summary>
        public virtual void MarkAsDeleted()
        {
            Status = FileStatus.Deleted;
        }

        /// <summary>
        /// 标记为归档状态
        /// </summary>
        public virtual void MarkAsArchived()
        {
            Status = FileStatus.Archived;
        }

        /// <summary>
        /// 检查文件是否过期
        /// </summary>
        /// <returns></returns>
        public virtual bool IsExpired()
        {
            return ExpirationTime.HasValue && ExpirationTime.Value <= DateTime.UtcNow;
        }
    }


}
