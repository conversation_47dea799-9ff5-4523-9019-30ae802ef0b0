﻿using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using TSZ.ServiceBase.FileStorageCenter.Data;
using Volo.Abp.DependencyInjection;

namespace TSZ.ServiceBase.FileStorageCenter.EntityFrameworkCore
{
    public class EntityFrameworkCoreFileStorageCenterDbSchemaMigrator
        : IFileStorageCenterDbSchemaMigrator, ITransientDependency
    {
        private readonly IServiceProvider _serviceProvider;

        public EntityFrameworkCoreFileStorageCenterDbSchemaMigrator(
            IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task MigrateAsync()
        {
            /* We intentionally resolving the FileStorageCenterMigrationsDbContext
             * from IServiceProvider (instead of directly injecting it)
             * to properly get the connection string of the current tenant in the
             * current scope.
             */

            await _serviceProvider
                .GetRequiredService<FileStorageCenterMigrationsDbContext>()
                .Database
                .MigrateAsync();
        }
    }
}